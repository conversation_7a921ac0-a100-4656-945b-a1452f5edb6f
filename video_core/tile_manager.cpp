// FileName: MultipleFiles/tile_manager.cpp
// FileContents:
#include "tile_manager.h"
#include "command_processor.h"
#include "gnm_state.h"
#include <algorithm>
#include <chrono>
#include <cmath>
#include <cstring>
#include <shared_mutex>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <vector>

namespace ps4 {

void TileInfo::Serialize(std::ostream &out) const {
  out.write(reinterpret_cast<const char *>(&width), sizeof(width));
  out.write(reinterpret_cast<const char *>(&height), sizeof(height));
  out.write(reinterpret_cast<const char *>(&depth), sizeof(depth));
  out.write(reinterpret_cast<const char *>(&mode), sizeof(mode));
  out.write(reinterpret_cast<const char *>(&format), sizeof(format));
  out.write(reinterpret_cast<const char *>(&bytesPerPixel),
            sizeof(bytesPerPixel));
  out.write(reinterpret_cast<const char *>(&pitch), sizeof(pitch));
  out.write(reinterpret_cast<const char *>(&slice), sizeof(slice));
  out.write(reinterpret_cast<const char *>(&gpuAddr), sizeof(gpuAddr));
  out.write(reinterpret_cast<const char *>(&cpuAddr), sizeof(cpuAddr));
  out.write(reinterpret_cast<const char *>(&isRenderTarget),
            sizeof(isRenderTarget));
  out.write(reinterpret_cast<const char *>(&isDepthStencil),
            sizeof(isDepthStencil));
  out.write(reinterpret_cast<const char *>(&cacheHits), sizeof(cacheHits));
  out.write(reinterpret_cast<const char *>(&cacheMisses), sizeof(cacheMisses));
}

void TileInfo::Deserialize(std::istream &in) {
  in.read(reinterpret_cast<char *>(&width), sizeof(width));
  in.read(reinterpret_cast<char *>(&height), sizeof(height));
  in.read(reinterpret_cast<char *>(&depth), sizeof(depth));
  in.read(reinterpret_cast<char *>(&mode), sizeof(mode));
  in.read(reinterpret_cast<char *>(&format), sizeof(format));
  in.read(reinterpret_cast<char *>(&bytesPerPixel), sizeof(bytesPerPixel));
  in.read(reinterpret_cast<char *>(&pitch), sizeof(pitch));
  in.read(reinterpret_cast<char *>(&slice), sizeof(slice));
  in.read(reinterpret_cast<char *>(&gpuAddr), sizeof(gpuAddr));
  in.read(reinterpret_cast<char *>(&cpuAddr), sizeof(cpuAddr));
  in.read(reinterpret_cast<char *>(&isRenderTarget), sizeof(isRenderTarget));
  in.read(reinterpret_cast<char *>(&isDepthStencil), sizeof(isDepthStencil));
  in.read(reinterpret_cast<char *>(&cacheHits), sizeof(cacheHits));
  in.read(reinterpret_cast<char *>(&cacheMisses), sizeof(cacheMisses));
  if (!in.good()) {
    throw TileManagerException("Failed to deserialize TileInfo");
  }
}

void TileManager::TiledSurface::Serialize(std::ostream &out) const {
  info.Serialize(out);
  uint64_t dataSize = data.size();
  out.write(reinterpret_cast<const char *>(&dataSize), sizeof(dataSize));
  out.write(reinterpret_cast<const char *>(data.data()), dataSize);
  out.write(reinterpret_cast<const char *>(&active), sizeof(active));
  out.write(reinterpret_cast<const char *>(&cacheHits), sizeof(cacheHits));
  out.write(reinterpret_cast<const char *>(&cacheMisses), sizeof(cacheMisses));
}

void TileManager::TiledSurface::Deserialize(std::istream &in) {
  info.Deserialize(in);
  uint64_t dataSize;
  in.read(reinterpret_cast<char *>(&dataSize), sizeof(dataSize));
  if (!in.good() ||
      dataSize > 1024 * 1024 * 1024) { // Sanity check: max 1GB per surface
    throw TileManagerException(
        "Invalid surface data size during deserialization");
  }

  data.resize(dataSize);
  if (dataSize > 0) {
    in.read(reinterpret_cast<char *>(data.data()), dataSize);
    if (!in.good()) {
      throw TileManagerException(
          "Failed to read surface data during deserialization");
    }
  }

  in.read(reinterpret_cast<char *>(&active), sizeof(active));
  in.read(reinterpret_cast<char *>(&cacheHits), sizeof(cacheHits));
  in.read(reinterpret_cast<char *>(&cacheMisses), sizeof(cacheMisses));
  if (!in.good()) {
    throw TileManagerException("Failed to deserialize TiledSurface");
  }
}

TileManager::TileManager(GNMRegisterState &gnmState,
                         CommandProcessor &commandProcessor)
    : m_gnmState(gnmState), m_commandProcessor(commandProcessor) {
  spdlog::info("TileManager created");
}

TileManager::~TileManager() {
  Shutdown();
  spdlog::info("TileManager destroyed");
}

bool TileManager::Initialize() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    m_surfaces.clear();
    m_surfaceCache.clear();
    m_nextSurfaceId = 1;
    m_currentSurfaceId = 0;
    m_currentTileX = 0;
    m_currentTileY = 0;
    m_tilesX = 0;
    m_tilesY = 0;
    m_stats = TileManagerStats();
    m_tiles.clear(); // Clear internal tile storage
    m_tile_cache.clear();
    m_active_tiles.clear();

    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++; // Consider initialization a "hit" for overall stats
    spdlog::info("TileManager initialized, latency={}us", latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++; // Consider initialization a "miss" if it fails
    spdlog::error("TileManager initialization failed: {}", e.what());
    throw TileManagerException("Initialization failed: " +
                               std::string(e.what()));
  }
}

void TileManager::Shutdown() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    m_surfaces.clear();
    m_surfaceCache.clear();
    m_nextSurfaceId = 1;
    m_currentSurfaceId = 0;
    m_currentTileX = 0;
    m_currentTileY = 0;
    m_tilesX = 0;
    m_tilesY = 0;
    m_stats = TileManagerStats(); // Reset stats on shutdown
    m_tiles.clear();              // Clear internal tile storage
    m_tile_cache.clear();
    m_active_tiles.clear();

    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++; // Consider shutdown a "hit" for overall stats
    spdlog::info("TileManager shutdown, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++; // Consider shutdown a "miss" if it fails
    spdlog::error("TileManager shutdown failed: {}", e.what());
  }
}

uint32_t TileManager::GetBytesPerPixelInternal(TileFormat format) {
  // Internal helper method without locking - used when mutex is already held
  switch (format) {
  case TileFormat::R8_UNORM:
    return 1;
  case TileFormat::R8G8_UNORM:
  case TileFormat::D16_UNORM:
  case TileFormat::R16_FLOAT:
    return 2;
  case TileFormat::R8G8B8A8_UNORM:
  case TileFormat::B8G8R8A8_UNORM:
  case TileFormat::R16G16_FLOAT:
  case TileFormat::D24_UNORM_S8_UINT:
  case TileFormat::D32_FLOAT:
  case TileFormat::R32_FLOAT:
    return 4;
  case TileFormat::R16G16B16A16_FLOAT:
  case TileFormat::R32G32_FLOAT:
    return 8;
  case TileFormat::R32G32B32A32_FLOAT:
    return 16;
  default:
    spdlog::error("GetBytesPerPixelInternal: Invalid format {}",
                  static_cast<int>(format));
    throw TileManagerException("Invalid tile format");
  }
}

uint32_t TileManager::GetBytesPerPixel(TileFormat format) {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    uint32_t bytesPerPixel = GetBytesPerPixelInternal(format);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetBytesPerPixel: format={}, bytes={}, latency={}us",
                  static_cast<int>(format), bytesPerPixel, latency);
    return bytesPerPixel;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetBytesPerPixel failed: {}", e.what());
    throw TileManagerException("GetBytesPerPixel failed: " +
                               std::string(e.what()));
  }
}

void TileManager::CalculateTileLayout(TileInfo &info) {
  // No lock here, assumes caller holds it.
  info.bytesPerPixel = GetBytesPerPixelInternal(info.format);
  uint32_t width_in_tiles = (info.width + TILE_WIDTH - 1) / TILE_WIDTH;
  uint32_t height_in_tiles = (info.height + TILE_HEIGHT - 1) / TILE_HEIGHT;

  switch (info.mode) {
  case TileMode::LINEAR:
    info.pitch = info.width * info.bytesPerPixel;
    info.slice = info.pitch * info.height;
    break;
  case TileMode::TILED_1D:
    // For 1D, pitch is typically aligned to a tile width, but height is linear
    info.pitch = width_in_tiles * TILE_WIDTH * info.bytesPerPixel;
    info.slice = info.pitch * info.height;
    break;
  case TileMode::TILED_2D:
  case TileMode::TILED_2B:
  case TileMode::TILED_3D:
  case TileMode::TILED_DEPTH:
    // For 2D/3D/Depth, pitch and slice are aligned to tile dimensions
    info.pitch = width_in_tiles * TILE_WIDTH * info.bytesPerPixel;
    info.slice = info.pitch * height_in_tiles * TILE_HEIGHT;
    break;
  default:
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("CalculateTileLayout: Unsupported tile mode {}",
                  static_cast<int>(info.mode));
    throw TileManagerException("Unsupported tile mode");
  }
}

uint64_t TileManager::CreateTiledSurface(uint32_t width, uint32_t height,
                                         uint32_t depth, TileMode mode,
                                         TileFormat format, bool isRenderTarget,
                                         bool isDepthStencil) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    if (width == 0 || height == 0 || depth == 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("CreateTiledSurface: Invalid dimensions: {}x{}x{}", width,
                    height, depth);
      throw TileManagerException("Invalid surface dimensions");
    }
    if (format == TileFormat::INVALID) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("CreateTiledSurface: Invalid format");
      throw TileManagerException("Invalid surface format");
    }

    uint64_t surfaceId = m_nextSurfaceId++;
    // Bounds check: Ensure surfaceId is reasonable to prevent excessive memory usage
    if (surfaceId > 1000000) { // Reasonable limit for surface IDs
      spdlog::error("CreateTiledSurface: Surface ID {} exceeds reasonable limit", surfaceId);
      throw TileManagerException("Surface ID exceeds reasonable limit");
    }
    TiledSurface &surface = m_surfaces[surfaceId];
    surface.active = true;
    surface.info.width = width;
    surface.info.height = height;
    surface.info.depth = depth;
    surface.info.mode = mode;
    surface.info.format = format;
    surface.info.isRenderTarget = isRenderTarget;
    surface.info.isDepthStencil = isDepthStencil;
    // Assign dummy GPU/CPU addresses for now, could be more sophisticated
    surface.info.gpuAddr = 0x80000000ULL | (surfaceId << 32);
    surface.info.cpuAddr = 0x40000000ULL | (surfaceId << 32);
    surface.info.cacheHits = 0;
    surface.info.cacheMisses = 0;
    surface.cacheHits = 0;
    surface.cacheMisses = 0;

    CalculateTileLayout(surface.info);
    size_t totalSize = surface.info.slice * depth;
    surface.data.resize(totalSize, 0);

    if (isRenderTarget) {
      m_currentSurfaceId = surfaceId;
      m_currentTileX = 0;
      m_currentTileY = 0;
      m_tilesX = (width + TILE_WIDTH - 1) / TILE_WIDTH;
      m_tilesY = (height + TILE_HEIGHT - 1) / TILE_HEIGHT;
    }

    // Cache surface data
    // Note: The cache entry itself also has cacheHits/Misses, which might be
    // redundant with TileInfo's. For simplicity, we'll update both.
    SurfaceCacheEntry cacheEntry{surfaceId, surface.data, 0, 0};
    m_surfaceCache[surfaceId] = cacheEntry;
    surface.info.cacheHits++; // This surface's info was accessed
    surface.cacheHits++;      // This surface's data was accessed
    m_stats.operationCount++;
    m_stats.cacheHits++; // Overall manager cache hit for this operation

    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("CreateTiledSurface: Created surface {}: {}x{}x{}, mode={}, "
                 "format={}, size={} bytes, latency={}us",
                 surfaceId, width, height, depth, static_cast<int>(mode),
                 static_cast<int>(format), totalSize, latency);

    // Unlock before notifying to avoid potential deadlocks if CommandProcessor
    // tries to acquire the same lock.
    lock.unlock();
    m_commandProcessor.NotifySurfaceCreated(surfaceId, width, height,
                                            static_cast<uint32_t>(format));
    // Re-lock if further operations on m_surfaces are needed, though not here.
    // lock.lock();

    return surfaceId;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("CreateTiledSurface failed: {}", e.what());
    throw TileManagerException("CreateTiledSurface failed: " +
                               std::string(e.what()));
  }
}

void TileManager::DestroyTiledSurface(uint64_t surfaceId) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("DestroyTiledSurface: Invalid surface ID: {}", surfaceId);
      throw TileManagerException("Invalid surface ID");
    }

    // Mark as inactive and clear data to free memory
    it->second.active = false;
    it->second.data.clear();
    it->second.info.cacheHits++; // Info accessed for destruction
    it->second.cacheHits++;      // Surface data implicitly handled
    m_surfaceCache.erase(surfaceId); // Remove from cache

    if (m_currentSurfaceId == surfaceId) {
      m_currentSurfaceId = 0; // No active surface
      m_currentTileX = 0;
      m_currentTileY = 0;
      m_tilesX = 0;
      m_tilesY = 0;
    }

    m_stats.operationCount++;
    m_stats.cacheHits++; // Overall manager cache hit for this operation
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("DestroyTiledSurface: Destroyed surface {}, latency={}us",
                 surfaceId, latency);

    lock.unlock();
    m_commandProcessor.NotifySurfaceDestroyed(surfaceId);
    // Re-lock if needed, but not here.
    // lock.lock();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("DestroyTiledSurface failed: {}", e.what());
    throw TileManagerException("DestroyTiledSurface failed: " +
                               std::string(e.what()));
  }
}

const TileInfo *TileManager::GetTileInfo(uint64_t surfaceId) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("GetTileInfo: Invalid surface ID: {}", surfaceId);
      return nullptr;
    }

    // Increment mutable cache counters
    it->second.info.cacheHits++;
    it->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++; // Overall manager cache hit for this operation

    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetTileInfo: Retrieved info for surface {}, latency={}us",
                  surfaceId, latency);
    return &it->second.info;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetTileInfo failed: {}", e.what());
    return nullptr;
  }
}

void TileManager::LinearToTiled(uint64_t surfaceId, const void *linearData,
                                size_t linearSize) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw TileManagerException("Invalid surface ID for LinearToTiled");
    }

    TileInfo &info = it->second.info;
    void *tiledData = it->second.data.data();

    // Check if linearSize matches expected size for the surface
    size_t expectedSize = info.slice * info.depth;
    if (linearSize < expectedSize) {
      spdlog::warn("LinearToTiled: Provided linearSize ({}) is less than "
                   "expected surface size ({}) for surface {}",
                   linearSize, expectedSize, surfaceId);
      // We will still proceed, but only copy up to linearSize
    }

    // Release lock during computation to allow other read operations
    lock.unlock();
    switch (info.mode) {
    case TileMode::LINEAR:
      std::memcpy(tiledData, linearData, std::min(linearSize, expectedSize));
      break;
    case TileMode::TILED_1D:
      LinearToTiled1D(info, linearData, tiledData);
      break;
    case TileMode::TILED_2D:
      LinearToTiled2D(info, linearData, tiledData);
      break;
    case TileMode::TILED_2B:
      LinearToTiled2B(info, linearData, tiledData);
      break;
    case TileMode::TILED_3D:
      LinearToTiled3D(info, linearData, tiledData);
      break;
    case TileMode::TILED_DEPTH:
      LinearToTiledDepth(info, linearData, tiledData);
      break;
    default:
      // Re-acquire lock before throwing to ensure consistent state
      lock.lock();
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw TileManagerException("Unsupported tile mode for LinearToTiled");
    }
    lock.lock(); // Re-acquire lock after computation

    // Update cache and stats
    SurfaceCacheEntry cacheEntry{surfaceId, it->second.data, 0, 0};
    m_surfaceCache[surfaceId] = cacheEntry; // Overwrite with new data
    it->second.info.cacheHits++;
    it->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;

    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("LinearToTiled: Converted surface {}, latency={}us",
                  surfaceId, latency);

    // Notify CommandProcessor of surface update
    m_commandProcessor.NotifySurfaceUpdated(surfaceId, 0, 0, info.width,
                                            info.height);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("LinearToTiled failed for surface {}: {}", surfaceId,
                  e.what());
    throw TileManagerException("LinearToTiled failed: " +
                               std::string(e.what()));
  }
}

void TileManager::TiledToLinear(uint64_t surfaceId, void *linearData,
                                size_t linearSize) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw TileManagerException("Invalid surface ID for TiledToLinear");
    }

    const TileInfo &info = it->second.info;
    const void *tiledData = it->second.data.data();

    // Check if linearSize matches expected size for the surface
    size_t expectedSize = info.slice * info.depth;
    if (linearSize < expectedSize) {
      spdlog::warn("TiledToLinear: Provided linearSize ({}) is less than "
                   "expected surface size ({}) for surface {}",
                   linearSize, expectedSize, surfaceId);
      // We will still proceed, but only copy up to linearSize
    }

    // Release lock during computation
    lock.unlock();
    switch (info.mode) {
    case TileMode::LINEAR:
      std::memcpy(linearData, tiledData, std::min(linearSize, expectedSize));
      break;
    case TileMode::TILED_1D:
      TiledToLinear1D(info, tiledData, linearData);
      break;
    case TileMode::TILED_2D:
      TiledToLinear2D(info, tiledData, linearData);
      break;
    case TileMode::TILED_2B:
      TiledToLinear2B(info, tiledData, linearData);
      break;
    case TileMode::TILED_3D:
      TiledToLinear3D(info, tiledData, linearData);
      break;
    case TileMode::TILED_DEPTH:
      TiledToLinearDepth(info, tiledData, linearData);
      break;
    default:
      // Re-acquire lock before throwing
      lock.lock();
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw TileManagerException("Unsupported tile mode for TiledToLinear");
    }
    lock.lock(); // Re-acquire lock

    // Update stats
    it->second.info.cacheHits++;
    it->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;

    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("TiledToLinear: Converted surface {}, latency={}us",
                  surfaceId, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("TiledToLinear failed for surface {}: {}", surfaceId,
                  e.what());
    throw TileManagerException("TiledToLinear failed: " +
                               std::string(e.what()));
  }
}

uint64_t TileManager::GetGcnTiledAddress(const TileInfo &info, uint32_t x,
                                         uint32_t y, uint32_t z) const {
  // This function provides a simplified model of GCN tiling for emulation.
  // Real hardware tiling is significantly more complex and depends on many
  // factors (e.g., micro-tile mode, macro-tile mode, bank swizzle, pipe swizzle,
  // array mode, etc.). This implementation uses a basic Z-order curve within
  // fixed 8x8 tiles.

  uint32_t bpp = info.bytesPerPixel;
  if (bpp == 0) {
    spdlog::warn("GetGcnTiledAddress: Bytes per pixel is zero for format {}",
                 static_cast<int>(info.format));
    return 0;
  }

  // Calculate dimensions in tiles
  uint32_t width_in_tiles = (info.width + TILE_WIDTH - 1) / TILE_WIDTH;
  // uint32_t height_in_tiles = (info.height + TILE_HEIGHT - 1) / TILE_HEIGHT; // Not directly used here

  // Calculate which tile this pixel belongs to
  uint32_t tile_x = x / TILE_WIDTH;
  uint32_t tile_y = y / TILE_HEIGHT;

  // Calculate coordinates within the current tile
  uint32_t in_tile_x = x % TILE_WIDTH;
  uint32_t in_tile_y = y % TILE_HEIGHT;

  // Calculate slice offset for 3D textures
  uint64_t slice_offset = (uint64_t)z * info.slice;

  // Calculate the base offset for the current tile within its slice
  // This assumes a linear arrangement of tiles in memory
  uint64_t tile_offset_in_slice =
      (tile_y * width_in_tiles + tile_x) * (TILE_WIDTH * TILE_HEIGHT * bpp);

  // Z-order (Morton) swizzle for pixels within a tile
  // This interleaves the x and y bits to create a 2D Morton code.
  uint32_t morton_offset = 0;
  uint32_t bit_idx = 0;
  uint32_t temp_x = in_tile_x;
  uint32_t temp_y = in_tile_y;

  // Assuming TILE_WIDTH and TILE_HEIGHT are powers of 2 (e.g., 8x8)
  // For 8x8, we need 3 bits for x and 3 bits for y.
  // Max bits needed is log2(max(TILE_WIDTH, TILE_HEIGHT))
  uint32_t max_bits = std::max(static_cast<uint32_t>(std::log2(TILE_WIDTH)),
                               static_cast<uint32_t>(std::log2(TILE_HEIGHT)));

  for (uint32_t i = 0; i < max_bits; ++i) {
    morton_offset |= (temp_x & 1) << bit_idx++; // x bit
    temp_x >>= 1;
    morton_offset |= (temp_y & 1) << bit_idx++; // y bit
    temp_y >>= 1;
  }

  uint64_t in_tile_offset = morton_offset * bpp;

  return slice_offset + tile_offset_in_slice + in_tile_offset;
}

// Implementations for the new tiling modes
void TileManager::LinearToTiled2B(const TileInfo &info, const void *linearData,
                                  void *tiledData) const {
  // TILED_2B often has additional bank/pipe swizzling. For emulation,
  // we can treat it similarly to 2D but could add another XOR layer if needed.
  // For now, let's delegate to the standard 2D implementation.
  // A more accurate emulation would involve specific bank/pipe calculations.
  LinearToTiled2D(info, linearData, tiledData);
}

void TileManager::TiledToLinear2B(const TileInfo &info, const void *tiledData,
                                  void *linearData) const {
  TiledToLinear2D(info, tiledData, linearData);
}

void TileManager::LinearToTiled3D(const TileInfo &info, const void *linearData,
                                  void *tiledData) const {
  const auto *src_base = static_cast<const uint8_t *>(linearData);
  auto *dst_base = static_cast<uint8_t *>(tiledData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;
  uint32_t linear_slice_size = linear_pitch * info.height;

  for (uint32_t z = 0; z < info.depth; ++z) {
    for (uint32_t y = 0; y < info.height; ++y) {
      for (uint32_t x = 0; x < info.width; ++x) {
        uint64_t linear_offset = (uint64_t)z * linear_slice_size +
                                 (uint64_t)y * linear_pitch +
                                 x * info.bytesPerPixel;
        uint64_t tiled_offset = GetGcnTiledAddress(info, x, y, z);
        // Ensure we don't write beyond the allocated tiled data size
        if (tiled_offset + info.bytesPerPixel <= info.slice * info.depth) {
          std::memcpy(dst_base + tiled_offset, src_base + linear_offset,
                      info.bytesPerPixel);
        } else {
          spdlog::warn("LinearToTiled3D: Write out of bounds for pixel "
                       "({},{},{}) on surface. Tiled offset: {}, Max size: {}",
                       x, y, z, tiled_offset, info.slice * info.depth);
        }
      }
    }
  }
}

void TileManager::TiledToLinear3D(const TileInfo &info, const void *tiledData,
                                  void *linearData) const {
  const auto *src_base = static_cast<const uint8_t *>(tiledData);
  auto *dst_base = static_cast<uint8_t *>(linearData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;
  uint32_t linear_slice_size = linear_pitch * info.height;

  for (uint32_t z = 0; z < info.depth; ++z) {
    for (uint32_t y = 0; y < info.height; ++y) {
      for (uint32_t x = 0; x < info.width; ++x) {
        uint64_t tiled_offset = GetGcnTiledAddress(info, x, y, z);
        uint64_t linear_offset = (uint64_t)z * linear_slice_size +
                                 (uint64_t)y * linear_pitch +
                                 x * info.bytesPerPixel;
        // Ensure we don't read beyond the allocated tiled data size
        if (tiled_offset + info.bytesPerPixel <= info.slice * info.depth) {
          std::memcpy(dst_base + linear_offset, src_base + tiled_offset,
                      info.bytesPerPixel);
        } else {
          spdlog::warn("TiledToLinear3D: Read out of bounds for pixel "
                       "({},{},{}) on surface. Tiled offset: {}, Max size: {}",
                       x, y, z, tiled_offset, info.slice * info.depth);
        }
      }
    }
  }
}

void TileManager::LinearToTiledDepth(const TileInfo &info,
                                     const void *linearData,
                                     void *tiledData) const {
  // Depth tiling is a specialized 2D format. The main difference is often
  // metadata for compression (HTILE), which we are not emulating.
  // The address calculation can be considered the same as 2D for basic
  // emulation.
  LinearToTiled2D(info, linearData, tiledData);
}

void TileManager::TiledToLinearDepth(const TileInfo &info,
                                     const void *tiledData,
                                     void *linearData) const {
  TiledToLinear2D(info, tiledData, linearData);
}

// Existing conversion functions (can be simplified now)
void TileManager::LinearToTiled1D(const TileInfo &info, const void *linearData,
                                  void *tiledData) const {
  const auto *src = static_cast<const uint8_t *>(linearData);
  auto *dst = static_cast<uint8_t *>(tiledData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;
  // For 1D tiling, data is typically linear within each row, but rows might be
  // arranged differently. Here, we assume a simple row-by-row copy with pitch.
  for (uint32_t y = 0; y < info.height; ++y) {
    // Ensure we don't write beyond the allocated tiled data size
    if ((y + 1) * info.pitch <= info.slice) {
      std::memcpy(dst + y * info.pitch, src + y * linear_pitch, linear_pitch);
    } else {
      spdlog::warn("LinearToTiled1D: Write out of bounds for row {} on surface. "
                   "Tiled offset: {}, Max slice size: {}",
                   y, y * info.pitch, info.slice);
    }
  }
}

void TileManager::TiledToLinear1D(const TileInfo &info, const void *tiledData,
                                  void *linearData) const {
  const auto *src = static_cast<const uint8_t *>(tiledData);
  auto *dst = static_cast<uint8_t *>(linearData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;
  for (uint32_t y = 0; y < info.height; ++y) {
    // Ensure we don't read beyond the allocated tiled data size
    if ((y + 1) * info.pitch <= info.slice) {
      std::memcpy(dst + y * linear_pitch, src + y * info.pitch, linear_pitch);
    } else {
      spdlog::warn("TiledToLinear1D: Read out of bounds for row {} on surface. "
                   "Tiled offset: {}, Max slice size: {}",
                   y, y * info.pitch, info.slice);
    }
  }
}

void TileManager::LinearToTiled2D(const TileInfo &info, const void *linearData,
                                  void *tiledData) const {
  const auto *src_base = static_cast<const uint8_t *>(linearData);
  auto *dst_base = static_cast<uint8_t *>(tiledData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;

  for (uint32_t y = 0; y < info.height; ++y) {
    for (uint32_t x = 0; x < info.width; ++x) {
      uint64_t linear_offset =
          (uint64_t)y * linear_pitch + x * info.bytesPerPixel;
      uint64_t tiled_offset = GetGcnTiledAddress(info, x, y, 0); // Z is 0 for 2D
      // Ensure we don't write beyond the allocated tiled data size
      if (tiled_offset + info.bytesPerPixel <= info.slice) {
        std::memcpy(dst_base + tiled_offset, src_base + linear_offset,
                    info.bytesPerPixel);
      } else {
        spdlog::warn("LinearToTiled2D: Write out of bounds for pixel "
                     "({},{}) on surface. Tiled offset: {}, Max slice size: {}",
                     x, y, tiled_offset, info.slice);
      }
    }
  }
}

void TileManager::TiledToLinear2D(const TileInfo &info, const void *tiledData,
                                  void *linearData) const {
  const auto *src_base = static_cast<const uint8_t *>(tiledData);
  auto *dst_base = static_cast<uint8_t *>(linearData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;

  for (uint32_t y = 0; y < info.height; ++y) {
    for (uint32_t x = 0; x < info.width; ++x) {
      uint64_t tiled_offset = GetGcnTiledAddress(info, x, y, 0); // Z is 0 for 2D
      uint64_t linear_offset =
          (uint64_t)y * linear_pitch + x * info.bytesPerPixel;
      // Ensure we don't read beyond the allocated tiled data size
      if (tiled_offset + info.bytesPerPixel <= info.slice) {
        std::memcpy(dst_base + linear_offset, src_base + tiled_offset,
                    info.bytesPerPixel);
      } else {
        spdlog::warn("TiledToLinear2D: Read out of bounds for pixel "
                     "({},{}) on surface. Tiled offset: {}, Max slice size: {}",
                     x, y, tiled_offset, info.slice);
      }
    }
  }
}

void TileManager::ClearTiledSurface(uint64_t surfaceId, const float color[4]) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ClearTiledSurface: Invalid surface ID: {}", surfaceId);
      throw TileManagerException("Invalid surface ID");
    }

    const TileInfo &info = it->second.info;
    std::vector<uint8_t> &data = it->second.data;
    lock.unlock(); // Release lock during data manipulation

    // Use a temporary buffer for the clear value to avoid reinterpreting
    // data.data() directly, which might not be aligned for all types.
    std::vector<uint8_t> clear_value_bytes(info.bytesPerPixel);

    switch (info.format) {
    case TileFormat::R8_UNORM: {
      uint8_t value = static_cast<uint8_t>(color[0] * 255.0f);
      clear_value_bytes[0] = value;
      break;
    }
    case TileFormat::R8G8_UNORM: {
      uint8_t r = static_cast<uint8_t>(color[0] * 255.0f);
      uint8_t g = static_cast<uint8_t>(color[1] * 255.0f);
      uint16_t value = (static_cast<uint16_t>(g) << 8) | r;
      std::memcpy(clear_value_bytes.data(), &value, sizeof(value));
      break;
    }
    case TileFormat::R8G8B8A8_UNORM: {
      uint8_t r = static_cast<uint8_t>(color[0] * 255.0f);
      uint8_t g = static_cast<uint8_t>(color[1] * 255.0f);
      uint8_t b = static_cast<uint8_t>(color[2] * 255.0f);
      uint8_t a = static_cast<uint8_t>(color[3] * 255.0f);
      uint32_t value = (static_cast<uint32_t>(a) << 24) |
                       (static_cast<uint32_t>(b) << 16) |
                       (static_cast<uint32_t>(g) << 8) | r;
      std::memcpy(clear_value_bytes.data(), &value, sizeof(value));
      break;
    }
    case TileFormat::B8G8R8A8_UNORM: {
      uint8_t r = static_cast<uint8_t>(color[0] * 255.0f);
      uint8_t g = static_cast<uint8_t>(color[1] * 255.0f);
      uint8_t b = static_cast<uint8_t>(color[2] * 255.0f);
      uint8_t a = static_cast<uint8_t>(color[3] * 255.0f);
      uint32_t value = (static_cast<uint32_t>(a) << 24) |
                       (static_cast<uint32_t>(r) << 16) |
                       (static_cast<uint32_t>(g) << 8) | b;
      std::memcpy(clear_value_bytes.data(), &value, sizeof(value));
      break;
    }
    case TileFormat::R16_FLOAT: {
      // Simple float to half-float conversion (approximation)
      uint16_t value = static_cast<uint16_t>(color[0] * 65535.0f); // Placeholder
      std::memcpy(clear_value_bytes.data(), &value, sizeof(value));
      break;
    }
    case TileFormat::R16G16_FLOAT: {
      uint16_t r = static_cast<uint16_t>(color[0] * 65535.0f);
      uint16_t g = static_cast<uint16_t>(color[1] * 65535.0f);
      uint32_t value = (static_cast<uint32_t>(g) << 16) | r;
      std::memcpy(clear_value_bytes.data(), &value, sizeof(value));
      break;
    }
    case TileFormat::R16G16B16A16_FLOAT: {
      uint16_t r = static_cast<uint16_t>(color[0] * 65535.0f);
      uint16_t g = static_cast<uint16_t>(color[1] * 65535.0f);
      uint16_t b = static_cast<uint16_t>(color[2] * 65535.0f);
      uint16_t a = static_cast<uint16_t>(color[3] * 65535.0f);
      uint64_t value = (static_cast<uint64_t>(a) << 48) |
                       (static_cast<uint64_t>(b) << 32) |
                       (static_cast<uint64_t>(g) << 16) | r;
      std::memcpy(clear_value_bytes.data(), &value, sizeof(value));
      break;
    }
    case TileFormat::R32_FLOAT: {
      float value = color[0];
      std::memcpy(clear_value_bytes.data(), &value, sizeof(value));
      break;
    }
    case TileFormat::R32G32_FLOAT: {
      float values[2] = {color[0], color[1]};
      std::memcpy(clear_value_bytes.data(), &values, sizeof(values));
      break;
    }
    case TileFormat::R32G32B32A32_FLOAT: {
      float values[4] = {color[0], color[1], color[2], color[3]};
      std::memcpy(clear_value_bytes.data(), &values, sizeof(values));
      break;
    }
    case TileFormat::D16_UNORM: {
      uint16_t value = static_cast<uint16_t>(color[0] * 65535.0f);
      std::memcpy(clear_value_bytes.data(), &value, sizeof(value));
      break;
    }
    case TileFormat::D24_UNORM_S8_UINT: {
      // Depth is 24-bit, stencil is 8-bit. Clear depth component.
      uint32_t depth_val = static_cast<uint32_t>(color[0] * 0xFFFFFF); // 24-bit
      uint32_t value = depth_val; // Stencil part (8-bit) is 0 for now
      std::memcpy(clear_value_bytes.data(), &value, sizeof(value));
      break;
    }
    case TileFormat::D32_FLOAT: {
      float value = color[0];
      std::memcpy(clear_value_bytes.data(), &value, sizeof(value));
      break;
    }
    default:
      lock.lock(); // Re-acquire lock before throwing
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ClearTiledSurface: Unsupported format: {}",
                    static_cast<int>(info.format));
      throw TileManagerException("Unsupported format for clear");
    }

    // Fill the entire data buffer with the clear value
    for (size_t i = 0; i < data.size(); i += info.bytesPerPixel) {
      std::memcpy(data.data() + i, clear_value_bytes.data(),
                  info.bytesPerPixel);
    }

    lock.lock(); // Re-acquire lock after data manipulation
    // Update cache
    SurfaceCacheEntry cacheEntry{surfaceId, data, 0, 0}; // Reset cache hits/misses for entry
    m_surfaceCache[surfaceId] = cacheEntry;
    it->second.info.cacheHits++;
    it->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("ClearTiledSurface: Cleared surface {}, color=[{},{},{},{}], "
                 "latency={}us",
                 surfaceId, color[0], color[1], color[2], color[3], latency);

    if (info.isRenderTarget) {
      // Example: Set a GNM register to indicate a clear operation
      // This is highly dependent on the specific GNM state emulation.
      // For a real PS4, this would involve writing to a render target clear register.
      m_gnmState.SetContextRegister(0xA001, 0xFFFFFFFF); // Dummy register for clear
    }

    // Notify CommandProcessor of surface clear for GPU synchronization
    try {
      // Pack RGBA clear color into uint32_t (A<<24 | B<<16 | G<<8 | R)
      // This packing assumes 8-bit per channel. For float formats, this might
      // need to be adjusted or a different notification mechanism used.
      uint32_t clearValuePacked = 0;
      if (info.bytesPerPixel == 4 && (info.format == TileFormat::R8G8B8A8_UNORM ||
                                      info.format == TileFormat::B8G8R8A8_UNORM)) {
        clearValuePacked = (static_cast<uint32_t>(color[3] * 255.0f) << 24) |
                           (static_cast<uint32_t>(color[2] * 255.0f) << 16) |
                           (static_cast<uint32_t>(color[1] * 255.0f) << 8) |
                           static_cast<uint32_t>(color[0] * 255.0f);
      } else {
        // For other formats, just pass the first float component as a uint32_t
        // This is a simplification and might not be accurate for all formats.
        clearValuePacked = *reinterpret_cast<const uint32_t*>(&color[0]);
      }
      m_commandProcessor.NotifySurfaceClear(surfaceId, clearValuePacked);
      spdlog::trace("Notified CommandProcessor of surface {} clear", surfaceId);
    } catch (const std::exception &e) {
      spdlog::warn("Failed to notify CommandProcessor of surface clear: {}",
                   e.what());
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ClearTiledSurface failed: {}", e.what());
    throw TileManagerException("ClearTiledSurface failed: " +
                               std::string(e.what()));
  }
}

void TileManager::CopyTiledSurface(uint64_t dstSurfaceId,
                                   uint64_t srcSurfaceId) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto srcIt = m_surfaces.find(srcSurfaceId);
    auto dstIt = m_surfaces.find(dstSurfaceId);
    if (srcIt == m_surfaces.end() || !srcIt->second.active ||
        dstIt == m_surfaces.end() || !dstIt->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("CopyTiledSurface: Invalid surface IDs: src={}, dst={}",
                    srcSurfaceId, dstSurfaceId);
      throw TileManagerException("Invalid surface IDs");
    }

    const TileInfo &srcInfo = srcIt->second.info;
    TileInfo &dstInfo = dstIt->second.info;
    if (srcInfo.width != dstInfo.width || srcInfo.height != dstInfo.height ||
        srcInfo.depth != dstInfo.depth || srcInfo.format != dstInfo.format ||
        srcInfo.mode != dstInfo.mode) { // Also check tile mode
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("CopyTiledSurface: Incompatible surfaces: src={}, dst={}. "
                    "Dimensions or format/mode mismatch.",
                    srcSurfaceId, dstSurfaceId);
      throw TileManagerException("Incompatible surfaces");
    }

    // Perform the copy
    dstIt->second.data = srcIt->second.data;

    // Update cache for destination
    SurfaceCacheEntry cacheEntry{dstSurfaceId, dstIt->second.data, 0, 0};
    m_surfaceCache[dstSurfaceId] = cacheEntry;

    // Update stats for both surfaces and manager
    srcIt->second.info.cacheHits++;
    srcIt->second.cacheHits++;
    dstIt->second.info.cacheHits++;
    dstIt->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("CopyTiledSurface: Copied surface {} to {}, size={} bytes, "
                 "latency={}us",
                 srcSurfaceId, dstSurfaceId, srcIt->second.data.size(),
                 latency);

    lock.unlock();
    m_commandProcessor.NotifySurfaceCopy(srcSurfaceId, dstSurfaceId, 0, 0, 0, 0,
                                         srcInfo.width, srcInfo.height);
    // Re-lock if needed.
    // lock.lock();

  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("CopyTiledSurface failed: {}", e.what());
    throw TileManagerException("CopyTiledSurface failed: " +
                               std::string(e.what()));
  }
}

void TileManager::GetCurrentTile(uint32_t &x, uint32_t &y, uint32_t &width,
                                 uint32_t &height) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    x = 0;
    y = 0;
    width = 0; // Initialize to 0, will be set if active surface exists
    height = 0;

    if (m_currentSurfaceId == 0) {
      m_stats.cacheHits++; // Still a "hit" for the operation, just no active surface
      spdlog::trace("GetCurrentTile: No active surface");
      return;
    }

    auto it = m_surfaces.find(m_currentSurfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("GetCurrentTile: Invalid current surface ID: {}",
                    m_currentSurfaceId);
      // Note: Cannot reset m_currentSurfaceId in const method
      return;
    }

    const TileInfo &info = it->second.info;
    x = m_currentTileX * TILE_WIDTH;
    y = m_currentTileY * TILE_HEIGHT;

    // Calculate actual width/height of the current tile, clamping to surface bounds
    width = std::min(TILE_WIDTH, info.width - x);
    height = std::min(TILE_HEIGHT, info.height - y);

    // Ensure width/height are not negative if surface dimensions are smaller than tile
    if (x >= info.width) width = 0;
    if (y >= info.height) height = 0;

    it->second.info.cacheHits++;
    it->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetCurrentTile: surface={}, x={}, y={}, width={}, "
                  "height={}, latency={}us",
                  m_currentSurfaceId, x, y, width, height, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetCurrentTile failed: {}", e.what());
  }
}

void TileManager::NextTile() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    if (m_currentSurfaceId == 0 || m_tilesX == 0 || m_tilesY == 0) {
      m_stats.cacheHits++; // Still a "hit" for the operation, just no tiles to advance
      spdlog::trace("NextTile: No active surface or tiles to advance");
      return;
    }

    m_currentTileX++;
    if (m_currentTileX >= m_tilesX) {
      m_currentTileX = 0;
      m_currentTileY++;
      if (m_currentTileY >= m_tilesY) {
        m_currentTileY = 0; // Wrap around to the first tile
      }
    }

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("NextTile: surface={}, tileX={}, tileY={}, latency={}us",
                  m_currentSurfaceId, m_currentTileX, m_currentTileY, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NextTile failed: {}", e.what());
  }
}

size_t TileManager::GetTotalTiles() const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    size_t totalTiles = 0;
    for (const auto &[id, surface] : m_surfaces) {
      if (surface.active) {
        // Calculate tiles based on actual dimensions, rounding up
        uint32_t tiles_x = (surface.info.width + TILE_WIDTH - 1) / TILE_WIDTH;
        uint32_t tiles_y = (surface.info.height + TILE_HEIGHT - 1) / TILE_HEIGHT;
        totalTiles += (size_t)tiles_x * tiles_y * surface.info.depth; // Include depth for 3D
      }
    }

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetTotalTiles: total={}, latency={}us", totalTiles, latency);
    return totalTiles;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetTotalTiles failed: {}", e.what());
    return 0;
  }
}

bool TileManager::GetCachedSurfaceData(uint64_t surfaceId,
                                       std::vector<uint8_t> &data) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaceCache.find(surfaceId);
    if (it != m_surfaceCache.end()) {
      data = it->second.data; // Copy data
      it->second.cacheHits++; // Increment mutable counter for this entry
      m_stats.cacheHits++;    // Overall manager cache hit
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace(
          "GetCachedSurfaceData: surface={}, hit, size={} bytes, latency={}us",
          surfaceId, data.size(), latency);
      return true;
    }
    m_stats.cacheMisses++; // Overall manager cache miss
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetCachedSurfaceData: surface={}, miss, latency={}us",
                  surfaceId, latency);
    return false;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetCachedSurfaceData failed: {}", e.what());
    return false;
  }
}

void TileManager::ClearSurfaceCache() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    m_surfaceCache.clear();
    m_stats.cacheHits++; // Consider clearing the cache a "hit" for the operation
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ClearSurfaceCache: Cleared cache, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ClearSurfaceCache failed: {}", e.what());
  }
}

TileManagerStats TileManager::GetStats() const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex); // Lock for m_stats
  try {
    // No direct cache hit/miss for GetStats itself, but it's an operation.
    m_stats.operationCount++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetStats: latency={}us", latency);
    return m_stats;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetStats failed: {}", e.what());
    return m_stats; // Return current stats even on error
  }
}

TileManager::TileStats TileManager::GetTileStats() const {
  std::shared_lock<std::shared_mutex> lock(m_mutex); // Lock for m_tiles
  TileStats stats;
  stats.total_tiles = m_max_tiles;
  stats.allocated_tiles = 0;
  stats.memory_used = 0;

  for (const auto &tile : m_tiles) {
    if (tile.allocated) {
      stats.allocated_tiles++;
      stats.memory_used += tile.data.size();
    }
  }
  stats.free_tiles = stats.total_tiles - stats.allocated_tiles;
  return stats;
}

bool TileManager::AllocateTile(uint32_t tile_id,
                               const TileDescriptor &descriptor) {
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  if (tile_id >= m_max_tiles) {
    spdlog::error("AllocateTile: tile_id {} out of bounds (max {})", tile_id,
                  m_max_tiles);
    return false;
  }

  if (m_tiles.size() <= tile_id) {
    m_tiles.resize(tile_id + 1); // Dynamically grow if needed
  }

  if (m_tiles[tile_id].allocated) {
    spdlog::warn("AllocateTile: Tile {} already allocated", tile_id);
    return false;
  }

  try {
    Tile &tile = m_tiles[tile_id];
    tile.descriptor = descriptor;
    tile.allocated = true;
    tile.last_access = std::chrono::steady_clock::now();

    // Calculate size for the tile data
    uint32_t bytesPerPixel = GetBytesPerPixelInternal(descriptor.format);
    size_t tile_data_size =
        descriptor.width * descriptor.height * bytesPerPixel;
    tile.data.resize(tile_data_size);
    std::fill(tile.data.begin(), tile.data.end(), 0); // Initialize to zero

    m_active_tiles.push_back(tile_id);
    spdlog::info("AllocateTile: Allocated tile {} ({}x{} pixels, {} bpp)",
                 tile_id, descriptor.width, descriptor.height, bytesPerPixel);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("AllocateTile failed for tile {}: {}", tile_id, e.what());
    return false;
  }
}

bool TileManager::DeallocateTile(uint32_t tile_id) {
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  return DeallocateTileInternal(tile_id);
}

bool TileManager::DeallocateTileInternal(uint32_t tile_id) {
  // Assumes m_mutex is already held
  if (tile_id >= m_tiles.size() || !m_tiles[tile_id].allocated) {
    spdlog::warn("DeallocateTile: Tile {} not allocated or out of bounds",
                 tile_id);
    return false;
  }

  m_tiles[tile_id].allocated = false;
  m_tiles[tile_id].data.clear(); // Free memory
  m_tiles[tile_id].data.shrink_to_fit();

  // Remove from active tiles list
  auto it = std::remove(m_active_tiles.begin(), m_active_tiles.end(), tile_id);
  m_active_tiles.erase(it, m_active_tiles.end());

  spdlog::info("DeallocateTile: Deallocated tile {}", tile_id);
  return true;
}

bool TileManager::WriteTileData(uint32_t tile_id, const void *data,
                                size_t size, size_t offset) {
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  if (tile_id >= m_tiles.size() || !m_tiles[tile_id].allocated) {
    spdlog::error("WriteTileData: Tile {} not allocated or out of bounds",
                  tile_id);
    return false;
  }

  Tile &tile = m_tiles[tile_id];
  if (offset + size > tile.data.size()) {
    spdlog::error("WriteTileData: Write out of bounds for tile {}. Offset {}, "
                  "size {}, tile data size {}",
                  tile_id, offset, size, tile.data.size());
    return false;
  }

  std::memcpy(tile.data.data() + offset, data, size);
  tile.last_access = std::chrono::steady_clock::now();
  return true;
}

bool TileManager::ReadTileData(uint32_t tile_id, void *data, size_t size,
                               size_t offset) const {
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  if (tile_id >= m_tiles.size() || !m_tiles[tile_id].allocated) {
    spdlog::error("ReadTileData: Tile {} not allocated or out of bounds",
                  tile_id);
    return false;
  }

  const Tile &tile = m_tiles[tile_id];
  if (offset + size > tile.data.size()) {
    spdlog::error("ReadTileData: Read out of bounds for tile {}. Offset {}, "
                  "size {}, tile data size {}",
                  tile_id, offset, size, tile.data.size());
    return false;
  }

  std::memcpy(data, tile.data.data() + offset, size);
  // Update last_access for const method requires mutable or separate mechanism
  // For now, we'll skip updating last_access in const method.
  // If needed, could use const_cast or a separate non-const helper.
  return true;
}

std::vector<uint32_t> TileManager::GetActiveTiles() const {
  std::shared_lock<std::shared_mutex> lock(m_mutex);
  return m_active_tiles;
}

void TileManager::CleanupUnusedTiles(std::chrono::milliseconds max_age) {
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  auto now = std::chrono::steady_clock::now();
  std::vector<uint32_t> tiles_to_deallocate;

  for (uint32_t i = 0; i < m_tiles.size(); ++i) {
    if (m_tiles[i].allocated &&
        (now - m_tiles[i].last_access) > max_age) {
      tiles_to_deallocate.push_back(i);
    }
  }

  for (uint32_t tile_id : tiles_to_deallocate) {
    DeallocateTileInternal(tile_id); // Use internal, already locked
  }
  if (!tiles_to_deallocate.empty()) {
    spdlog::info("CleanupUnusedTiles: Deallocated {} tiles older than {}ms",
                 tiles_to_deallocate.size(), max_age.count());
  }
}

void TileManager::SaveState(std::ostream &out) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex); // Lock for surfaces and main state
  std::shared_lock<std::shared_mutex> tile_lock(m_mutex);   // Lock for internal tiles

  try {
    uint32_t version = 1; // Serialization version
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    // Serialize m_surfaces
    uint64_t surfaceCount = m_surfaces.size();
    out.write(reinterpret_cast<const char *>(&surfaceCount),
              sizeof(surfaceCount));
    for (const auto &[id, surface] : m_surfaces) {
      out.write(reinterpret_cast<const char *>(&id), sizeof(id));
      surface.Serialize(out);
    }

    // Serialize m_surfaceCache
    uint64_t cacheCount = m_surfaceCache.size();
    out.write(reinterpret_cast<const char *>(&cacheCount), sizeof(cacheCount));
    for (const auto &[id, entry] : m_surfaceCache) {
      out.write(reinterpret_cast<const char *>(&id), sizeof(id));
      uint64_t dataSize = entry.data.size();
      out.write(reinterpret_cast<const char *>(&dataSize), sizeof(dataSize));
      out.write(reinterpret_cast<const char *>(entry.data.data()), dataSize);
      out.write(reinterpret_cast<const char *>(&entry.cacheHits),
                sizeof(entry.cacheHits));
      out.write(reinterpret_cast<const char *>(&entry.cacheMisses),
                sizeof(entry.cacheMisses));
    }

    // Serialize main TileManager state variables
    out.write(reinterpret_cast<const char *>(&m_nextSurfaceId),
              sizeof(m_nextSurfaceId));
    out.write(reinterpret_cast<const char *>(&m_currentSurfaceId),
              sizeof(m_currentSurfaceId));
    out.write(reinterpret_cast<const char *>(&m_currentTileX),
              sizeof(m_currentTileX));
    out.write(reinterpret_cast<const char *>(&m_currentTileY),
              sizeof(m_currentTileY));
    out.write(reinterpret_cast<const char *>(&m_tilesX), sizeof(m_tilesX));
    out.write(reinterpret_cast<const char *>(&m_tilesY), sizeof(m_tilesY));
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));

    // Serialize internal m_tiles (for AllocateTile/DeallocateTile)
    uint64_t internalTileCount = m_tiles.size();
    out.write(reinterpret_cast<const char *>(&internalTileCount), sizeof(internalTileCount));
    for (uint64_t i = 0; i < internalTileCount; ++i) {
        const auto& tile = m_tiles[i];
        out.write(reinterpret_cast<const char*>(&tile.allocated), sizeof(tile.allocated));
        if (tile.allocated) {
            // Serialize TileDescriptor
            out.write(reinterpret_cast<const char*>(&tile.descriptor.width), sizeof(tile.descriptor.width));
            out.write(reinterpret_cast<const char*>(&tile.descriptor.height), sizeof(tile.descriptor.height));
            out.write(reinterpret_cast<const char*>(&tile.descriptor.bits_per_pixel), sizeof(tile.descriptor.bits_per_pixel));
            out.write(reinterpret_cast<const char*>(&tile.descriptor.format), sizeof(tile.descriptor.format));
            out.write(reinterpret_cast<const char*>(&tile.descriptor.mode), sizeof(tile.descriptor.mode));

            // Serialize data
            uint64_t tileDataSize = tile.data.size();
            out.write(reinterpret_cast<const char*>(&tileDataSize), sizeof(tileDataSize));
            out.write(reinterpret_cast<const char*>(tile.data.data()), tileDataSize);

            // Serialize last_access (as duration since epoch or similar)
            auto duration = tile.last_access.time_since_epoch();
            uint64_t lastAccessNs = std::chrono::duration_cast<std::chrono::nanoseconds>(duration).count();
            out.write(reinterpret_cast<const char*>(&lastAccessNs), sizeof(lastAccessNs));
        }
    }

    // Serialize m_active_tiles
    uint64_t activeTilesCount = m_active_tiles.size();
    out.write(reinterpret_cast<const char*>(&activeTilesCount), sizeof(activeTilesCount));
    out.write(reinterpret_cast<const char*>(m_active_tiles.data()), activeTilesCount * sizeof(uint32_t));

    // Serialize m_max_tiles
    out.write(reinterpret_cast<const char*>(&m_max_tiles), sizeof(m_max_tiles));


    if (!out.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++; // Treat as a cache miss for the save operation
      throw TileManagerException("Failed to write tile manager state");
    }

    m_stats.cacheHits++; // Overall manager cache hit for the save operation
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SaveState: Saved tile manager state, surfaces={}, "
                 "cache_count={}, internal_tiles={}, latency={}us",
                 surfaceCount, cacheCount, internalTileCount, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SaveState failed: {}", e.what());
    throw TileManagerException("SaveState failed: " + std::string(e.what()));
  }
}

void TileManager::LoadState(std::istream &in) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex); // Lock for surfaces and main state
  std::unique_lock<std::shared_mutex> tile_lock(m_mutex);   // Lock for internal tiles

  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw TileManagerException("Unsupported tile manager state version: " +
                                 std::to_string(version));
    }

    // Clear current state before loading
    m_surfaces.clear();
    m_surfaceCache.clear();
    m_tiles.clear();
    m_tile_cache.clear(); // Not serialized, clear it
    m_active_tiles.clear();

    // Deserialize m_surfaces
    uint64_t surfaceCount;
    in.read(reinterpret_cast<char *>(&surfaceCount), sizeof(surfaceCount));
    for (uint64_t i = 0; i < surfaceCount && in.good(); ++i) {
      uint64_t id;
      TiledSurface surface;
      in.read(reinterpret_cast<char *>(&id), sizeof(id));
      surface.Deserialize(in);
      m_surfaces[id] = surface;
    }

    // Deserialize m_surfaceCache
    uint64_t cacheCount;
    in.read(reinterpret_cast<char *>(&cacheCount), sizeof(cacheCount));
    for (uint64_t i = 0; i < cacheCount && in.good(); ++i) {
      uint64_t id;
      SurfaceCacheEntry entry;
      in.read(reinterpret_cast<char *>(&id), sizeof(id));
      uint64_t dataSize;
      in.read(reinterpret_cast<char *>(&dataSize), sizeof(dataSize));
      if (dataSize > 1024 * 1024 * 1024) { // Sanity check
          throw TileManagerException("Invalid cached surface data size during load");
      }
      entry.data.resize(dataSize);
      if (dataSize > 0) {
        in.read(reinterpret_cast<char *>(entry.data.data()), dataSize);
      }
      in.read(reinterpret_cast<char *>(&entry.cacheHits),
              sizeof(entry.cacheHits));
      in.read(reinterpret_cast<char *>(&entry.cacheMisses),
              sizeof(entry.cacheMisses));
      entry.surfaceId = id;
      m_surfaceCache[id] = entry;
    }

    // Deserialize main TileManager state variables
    in.read(reinterpret_cast<char *>(&m_nextSurfaceId),
            sizeof(m_nextSurfaceId));
    in.read(reinterpret_cast<char *>(&m_currentSurfaceId),
            sizeof(m_currentSurfaceId));
    in.read(reinterpret_cast<char *>(&m_currentTileX), sizeof(m_currentTileX));
    in.read(reinterpret_cast<char *>(&m_currentTileY), sizeof(m_currentTileY));
    in.read(reinterpret_cast<char *>(&m_tilesX), sizeof(m_tilesX));
    in.read(reinterpret_cast<char *>(&m_tilesY), sizeof(m_tilesY));
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));

    // Deserialize internal m_tiles
    uint64_t internalTileCount;
    in.read(reinterpret_cast<char *>(&internalTileCount), sizeof(internalTileCount));
    m_tiles.resize(internalTileCount);
    for (uint64_t i = 0; i < internalTileCount && in.good(); ++i) {
        auto& tile = m_tiles[i];
        in.read(reinterpret_cast<char*>(&tile.allocated), sizeof(tile.allocated));
        if (tile.allocated) {
            // Deserialize TileDescriptor
            in.read(reinterpret_cast<char*>(&tile.descriptor.width), sizeof(tile.descriptor.width));
            in.read(reinterpret_cast<char*>(&tile.descriptor.height), sizeof(tile.descriptor.height));
            in.read(reinterpret_cast<char*>(&tile.descriptor.bits_per_pixel), sizeof(tile.descriptor.bits_per_pixel));
            in.read(reinterpret_cast<char*>(&tile.descriptor.format), sizeof(tile.descriptor.format));
            in.read(reinterpret_cast<char*>(&tile.descriptor.mode), sizeof(tile.descriptor.mode));

            // Deserialize data
            uint64_t tileDataSize;
            in.read(reinterpret_cast<char*>(&tileDataSize), sizeof(tileDataSize));
            if (tileDataSize > 1024 * 1024 * 1024) { // Sanity check
                throw TileManagerException("Invalid internal tile data size during load");
            }
            tile.data.resize(tileDataSize);
            if (tileDataSize > 0) {
                in.read(reinterpret_cast<char*>(tile.data.data()), tileDataSize);
            }

            // Deserialize last_access
            uint64_t lastAccessNs;
            in.read(reinterpret_cast<char*>(&lastAccessNs), sizeof(lastAccessNs));
            tile.last_access = std::chrono::steady_clock::time_point(std::chrono::nanoseconds(lastAccessNs));
        }
    }

    // Deserialize m_active_tiles
    uint64_t activeTilesCount;
    in.read(reinterpret_cast<char*>(&activeTilesCount), sizeof(activeTilesCount));
    if (activeTilesCount > internalTileCount) { // Sanity check
        throw TileManagerException("Invalid active tiles count during load");
    }
    m_active_tiles.resize(activeTilesCount);
    if (activeTilesCount > 0) {
        in.read(reinterpret_cast<char*>(m_active_tiles.data()), activeTilesCount * sizeof(uint32_t));
    }

    // Deserialize m_max_tiles
    in.read(reinterpret_cast<char*>(&m_max_tiles), sizeof(m_max_tiles));


    if (!in.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw TileManagerException("Failed to read tile manager state");
    }

    m_stats.cacheHits++; // Overall manager cache hit for the load operation
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("LoadState: Loaded tile manager state, surfaces={}, "
                 "cache_count={}, internal_tiles={}, latency={}us",
                 surfaceCount, cacheCount, internalTileCount, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("LoadState failed: {}", e.what());
    // Re-throw to indicate failure to caller
    throw TileManagerException("LoadState failed: " + std::string(e.what()));
  }
}

bool TileManager::SetTileMode(uint64_t surfaceId, uint32_t tileMode) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("SetTileMode: Invalid surface ID: {}", surfaceId);
      throw TileManagerException("Invalid surface ID");
    }

    // Validate tile mode
    if (tileMode > static_cast<uint32_t>(TileMode::TILED_DEPTH)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("SetTileMode: Invalid tile mode: {}", tileMode);
      throw TileManagerException("Invalid tile mode");
    }

    TileInfo &info = it->second.info;
    TileMode oldMode = info.mode;
    TileMode newMode = static_cast<TileMode>(tileMode);

    // If mode hasn't changed, just return success
    if (oldMode == newMode) {
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "SetTileMode: Mode already set for surface {}, mode={}, latency={}us",
          surfaceId, tileMode, latency);
      return true;
    }

    // Create a copy of the current data in linear format
    // This is crucial for re-tiling without data loss.
    std::vector<uint8_t> linearData(info.width * info.height * info.depth *
                                    info.bytesPerPixel);
    // Temporarily release lock for TiledToLinear to avoid deadlock if it needs
    // to acquire other locks (e.g., in CommandProcessor notification).
    lock.unlock();
    TiledToLinear(surfaceId, linearData.data(), linearData.size());
    lock.lock(); // Re-acquire lock

    // Update tile mode
    info.mode = newMode;

    // Recalculate layout with new mode
    CalculateTileLayout(info);

    // Resize data buffer if needed. This might change if the new tiling mode
    // requires different padding or alignment, leading to a different total size.
    size_t newSize = info.slice * info.depth;
    if (it->second.data.size() != newSize) {
      it->second.data.resize(newSize, 0); // Resize and zero-fill new parts
    }

    // Convert linear data back to the new tile format
    // Temporarily release lock for LinearToTiled.
    lock.unlock();
    LinearToTiled(surfaceId, linearData.data(), linearData.size());
    lock.lock(); // Re-acquire lock

    // Update cache
    SurfaceCacheEntry cacheEntry{surfaceId, it->second.data, 0, 0};
    m_surfaceCache[surfaceId] = cacheEntry;

    info.cacheHits++;
    it->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;

    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    spdlog::info(
        "SetTileMode: Updated surface {} tile mode from {} to {}, latency={}us",
        surfaceId, static_cast<int>(oldMode), tileMode, latency);

    // Notify CommandProcessor of surface update
    m_commandProcessor.NotifySurfaceUpdated(surfaceId, 0, 0, info.width,
                                            info.height);

    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetTileMode failed for surface {}: {}", surfaceId, e.what());
    throw TileManagerException("SetTileMode failed: " + std::string(e.what()));
  }
}

uint64_t TileManager::MapTiledMemory(uint64_t cpuAddress, size_t size,
                                     uint32_t tileMode) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex); // Use m_surfaceMutex for surface operations

  try {
    spdlog::info("Mapping tiled memory: cpu=0x{:x}, size={}, tileMode={}",
                 cpuAddress, size, tileMode);

    // Validate input parameters
    if (size == 0) {
      throw TileManagerException(
          "Invalid size (zero) for tiled memory mapping");
    }

    if (tileMode > static_cast<uint32_t>(TileMode::TILED_DEPTH)) {
      throw TileManagerException("Invalid tile mode for memory mapping");
    }

    // Use the CPU address as the surface ID for direct mapping.
    // This assumes CPU addresses are unique and can serve as IDs.
    uint64_t surfaceId = cpuAddress;

    // Check if a surface with this ID already exists and is active
    if (m_surfaces.count(surfaceId) && m_surfaces[surfaceId].active) {
        spdlog::warn("MapTiledMemory: Surface with ID 0x{:x} already exists and is active. "
                     "Returning existing surface ID.", surfaceId);
        // Optionally, update its properties or throw an error if re-mapping is not allowed.
        // For now, we'll just return the existing ID.
        return surfaceId;
    }

    // Calculate dimensions based on size
    // For simplicity, we'll use a square layout that can hold the data
    // A more robust implementation would consider actual memory allocation
    // patterns and alignment requirements.
    uint32_t bytesPerPixel = 4; // Default to RGBA (4 bytes) for generic memory
    // Try to infer bpp from tileMode if it implies a specific format,
    // or allow it to be passed as a parameter. For now, fixed to 4.

    uint32_t pixelCount =
        static_cast<uint32_t>((size + bytesPerPixel - 1) / bytesPerPixel);
    uint32_t dimension =
        static_cast<uint32_t>(std::ceil(std::sqrt(pixelCount)));

    // Round up dimensions to be multiples of TILE_WIDTH/HEIGHT
    // This ensures that the surface can be perfectly divided into tiles.
    dimension = ((dimension + TILE_WIDTH - 1) / TILE_WIDTH) * TILE_WIDTH;
    if (dimension == 0) dimension = TILE_WIDTH; // Ensure minimum dimension

    // Create the surface with appropriate dimensions
    TiledSurface surface;
    surface.info.width = dimension;
    surface.info.height = dimension;
    surface.info.depth = 1; // Assuming 2D mapping for now
    surface.info.mode = static_cast<TileMode>(tileMode);
    surface.info.format = TileFormat::R8G8B8A8_UNORM; // Default format for generic memory
    surface.info.bytesPerPixel = bytesPerPixel;
    surface.info.gpuAddr = surfaceId; // GPU address is the surface ID
    surface.info.cpuAddr = cpuAddress;
    surface.info.isRenderTarget = false;
    surface.info.isDepthStencil = false;
    surface.info.cacheHits = 0;
    surface.info.cacheMisses = 0;
    surface.cacheHits = 0;
    surface.cacheMisses = 0;

    // Calculate layout based on the tile mode
    CalculateTileLayout(surface.info);

    // Allocate memory for the surface. The actual allocated size might be
    // larger than 'size' due to tiling alignment.
    size_t totalAllocatedSize = surface.info.slice * surface.info.depth;
    surface.data.resize(totalAllocatedSize, 0); // Initialize with zeros
    surface.active = true;

    // Store the surface
    m_surfaces[surfaceId] = std::move(surface);

    // Cache the surface data
    SurfaceCacheEntry cacheEntry{surfaceId, m_surfaces[surfaceId].data, 0, 0};
    m_surfaceCache[surfaceId] = cacheEntry;

    m_stats.operationCount++;
    m_stats.cacheHits++; // Consider this a cache hit for the operation

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    spdlog::info("Tiled memory mapped: cpu=0x{:x} -> gpu=0x{:x}, requested_size={}, "
                 "allocated_size={}, tileMode={}, latency={}us",
                 cpuAddress, surfaceId, size, totalAllocatedSize, tileMode, latency);

    // Notify CommandProcessor about the new memory mapping
    m_commandProcessor.NotifyMemoryMapped(cpuAddress, surfaceId, size, tileMode);

    // Return the GPU address (which is the same as the surface ID in this
    // implementation)
    return surfaceId;

  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("MapTiledMemory failed for cpuAddress 0x{:x}: {}", cpuAddress, e.what());
    throw TileManagerException("MapTiledMemory failed: " +
                               std::string(e.what()));
  }
}

} // namespace ps4
