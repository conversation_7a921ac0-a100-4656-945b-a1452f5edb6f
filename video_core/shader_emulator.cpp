#include "shader_emulator.h"
#include "../debug/vector_debug.h"
#include "command_processor.h"
#include "gcn_types.h"
#include "gnm_shader_translator.h"
#include "gnm_state.h"
#include "tile_manager.h"
#include <algorithm>
#include <chrono>
#include <cmath>
#include <cstring>
#include <shared_mutex>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <unordered_map>
#include <vector>

#if __cplusplus >= 202002L
#include <bit>
#endif

namespace ps4 {
class ShaderEmulator::Impl {
public:
  Impl(GNMRegisterState &gnmState, TileManager &tileManager,
       GNMShaderTranslator &shaderTranslator,
       CommandProcessor &commandProcessor)
      : m_gnmState(gnmState), m_tileManager(tileManager),
        m_shaderTranslator(shaderTranslator),
        m_commandProcessor(commandProcessor),
        m_shaderType(GCNShaderType::PIXEL), m_pc(0), m_renderTarget(0),
        m_instructionCount(0), m_stats{} {
    m_stats = {};
    InitializeRegisters();
    InitializeInstructionTable();
    spdlog::info(
        "ShaderEmulator::Impl constructed with zero-initialized stats");
  }

  ~Impl() {
    Shutdown();
    spdlog::info("ShaderEmulator::Impl destroyed");
  }

  bool Initialize() {
    auto start = std::chrono::high_resolution_clock::now();
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    try {
      ResetRegisters();
      m_instructionCache.clear();
      m_stats = ShaderEmulatorStats();
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      m_stats.cacheHits++;
      spdlog::info("ShaderEmulator::Impl initialized, latency={}us", latency);
      return true;
    } catch (const std::exception &e) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ShaderEmulator::Impl initialization failed: {}", e.what());
      throw ShaderEmulatorException("Initialization failed: " +
                                    std::string(e.what()));
    }
  }

  void Shutdown() {
    auto start = std::chrono::high_resolution_clock::now();
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    try {
      m_bytecode.clear();
      m_scalarRegs.clear();
      m_vectorRegs.clear();
      m_memory.clear();
      m_instructionTable.clear();
      m_instructionCache.clear();
      m_stats = ShaderEmulatorStats();
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      m_stats.cacheHits++;
      spdlog::info("ShaderEmulator::Impl shutdown, latency={}us", latency);
    } catch (const std::exception &e) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ShaderEmulator::Impl shutdown failed: {}", e.what());
    }
  }

  bool LoadShader(const std::vector<uint32_t> &bytecode, GCNShaderType type) {
    auto start = std::chrono::high_resolution_clock::now();
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    try {
      if (bytecode.empty()) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::error("LoadShader: Empty bytecode");
        throw ShaderEmulatorException("Empty shader bytecode");
      }

      std::vector<uint32_t> spirvCode;
      std::string glslCode;
      uint64_t bytecodeHash = 0;
      for (const auto &word : bytecode) {
        bytecodeHash ^= std::hash<uint32_t>{}(word);
      }
      lock.unlock();
      if (!m_shaderTranslator.GetCachedShader(bytecodeHash, type, spirvCode,
                                              glslCode)) {
        spirvCode = m_shaderTranslator.TranslateToSPIRV(bytecode, type);
      }
      lock.lock();

      m_bytecode = bytecode;
      m_shaderType = type;
      m_pc = 0;
      ResetRegisters();
      m_instructionCount = 0;
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("LoadShader: Loaded shader, type={}, size={} bytes, "
                   "hash=0x{:x}, latency={}us",
                   static_cast<int>(type), bytecode.size() * 4, bytecodeHash,
                   latency);
      return true;
    } catch (const std::exception &e) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("LoadShader failed: {}", e.what());
      throw ShaderEmulatorException("LoadShader failed: " +
                                    std::string(e.what()));
    }
  }

  bool Execute() {
    auto start = std::chrono::high_resolution_clock::now();
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    try {
      if (m_bytecode.empty()) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::error("Execute: No shader bytecode loaded");
        throw ShaderEmulatorException("No shader bytecode loaded");
      }

      uint32_t maxInstructions = 100000;
      uint32_t executedInstructions = 0;

      while (m_pc < m_bytecode.size() &&
             executedInstructions < maxInstructions) {
        GCNInstruction instr;
        uint32_t current_pc = m_pc;
        // CRITICAL: Bounds check already done in while condition, but add safe
        // access
        uint32_t instruction = CRITICAL_VECTOR_ACCESS(
            m_bytecode, m_pc, "ShaderEmulator bytecode decode");
        if (!DecodeInstruction(instruction, instr)) {
          m_stats.errorCount++;
          m_stats.cacheMisses++;
          spdlog::error("Execute: Failed to decode instruction at PC {}", m_pc);
          throw ShaderEmulatorException("Instruction decode failed");
        }

        // CRITICAL: Use already validated instruction for hash
        uint64_t instrHash = std::hash<uint32_t>{}(instruction) ^
                             (static_cast<uint64_t>(m_pc) << 32);
        std::vector<uint32_t> cachedResults;

        lock.unlock();
        bool cacheHit = GetCachedInstructionResult(instrHash, cachedResults);
        lock.lock();

        if (cacheHit && cachedResults.size() <= m_scalarRegs.size()) {
          for (size_t i = 0; i < cachedResults.size(); ++i) {
            // CRITICAL: Bounds check for scalar register access
            if (i >= m_scalarRegs.size()) {
              spdlog::error("ShaderEmulator: Cache restore index {} >= "
                            "m_scalarRegs.size() {}",
                            i, m_scalarRegs.size());
              break;
            }
            CRITICAL_VECTOR_ACCESS(m_scalarRegs, i,
                                   "ShaderEmulator cache restore") =
                CRITICAL_VECTOR_ACCESS(cachedResults, i,
                                       "ShaderEmulator cached results");
          }
          m_stats.cacheHits++;
        } else {
          uint32_t oldPC = m_pc;
          if (!ExecuteInstruction(instr)) {
            m_stats.errorCount++;
            m_stats.cacheMisses++;
            spdlog::error("Execute: Failed to execute instruction at PC {}",
                          oldPC);
            throw ShaderEmulatorException("Instruction execution failed");
          }

          if (instr.opcode != 0xA0) {
            InstructionCacheEntry cacheEntry{
                instr, std::vector<uint32_t>(m_scalarRegs.begin(),
                                             m_scalarRegs.end())};
            m_instructionCache[instrHash] = std::move(cacheEntry);
          }
        }

        if (m_pc == current_pc) {
          m_pc++;
        }

        executedInstructions++;
        m_instructionCount++;

        if (instr.opcode == 0xBF810000) {
          spdlog::debug("Execute: Program termination instruction encountered");
          break;
        }
      }

      if (executedInstructions >= maxInstructions) {
        spdlog::warn("Execute: Maximum instruction limit reached, possible "
                     "infinite loop");
        m_stats.errorCount++;
      }

      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "Execute: Completed shader execution, instructions={}, latency={}us",
          executedInstructions, latency);

      if (m_shaderExecutionCallback) {
        lock.unlock();
        m_shaderExecutionCallback(m_shaderType, executedInstructions);
        lock.lock();
      }
      return true;
    } catch (const std::exception &e) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("Execute failed: {}", e.what());
      throw ShaderEmulatorException("Execute failed: " + std::string(e.what()));
    }
  }

  bool GetCachedInstructionResult(uint64_t instructionHash,
                                  std::vector<uint32_t> &results) const {
    auto start = std::chrono::high_resolution_clock::now();
    std::shared_lock<std::shared_mutex> lock(m_emulatorMutex);
    try {
      auto it = m_instructionCache.find(instructionHash);
      if (it != m_instructionCache.end()) {
        results = it->second.results;
        it->second.cacheHits++;
        m_stats.cacheHits++;
        auto end = std::chrono::high_resolution_clock::now();
        auto latency =
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count();
        m_stats.totalLatencyUs += latency;
        spdlog::trace(
            "GetCachedInstructionResult: hash=0x{:x}, hit, latency={}us",
            instructionHash, latency);
        return true;
      }
      m_stats.cacheMisses++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace(
          "GetCachedInstructionResult: hash=0x{:x}, miss, latency={}us",
          instructionHash, latency);
      return false;
    } catch (const std::exception &e) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("GetCachedInstructionResult failed: {}", e.what());
      return false;
    }
  }

  void ClearInstructionCache() {
    auto start = std::chrono::high_resolution_clock::now();
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    try {
      m_instructionCache.clear();
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("ClearInstructionCache: Cleared cache, latency={}us",
                    latency);
    } catch (const std::exception &e) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ClearInstructionCache failed: {}", e.what());
    }
  }

  ShaderEmulatorStats GetStats() const {
    auto start = std::chrono::high_resolution_clock::now();
    std::shared_lock<std::shared_mutex> lock(m_emulatorMutex);
    try {
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetStats: latency={}us", latency);
      return m_stats;
    } catch (const std::exception &e) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("GetStats failed: {}", e.what());
      return m_stats;
    }
  }

  void SetShaderExecutionCallback(const ShaderExecutionCallback &callback) {
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    m_shaderExecutionCallback = callback;
    spdlog::debug("ShaderEmulator: Shader execution callback set");
  }

  void SaveState(std::ostream &out) const {
    auto start = std::chrono::high_resolution_clock::now();
    std::shared_lock<std::shared_mutex> lock(m_emulatorMutex);
    try {
      uint32_t version = 1;
      out.write(reinterpret_cast<const char *>(&version), sizeof(version));

      uint64_t bytecodeSize = m_bytecode.size();
      out.write(reinterpret_cast<const char *>(&bytecodeSize),
                sizeof(bytecodeSize));
      out.write(reinterpret_cast<const char *>(m_bytecode.data()),
                bytecodeSize * sizeof(uint32_t));
      out.write(reinterpret_cast<const char *>(&m_shaderType),
                sizeof(m_shaderType));
      out.write(reinterpret_cast<const char *>(&m_pc), sizeof(m_pc));
      out.write(reinterpret_cast<const char *>(&m_renderTarget),
                sizeof(m_renderTarget));
      out.write(reinterpret_cast<const char *>(m_scalarRegs.data()),
                m_scalarRegs.size() * sizeof(uint32_t));
      out.write(reinterpret_cast<const char *>(m_vectorRegs.data()),
                m_vectorRegs.size() * sizeof(float));
      out.write(reinterpret_cast<const char *>(m_memory.data()),
                m_memory.size() * sizeof(uint8_t));

      uint64_t cacheCount = m_instructionCache.size();
      out.write(reinterpret_cast<const char *>(&cacheCount),
                sizeof(cacheCount));
      for (const auto &[hash, entry] : m_instructionCache) {
        out.write(reinterpret_cast<const char *>(&hash), sizeof(hash));
        out.write(reinterpret_cast<const char *>(&entry.cacheHits),
                  sizeof(entry.cacheHits));
        out.write(reinterpret_cast<const char *>(&entry.cacheMisses),
                  sizeof(entry.cacheMisses));
      }

      out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));

      if (!out.good()) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        throw ShaderEmulatorException("Failed to write shader emulator state");
      }

      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("SaveState: Saved shader emulator state, bytecode_size={}, "
                   "cache_count={}, latency={}us",
                   bytecodeSize, cacheCount, latency);
    } catch (const std::exception &e) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("SaveState failed: {}", e.what());
      throw ShaderEmulatorException("SaveState failed: " +
                                    std::string(e.what()));
    }
  }

  void LoadState(std::istream &in) {
    auto start = std::chrono::high_resolution_clock::now();
    std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
    try {
      uint32_t version;
      in.read(reinterpret_cast<char *>(&version), sizeof(version));
      if (!in.good() || version != 1) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        throw ShaderEmulatorException("Invalid or unsupported state version");
      }

      uint64_t bytecodeSize;
      in.read(reinterpret_cast<char *>(&bytecodeSize), sizeof(bytecodeSize));
      if (!in.good() || bytecodeSize > 1024 * 1024) {
        throw ShaderEmulatorException("Invalid bytecode size");
      }

      m_bytecode.resize(bytecodeSize);
      if (bytecodeSize > 0) {
        in.read(reinterpret_cast<char *>(m_bytecode.data()),
                bytecodeSize * sizeof(uint32_t));
        if (!in.good()) {
          throw ShaderEmulatorException("Failed to read bytecode");
        }
      }

      in.read(reinterpret_cast<char *>(&m_shaderType), sizeof(m_shaderType));
      in.read(reinterpret_cast<char *>(&m_pc), sizeof(m_pc));
      in.read(reinterpret_cast<char *>(&m_renderTarget),
              sizeof(m_renderTarget));
      in.read(reinterpret_cast<char *>(m_scalarRegs.data()),
              m_scalarRegs.size() * sizeof(uint32_t));
      in.read(reinterpret_cast<char *>(m_vectorRegs.data()),
              m_vectorRegs.size() * sizeof(float));
      in.read(reinterpret_cast<char *>(m_memory.data()),
              m_memory.size() * sizeof(uint8_t));
      if (!in.good()) {
        throw ShaderEmulatorException("Failed to read state data");
      }

      m_instructionCache.clear();
      uint64_t cacheCount;
      in.read(reinterpret_cast<char *>(&cacheCount), sizeof(cacheCount));
      if (!in.good() || cacheCount > 100000) {
        throw ShaderEmulatorException("Invalid cache count");
      }

      for (uint64_t i = 0; i < cacheCount; ++i) {
        uint64_t hash;
        InstructionCacheEntry entry;
        in.read(reinterpret_cast<char *>(&hash), sizeof(hash));
        uint64_t resultsSize;
        in.read(reinterpret_cast<char *>(&resultsSize), sizeof(resultsSize));
        if (!in.good() || resultsSize > 1024) {
          spdlog::warn(
              "LoadState: Skipping invalid cache entry {} with size {}", i,
              resultsSize);
          continue;
        }

        entry.results.resize(resultsSize);
        if (resultsSize > 0) {
          in.read(reinterpret_cast<char *>(entry.results.data()),
                  resultsSize * sizeof(uint32_t));
        }
        in.read(reinterpret_cast<char *>(&entry.cacheHits),
                sizeof(entry.cacheHits));
        in.read(reinterpret_cast<char *>(&entry.cacheMisses),
                sizeof(entry.cacheMisses));
        if (!in.good()) {
          spdlog::warn(
              "LoadState: Failed to read cache entry {}, stopping cache load",
              i);
          break;
        }
        m_instructionCache[hash] = std::move(entry);
      }

      in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
      if (!in.good()) {
        spdlog::warn("LoadState: Failed to read stats, using defaults");
        m_stats = ShaderEmulatorStats{};
      }

      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info("LoadState: Loaded shader emulator state, bytecode_size={}, "
                   "cache_count={}, latency={}us",
                   bytecodeSize, m_instructionCache.size(), latency);
    } catch (const std::exception &e) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("LoadState failed: {}", e.what());
      throw ShaderEmulatorException("LoadState failed: " +
                                    std::string(e.what()));
    }
  }

private:
  GNMRegisterState &m_gnmState;
  TileManager &m_tileManager;
  GNMShaderTranslator &m_shaderTranslator;
  CommandProcessor &m_commandProcessor;
  std::vector<uint32_t> m_bytecode;
  GCNShaderType m_shaderType;
  uint32_t m_pc;
  uint64_t m_renderTarget;
  std::vector<uint32_t> m_scalarRegs;
  std::vector<float> m_vectorRegs;
  std::vector<uint8_t> m_memory;
  std::unordered_map<uint32_t, std::function<bool(const GCNInstruction &)>>
      m_instructionTable;
  mutable std::unordered_map<uint64_t, InstructionCacheEntry>
      m_instructionCache;
  mutable std::shared_mutex m_emulatorMutex;
  uint64_t m_instructionCount;
  mutable ShaderEmulatorStats m_stats;
  ShaderExecutionCallback m_shaderExecutionCallback;

  bool ExecuteSMovB32(const GCNInstruction &instr);
  bool ExecuteSAddU32(const GCNInstruction &instr);
  bool ExecuteSSubU32(const GCNInstruction &instr);
  bool ExecuteSMulU32(const GCNInstruction &instr);
  bool ExecuteSAndB32(const GCNInstruction &instr);
  bool ExecuteSOrB32(const GCNInstruction &instr);
  bool ExecuteSXorB32(const GCNInstruction &instr);
  bool ExecuteSLshlB32(const GCNInstruction &instr);
  bool ExecuteSLshrB32(const GCNInstruction &instr);
  bool ExecuteSAshrI32(const GCNInstruction &instr);
  bool ExecuteSCmpEqU32(const GCNInstruction &instr);
  bool ExecuteVMovB32(const GCNInstruction &instr);
  bool ExecuteVAddF32(const GCNInstruction &instr);
  bool ExecuteVSubF32(const GCNInstruction &instr);
  bool ExecuteVMulF32(const GCNInstruction &instr);
  bool ExecuteVMadF32(const GCNInstruction &instr);
  bool ExecuteVMinF32(const GCNInstruction &instr);
  bool ExecuteVMaxF32(const GCNInstruction &instr);
  bool ExecuteVCmpEqF32(const GCNInstruction &instr);
  bool ExecuteVCmpGtF32(const GCNInstruction &instr);
  bool ExecuteBufferLoadDword(const GCNInstruction &instr);
  bool ExecuteBufferStoreDword(const GCNInstruction &instr);
  bool ExecuteFlatLoadDword(const GCNInstruction &instr);
  bool ExecuteFlatStoreDword(const GCNInstruction &instr);
  bool ExecuteSBranch(const GCNInstruction &instr);
  bool ExecuteSCbranchScc0(const GCNInstruction &instr);
  bool ExecuteSCbranchVccz(const GCNInstruction &instr);
  bool ExecuteSEndpgm(const GCNInstruction &instr);
  bool ExecuteVCvtF32U32(const GCNInstruction &instr);
  bool ExecuteVCvtU32F32(const GCNInstruction &instr);

  void InitializeRegisters();
  void ResetRegisters();
  void InitializeInstructionTable();
  bool DecodeInstruction(uint32_t word, GCNInstruction &instr);
  bool ExecuteInstruction(const GCNInstruction &instr);

  template <class To, class From>
  std::enable_if_t<sizeof(To) == sizeof(From) &&
                       std::is_trivially_copyable_v<From> &&
                       std::is_trivially_copyable_v<To>,
                   To>
  bit_cast(const From &src) noexcept {
#if __cplusplus >= 202002L
    return std::bit_cast<To>(src);
#else
    To dst;
    std::memcpy(&dst, &src, sizeof(To));
    return dst;
#endif
  }
};

// Handler function implementations
bool ShaderEmulator::Impl::ExecuteSMovB32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    if (instr.dst >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteSMovB32: Invalid destination register index: dst={}",
          instr.dst);
      return false;
    }
    uint32_t srcValue;
    if (instr.imm != 0) {
      srcValue = instr.imm;
    } else {
      if (instr.src0 >= m_scalarRegs.size()) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::error("ExecuteSMovB32: Invalid source register index: src={}",
                      instr.src0);
        return false;
      }
      if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                        instr.src0, srcValue)) {
        srcValue = m_gnmState.GetShaderRegister(stage, instr.src0);
      }
    }
    SAFE_VECTOR_ACCESS(m_scalarRegs, instr.dst, "ExecuteSMovB32 dst") = srcValue;
    if (instr.predicated) {
      if (instr.predicate < m_scalarRegs.size()) {
        SAFE_VECTOR_ACCESS(m_scalarRegs, instr.predicate, "ExecuteSMovB32 predicate") = 1;
      }
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, SAFE_VECTOR_ACCESS(m_scalarRegs, instr.dst, "ExecuteSMovB32 dst read"));
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSMovB32: s{} = {}, latency={}us", instr.dst,
                  (instr.imm != 0) ? std::to_string(srcValue)
                                   : "s" + std::to_string(instr.src0),
                  latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSMovB32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSAddU32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    if (instr.dst >= m_scalarRegs.size() || instr.src0 >= m_scalarRegs.size() ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteSAddU32: Invalid register index: dst={}, src0={}, src1={}",
          instr.dst, instr.src0, instr.src1);
      return false;
    }
    uint32_t src0Value, src1Value;
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src0, src0Value)) {
      src0Value = m_gnmState.GetShaderRegister(stage, instr.src0);
    }
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src1, src1Value)) {
      src1Value = m_gnmState.GetShaderRegister(stage, instr.src1);
    }
    uint64_t result = static_cast<uint64_t>(src0Value) + src1Value;
    SAFE_VECTOR_ACCESS(m_scalarRegs, instr.dst, "ExecuteSAddU32 dst") = static_cast<uint32_t>(result);
    if (instr.predicated) {
      if (instr.predicate < m_scalarRegs.size()) {
        SAFE_VECTOR_ACCESS(m_scalarRegs, instr.predicate, "ExecuteSAddU32 predicate") = (result > 0xFFFFFFFF) ? 1 : 0;
      }
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, SAFE_VECTOR_ACCESS(m_scalarRegs, instr.dst, "ExecuteSAddU32 dst read"));
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSAddU32: s{} = s{} + s{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSAddU32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSSubU32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    if (instr.dst >= m_scalarRegs.size() || instr.src0 >= m_scalarRegs.size() ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteSSubU32: Invalid register index: dst={}, src0={}, src1={}",
          instr.dst, instr.src0, instr.src1);
      return false;
    }
    uint32_t src0Value, src1Value;
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src0, src0Value)) {
      src0Value = m_gnmState.GetShaderRegister(stage, instr.src0);
    }
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src1, src1Value)) {
      src1Value = m_gnmState.GetShaderRegister(stage, instr.src1);
    }
    uint64_t result = static_cast<uint64_t>(src0Value) - src1Value;
    SAFE_VECTOR_ACCESS(m_scalarRegs, instr.dst, "ExecuteSSubU32 dst") = static_cast<uint32_t>(result);
    if (instr.predicated) {
      if (instr.predicate < m_scalarRegs.size()) {
        SAFE_VECTOR_ACCESS(m_scalarRegs, instr.predicate, "ExecuteSSubU32 predicate") = (src0Value < src1Value) ? 1 : 0;
      }
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, SAFE_VECTOR_ACCESS(m_scalarRegs, instr.dst, "ExecuteSSubU32 dst read"));
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSSubU32: s{} = s{} - s{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSSubU32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSMulU32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    if (instr.dst >= m_scalarRegs.size() || instr.src0 >= m_scalarRegs.size() ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteSMulU32: Invalid register index: dst={}, src0={}, src1={}",
          instr.dst, instr.src0, instr.src1);
      return false;
    }
    uint32_t src0Value, src1Value;
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src0, src0Value)) {
      src0Value = m_gnmState.GetShaderRegister(stage, instr.src0);
    }
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src1, src1Value)) {
      src1Value = m_gnmState.GetShaderRegister(stage, instr.src1);
    }
    uint64_t result = static_cast<uint64_t>(src0Value) * src1Value;
    m_scalarRegs[instr.dst] = static_cast<uint32_t>(result);
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = (result > 0xFFFFFFFF) ? 1 : 0;
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, m_scalarRegs[instr.dst]);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSMulU32: s{} = s{} * s{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSMulU32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSAndB32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    if (instr.dst >= m_scalarRegs.size() || instr.src0 >= m_scalarRegs.size() ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteSAndB32: Invalid register index: dst={}, src0={}, src1={}",
          instr.dst, instr.src0, instr.src1);
      return false;
    }
    uint32_t src0Value, src1Value;
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src0, src0Value)) {
      src0Value = m_gnmState.GetShaderRegister(stage, instr.src0);
    }
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src1, src1Value)) {
      src1Value = m_gnmState.GetShaderRegister(stage, instr.src1);
    }
    m_scalarRegs[instr.dst] = src0Value & src1Value;
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = (m_scalarRegs[instr.dst] == 0) ? 0 : 1;
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, m_scalarRegs[instr.dst]);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSAndB32: s{} = s{} & s{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSAndB32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSOrB32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    if (instr.dst >= m_scalarRegs.size() || instr.src0 >= m_scalarRegs.size() ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteSOrB32: Invalid register index: dst={}, src0={}, src1={}",
          instr.dst, instr.src0, instr.src1);
      return false;
    }
    uint32_t src0Value, src1Value;
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src0, src0Value)) {
      src0Value = m_gnmState.GetShaderRegister(stage, instr.src0);
    }
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src1, src1Value)) {
      src1Value = m_gnmState.GetShaderRegister(stage, instr.src1);
    }
    m_scalarRegs[instr.dst] = src0Value | src1Value;
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = (m_scalarRegs[instr.dst] == 0) ? 0 : 1;
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, m_scalarRegs[instr.dst]);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSOrB32: s{} = s{} | s{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSOrB32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSXorB32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    if (instr.dst >= m_scalarRegs.size() || instr.src0 >= m_scalarRegs.size() ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteSXorB32: Invalid register index: dst={}, src0={}, src1={}",
          instr.dst, instr.src0, instr.src1);
      return false;
    }
    uint32_t src0Value, src1Value;
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src0, src0Value)) {
      src0Value = m_gnmState.GetShaderRegister(stage, instr.src0);
    }
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src1, src1Value)) {
      src1Value = m_gnmState.GetShaderRegister(stage, instr.src1);
    }
    m_scalarRegs[instr.dst] = src0Value ^ src1Value;
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = (m_scalarRegs[instr.dst] == 0) ? 0 : 1;
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, m_scalarRegs[instr.dst]);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSXorB32: s{} = s{} ^ s{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSXorB32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSLshlB32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    if (instr.dst >= m_scalarRegs.size() || instr.src0 >= m_scalarRegs.size() ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteSLshlB32: Invalid register index: dst={}, src0={}, src1={}",
          instr.dst, instr.src0, instr.src1);
      return false;
    }
    uint32_t src0Value, src1Value;
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src0, src0Value)) {
      src0Value = m_gnmState.GetShaderRegister(stage, instr.src0);
    }
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src1, src1Value)) {
      src1Value = m_gnmState.GetShaderRegister(stage, instr.src1);
    }
    m_scalarRegs[instr.dst] = src0Value << (src1Value & 0x1F); // Mask to 5 bits
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = (m_scalarRegs[instr.dst] == 0) ? 0 : 1;
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, m_scalarRegs[instr.dst]);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSLshlB32: s{} = s{} << s{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSLshlB32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSLshrB32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    if (instr.dst >= m_scalarRegs.size() || instr.src0 >= m_scalarRegs.size() ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteSLshrB32: Invalid register index: dst={}, src0={}, src1={}",
          instr.dst, instr.src0, instr.src1);
      return false;
    }
    uint32_t src0Value, src1Value;
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src0, src0Value)) {
      src0Value = m_gnmState.GetShaderRegister(stage, instr.src0);
    }
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src1, src1Value)) {
      src1Value = m_gnmState.GetShaderRegister(stage, instr.src1);
    }
    m_scalarRegs[instr.dst] =
        src0Value >> (src1Value & 0x1F); // Logical shift right
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = (m_scalarRegs[instr.dst] == 0) ? 0 : 1;
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, m_scalarRegs[instr.dst]);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSLshrB32: s{} = s{} >> s{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSLshrB32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSAshrI32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    if (instr.dst >= m_scalarRegs.size() || instr.src0 >= m_scalarRegs.size() ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteSAshrI32: Invalid register index: dst={}, src0={}, src1={}",
          instr.dst, instr.src0, instr.src1);
      return false;
    }
    uint32_t src0Value, src1Value;
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src0, src0Value)) {
      src0Value = m_gnmState.GetShaderRegister(stage, instr.src0);
    }
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src1, src1Value)) {
      src1Value = m_gnmState.GetShaderRegister(stage, instr.src1);
    }
    int32_t signedSrc = static_cast<int32_t>(src0Value);
    m_scalarRegs[instr.dst] = static_cast<uint32_t>(
        signedSrc >> (src1Value & 0x1F)); // Arithmetic shift right
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = (m_scalarRegs[instr.dst] == 0) ? 0 : 1;
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, m_scalarRegs[instr.dst]);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSAshrI32: s{} = s{} >> s{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSAshrI32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSCmpEqU32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    if (instr.dst >= m_scalarRegs.size() || instr.src0 >= m_scalarRegs.size() ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteSCmpEqU32: Invalid register index: dst={}, src0={}, src1={}",
          instr.dst, instr.src0, instr.src1);
      return false;
    }
    uint32_t src0Value, src1Value;
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src0, src0Value)) {
      src0Value = m_gnmState.GetShaderRegister(stage, instr.src0);
    }
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src1, src1Value)) {
      src1Value = m_gnmState.GetShaderRegister(stage, instr.src1);
    }
    m_scalarRegs[instr.dst] = (src0Value == src1Value) ? 1 : 0;
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = m_scalarRegs[instr.dst];
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, m_scalarRegs[instr.dst]);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSCmpEqU32: s{} = s{} == s{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSCmpEqU32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteVMovB32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    const uint32_t max_vector_regs = m_vectorRegs.size() / 4;
    if (instr.dst >= max_vector_regs) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteVMovB32: Invalid destination register index: dst={} (max={})",
          instr.dst, max_vector_regs);
      return false;
    }
    uint32_t srcValue;
    if (instr.src0 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ExecuteVMovB32: Invalid source register index: src={}",
                    instr.src0);
      return false;
    }
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG, stage,
                                      instr.src0, srcValue)) {
      srcValue = m_gnmState.GetShaderRegister(stage, instr.src0);
    }
    m_vectorRegs[instr.dst * 4] = bit_cast<float>(srcValue);
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = 1;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteVMovB32: v{} = s{}, latency={}us", instr.dst,
                  instr.src0, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteVMovB32 failed: {}", e.what());
    return false;
  }
}



bool ShaderEmulator::Impl::ExecuteVAddF32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    const uint32_t max_vector_regs = m_vectorRegs.size() / 4;
    if (instr.dst >= max_vector_regs ||
        instr.src0 >= max_vector_regs ||
        instr.src1 >= max_vector_regs) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteVAddF32: Invalid register index: dst={}, src0={}, src1={} (max={})",
          instr.dst, instr.src0, instr.src1, max_vector_regs);
      return false;
    }
    float result = SAFE_VECTOR_ACCESS(m_vectorRegs, instr.src0 * 4, "ExecuteVAddF32 src0") +
                   SAFE_VECTOR_ACCESS(m_vectorRegs, instr.src1 * 4, "ExecuteVAddF32 src1");
    SAFE_VECTOR_ACCESS(m_vectorRegs, instr.dst * 4, "ExecuteVAddF32 dst") = result;
    if (instr.predicated) {
      if (instr.predicate < m_scalarRegs.size()) {
        SAFE_VECTOR_ACCESS(m_scalarRegs, instr.predicate, "ExecuteVAddF32 predicate") =
            (std::isnan(result) || std::isinf(result)) ? 1 : 0;
      }
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteVAddF32: v{} = v{} + v{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteVAddF32 failed: {}", e.what());
    return false;
  }
}



bool ShaderEmulator::Impl::ExecuteVSubF32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    const uint32_t max_vector_regs = m_vectorRegs.size() / 4;
    if (instr.dst >= max_vector_regs ||
        instr.src0 >= max_vector_regs ||
        instr.src1 >= max_vector_regs) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteVSubF32: Invalid register index: dst={}, src0={}, src1={} (max={})",
          instr.dst, instr.src0, instr.src1, max_vector_regs);
      return false;
    }
    float result = m_vectorRegs[instr.src0 * 4] - m_vectorRegs[instr.src1 * 4];
    m_vectorRegs[instr.dst * 4] = result;
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] =
          (std::isnan(result) || std::isinf(result)) ? 1 : 0;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteVSubF32: v{} = v{} - v{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteVSubF32 failed: {}", e.what());
    return false;
  }
}



bool ShaderEmulator::Impl::ExecuteVMulF32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    const uint32_t max_vector_regs = m_vectorRegs.size() / 4;
    if (instr.dst >= max_vector_regs ||
        instr.src0 >= max_vector_regs ||
        instr.src1 >= max_vector_regs) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteVMulF32: Invalid register index: dst={}, src0={}, src1={} (max={})",
          instr.dst, instr.src0, instr.src1, max_vector_regs);
      return false;
    }
    float result = m_vectorRegs[instr.src0 * 4] * m_vectorRegs[instr.src1 * 4];
    m_vectorRegs[instr.dst * 4] = result;
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] =
          (std::isnan(result) || std::isinf(result)) ? 1 : 0;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteVMulF32: v{} = v{} * v{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteVMulF32 failed: {}", e.what());
    return false;
  }
}



bool ShaderEmulator::Impl::ExecuteVMadF32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    const uint32_t max_vector_regs = m_vectorRegs.size() / 4;
    if (instr.dst >= max_vector_regs ||
        instr.src0 >= max_vector_regs ||
        instr.src1 >= max_vector_regs ||
        instr.src2 >= max_vector_regs) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ExecuteVMadF32: Invalid register index: dst={}, src0={}, "
                    "src1={}, src2={} (max={})",
                    instr.dst, instr.src0, instr.src1, instr.src2, max_vector_regs);
      return false;
    }
    float result = m_vectorRegs[instr.src0 * 4] * m_vectorRegs[instr.src1 * 4] +
                   m_vectorRegs[instr.src2 * 4];
    m_vectorRegs[instr.dst * 4] = result;
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] =
          (std::isnan(result) || std::isinf(result)) ? 1 : 0;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteVMadF32: v{} = v{} * v{} + v{}, latency={}us",
                  instr.dst, instr.src0, instr.src1, instr.src2, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteVMadF32 failed: {}", e.what());
    return false;
  }
}


bool ShaderEmulator::Impl::ExecuteVMinF32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    const uint32_t max_vector_regs = m_vectorRegs.size() / 4;
    if (instr.dst >= max_vector_regs ||
        instr.src0 >= max_vector_regs ||
        instr.src1 >= max_vector_regs) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteVMinF32: Invalid register index: dst={}, src0={}, src1={} (max={})",
          instr.dst, instr.src0, instr.src1, max_vector_regs);
      return false;
    }
    float result =
        std::min(m_vectorRegs[instr.src0 * 4], m_vectorRegs[instr.src1 * 4]);
    m_vectorRegs[instr.dst * 4] = result;
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] =
          (std::isnan(result) || std::isinf(result)) ? 1 : 0;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteVMinF32: v{} = min(v{}, v{}), latency={}us",
                  instr.dst, instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteVMinF32 failed: {}", e.what());
    return false;
  }
}


bool ShaderEmulator::Impl::ExecuteVMaxF32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    const uint32_t max_vector_regs = m_vectorRegs.size() / 4;
    if (instr.dst >= max_vector_regs ||
        instr.src0 >= max_vector_regs ||
        instr.src1 >= max_vector_regs) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteVMaxF32: Invalid register index: dst={}, src0={}, src1={} (max={})",
          instr.dst, instr.src0, instr.src1, max_vector_regs);
      return false;
    }
    float result =
        std::max(m_vectorRegs[instr.src0 * 4], m_vectorRegs[instr.src1 * 4]);
    m_vectorRegs[instr.dst * 4] = result;
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] =
          (std::isnan(result) || std::isinf(result)) ? 1 : 0;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteVMaxF32: v{} = max(v{}, v{}), latency={}us",
                  instr.dst, instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteVMaxF32 failed: {}", e.what());
    return false;
  }
}


bool ShaderEmulator::Impl::ExecuteVCmpEqF32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    const uint32_t max_vector_regs = m_vectorRegs.size() / 4;
    if (instr.dst >= m_scalarRegs.size() ||
        instr.src0 >= max_vector_regs ||
        instr.src1 >= max_vector_regs) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteVCmpEqF32: Invalid register index: dst={}, src0={}, src1={} (max={})",
          instr.dst, instr.src0, instr.src1, max_vector_regs);
      return false;
    }
    bool result =
        (m_vectorRegs[instr.src0 * 4] == m_vectorRegs[instr.src1 * 4]);
    m_scalarRegs[instr.dst] = result ? 1 : 0;
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = result ? 1 : 0;
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, m_scalarRegs[instr.dst]);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteVCmpEqF32: s{} = v{} == v{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteVCmpEqF32 failed: {}", e.what());
    return false;
  }
}


bool ShaderEmulator::Impl::ExecuteVCmpGtF32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    uint32_t stage = static_cast<uint32_t>(m_shaderType);
    const uint32_t max_vector_regs = m_vectorRegs.size() / 4;
    if (instr.dst >= m_scalarRegs.size() ||
        instr.src0 >= max_vector_regs ||
        instr.src1 >= max_vector_regs) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteVCmpGtF32: Invalid register index: dst={}, src0={}, src1={} (max={})",
          instr.dst, instr.src0, instr.src1, max_vector_regs);
      return false;
    }
    bool result = (m_vectorRegs[instr.src0 * 4] > m_vectorRegs[instr.src1 * 4]);
    m_scalarRegs[instr.dst] = result ? 1 : 0;
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = result ? 1 : 0;
    }
    m_gnmState.SetShaderRegister(stage, instr.dst, m_scalarRegs[instr.dst]);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteVCmpGtF32: s{} = v{} > v{}, latency={}us", instr.dst,
                  instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteVCmpGtF32 failed: {}", e.what());
    return false;
  }
}


bool ShaderEmulator::Impl::ExecuteBufferLoadDword(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    const uint32_t max_vector_regs = m_vectorRegs.size() / 4;
    if (instr.dst >= max_vector_regs ||
        instr.src0 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteBufferLoadDword: Invalid register index: dst={}, src={} (max={})",
          instr.dst, instr.src0, max_vector_regs);
      return false;
    }
    uint32_t addr = m_scalarRegs[instr.src0];
    uint32_t value = 0;
    bool dataFetched = false;
    const TileInfo *tileInfo = m_tileManager.GetTileInfo(m_renderTarget);
    if (tileInfo && addr < tileInfo->slice * tileInfo->bytesPerPixel) {
      std::vector<uint8_t> linearData(tileInfo->bytesPerPixel);
      try {
        m_tileManager.TiledToLinear(m_renderTarget, linearData.data(),
                                    linearData.size());
        std::memcpy(&value, linearData.data(),
                    std::min<size_t>(linearData.size(), sizeof(value)));
        dataFetched = true;
      } catch (const std::exception &) {
      }
    }
    if (!dataFetched) {
      VALIDATE_VECTOR(m_memory, "ExecuteBufferLoadDword memory");
      if (addr < m_memory.size() - sizeof(uint32_t)) {
        std::memcpy(&value, &SAFE_VECTOR_ACCESS(m_memory, addr, "ExecuteBufferLoadDword memory access"), sizeof(uint32_t));
      } else {
        value = 0;
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::warn(
            "ExecuteBufferLoadDword: Out-of-bounds memory access at 0x{:x}",
            addr);
      }
    }
    SAFE_VECTOR_ACCESS(m_vectorRegs, instr.dst * 4, "ExecuteBufferLoadDword dst") = bit_cast<float>(value);
    if (instr.predicated) {
      if (instr.predicate < m_scalarRegs.size()) {
        SAFE_VECTOR_ACCESS(m_scalarRegs, instr.predicate, "ExecuteBufferLoadDword predicate") = dataFetched ? 1 : 0;
      }
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteBufferLoadDword: v{} = buffer[0x{:x}], latency={}us",
                  instr.dst, addr, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteBufferLoadDword failed: {}", e.what());
    return false;
  }
}


bool ShaderEmulator::Impl::ExecuteBufferStoreDword(
    const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    const uint32_t max_vector_regs = m_vectorRegs.size() / 4;
    if (instr.src0 >= max_vector_regs ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteBufferStoreDword: Invalid register index: src0={}, src1={} (max={})",
          instr.src0, instr.src1, max_vector_regs);
      return false;
    }
    uint32_t addr = m_scalarRegs[instr.src1];
    uint32_t value = bit_cast<uint32_t>(m_vectorRegs[instr.src0 * 4]);
    bool dataStored = false;
    const TileInfo *tileInfo = m_tileManager.GetTileInfo(m_renderTarget);
    if (tileInfo && addr < tileInfo->slice * tileInfo->bytesPerPixel) {
      std::vector<uint8_t> linearData(tileInfo->bytesPerPixel);
      std::memcpy(linearData.data(), &value,
                  std::min<size_t>(linearData.size(), sizeof(value)));
      try {
        m_tileManager.LinearToTiled(m_renderTarget, linearData.data(),
                                    linearData.size());
        dataStored = true;
      } catch (const std::exception &) {
      }
    }
    if (!dataStored) {
      if (addr < m_memory.size() - sizeof(uint32_t)) {
        std::memcpy(&m_memory[addr], &value, sizeof(uint32_t));
      } else {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::warn(
            "ExecuteBufferStoreDword: Out-of-bounds memory access at 0x{:x}",
            addr);
      }
    }
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = dataStored ? 1 : 0;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteBufferStoreDword: buffer[0x{:x}] = v{}, latency={}us",
                  addr, instr.src0, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteBufferStoreDword failed: {}", e.what());
    return false;
  }
}



bool ShaderEmulator::Impl::ExecuteFlatLoadDword(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    if (instr.dst >= m_vectorRegs.size() / 4 ||
        instr.src0 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteFlatLoadDword: Invalid register index: dst={}, src={}",
          instr.dst, instr.src0);
      return false;
    }
    uint32_t addr = m_scalarRegs[instr.src0];
    uint32_t value = 0;
    VALIDATE_VECTOR(m_memory, "ExecuteFlatLoadDword memory");
    if (addr < m_memory.size() - sizeof(uint32_t)) {
      std::memcpy(&value, &SAFE_VECTOR_ACCESS(m_memory, addr, "ExecuteFlatLoadDword memory access"), sizeof(uint32_t));
    } else {
      value = 0;
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::warn(
          "ExecuteFlatLoadDword: Out-of-bounds memory access at 0x{:x}", addr);
    }
    SAFE_VECTOR_ACCESS(m_vectorRegs, instr.dst * 4, "ExecuteFlatLoadDword dst") = bit_cast<float>(value);
    if (instr.predicated) {
      if (instr.predicate < m_scalarRegs.size()) {
        SAFE_VECTOR_ACCESS(m_scalarRegs, instr.predicate, "ExecuteFlatLoadDword predicate") = 1;
      }
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteFlatLoadDword: v{} = flat[0x{:x}], latency={}us",
                  instr.dst, addr, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteFlatLoadDword failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteFlatStoreDword(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    if (instr.src0 >= m_vectorRegs.size() / 4 ||
        instr.src1 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error(
          "ExecuteFlatStoreDword: Invalid register index: src0={}, src1={}",
          instr.src0, instr.src1);
      return false;
    }
    uint32_t addr = SAFE_VECTOR_ACCESS(m_scalarRegs, instr.src1, "ExecuteFlatStoreDword src1");
    uint32_t value = bit_cast<uint32_t>(SAFE_VECTOR_ACCESS(m_vectorRegs, instr.src0 * 4, "ExecuteFlatStoreDword src0"));
    VALIDATE_VECTOR(m_memory, "ExecuteFlatStoreDword memory");
    if (addr < m_memory.size() - sizeof(uint32_t)) {
      std::memcpy(&SAFE_VECTOR_ACCESS(m_memory, addr, "ExecuteFlatStoreDword memory access"), &value, sizeof(uint32_t));
    } else {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::warn(
          "ExecuteFlatStoreDword: Out-of-bounds memory access at 0x{:x}", addr);
    }
    if (instr.predicated) {
      if (instr.predicate < m_scalarRegs.size()) {
        SAFE_VECTOR_ACCESS(m_scalarRegs, instr.predicate, "ExecuteFlatStoreDword predicate") = 1;
      }
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteFlatStoreDword: flat[0x{:x}] = v{}, latency={}us",
                  addr, instr.src0, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteFlatStoreDword failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSBranch(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    bool shouldBranch = false;
    if (instr.predicated && instr.predicate < m_scalarRegs.size()) {
      shouldBranch = (m_scalarRegs[instr.predicate] != 0);
    } else if (!instr.predicated) {
      shouldBranch = true;
    }
    if (shouldBranch) {
      int32_t offset = static_cast<int32_t>(instr.imm);
      if (instr.src0 < m_scalarRegs.size()) {
        offset = static_cast<int32_t>(m_scalarRegs[instr.src0]);
      }
      int64_t newPC = static_cast<int64_t>(m_pc) + 1 + offset;
      if (newPC >= 0 && newPC < static_cast<int64_t>(m_bytecode.size())) {
        m_pc = static_cast<uint32_t>(newPC);
      } else {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::error(
            "ExecuteSBranch: Branch target out of bounds: PC=0x{:x}, offset={}",
            newPC, offset);
        return false;
      }
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSBranch: branch={}, PC=0x{:x}, latency={}us",
                  shouldBranch, m_pc, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSBranch failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSCbranchScc0(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    bool shouldBranch = false;
    if (instr.predicate < m_scalarRegs.size()) {
      shouldBranch = (m_scalarRegs[instr.predicate] == 0);
    }
    if (shouldBranch) {
      int32_t offset = static_cast<int32_t>(instr.imm);
      int64_t newPC = static_cast<int64_t>(m_pc) + 1 + offset;
      if (newPC >= 0 && newPC < static_cast<int64_t>(m_bytecode.size())) {
        m_pc = static_cast<uint32_t>(newPC);
      } else {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::error("ExecuteSCbranchScc0: Branch target out of bounds: "
                      "PC=0x{:x}, offset={}",
                      newPC, offset);
        return false;
      }
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSCbranchScc0: branch={}, PC=0x{:x}, latency={}us",
                  shouldBranch, m_pc, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSCbranchScc0 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSCbranchVccz(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    bool shouldBranch = false;
    if (instr.predicate < m_scalarRegs.size()) {
      shouldBranch = (m_scalarRegs[instr.predicate] == 0);
    }
    if (shouldBranch) {
      int32_t offset = static_cast<int32_t>(instr.imm);
      int64_t newPC = static_cast<int64_t>(m_pc) + 1 + offset;
      if (newPC >= 0 && newPC < static_cast<int64_t>(m_bytecode.size())) {
        m_pc = static_cast<uint32_t>(newPC);
      } else {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::error("ExecuteSCbranchVccz: Branch target out of bounds: "
                      "PC=0x{:x}, offset={}",
                      newPC, offset);
        return false;
      }
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSCbranchVccz: branch={}, PC=0x{:x}, latency={}us",
                  shouldBranch, m_pc, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSCbranchVccz failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteSEndpgm(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    m_pc = m_bytecode.size(); // Effectively ends program execution
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteSEndpgm: Program ended, latency={}us", latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteSEndpgm failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteVCvtF32U32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    if (instr.dst >= m_vectorRegs.size() / 4 ||
        instr.src0 >= m_scalarRegs.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ExecuteVCvtF32U32: Invalid register index: dst={}, src={}",
                    instr.dst, instr.src0);
      return false;
    }
    uint32_t srcValue;
    if (!m_gnmState.GetCachedRegister(GNMRegisterType::SHADER_REG,
                                      static_cast<uint32_t>(m_shaderType),
                                      instr.src0, srcValue)) {
      srcValue = m_gnmState.GetShaderRegister(
          static_cast<uint32_t>(m_shaderType), instr.src0);
    }
    m_vectorRegs[instr.dst * 4] = static_cast<float>(srcValue);
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = 1;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteVCvtF32U32: v{} = float(s{}), latency={}us",
                  instr.dst, instr.src0, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteVCvtF32U32 failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteVCvtU32F32(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    if (instr.dst >= m_scalarRegs.size() ||
        instr.src0 >= m_vectorRegs.size() / 4) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ExecuteVCvtU32F32: Invalid register index: dst={}, src={}",
                    instr.dst, instr.src0);
      return false;
    }
    m_scalarRegs[instr.dst] =
        static_cast<uint32_t>(m_vectorRegs[instr.src0 * 4]);
    if (instr.predicated) {
      m_scalarRegs[instr.predicate] = 1;
    }
    m_gnmState.SetShaderRegister(static_cast<uint32_t>(m_shaderType), instr.dst,
                                 m_scalarRegs[instr.dst]);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteVCvtU32F32: s{} = uint(v{}), latency={}us", instr.dst,
                  instr.src0, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteVCvtU32F32 failed: {}", e.what());
    return false;
  }
}

void ShaderEmulator::Impl::InitializeRegisters() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    m_scalarRegs.resize(256, 0);
    m_vectorRegs.resize(256 * 4, 0.0f);
    m_memory.resize(1024 * 1024, 0);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("InitializeRegisters: scalar={} regs, vector={} regs, "
                  "memory={} bytes, latency={}us",
                  m_scalarRegs.size(), m_vectorRegs.size() / 4, m_memory.size(),
                  latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("InitializeRegisters failed: {}", e.what());
    throw ShaderEmulatorException("InitializeRegisters failed: " +
                                  std::string(e.what()));
  }
}

void ShaderEmulator::Impl::ResetRegisters() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    std::fill(m_scalarRegs.begin(), m_scalarRegs.end(), 0);
    std::fill(m_vectorRegs.begin(), m_vectorRegs.end(), 0.0f);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "ResetRegisters: Cleared scalar and vector registers, latency={}us",
        latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ResetRegisters failed: {}", e.what());
    throw ShaderEmulatorException("ResetRegisters failed: " +
                                  std::string(e.what()));
  }
}

void ShaderEmulator::Impl::InitializeInstructionTable() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    m_instructionTable[0xBE] =
        std::bind(&Impl::ExecuteSMovB32, this, std::placeholders::_1);
    m_instructionTable[0x80000000] =
        std::bind(&Impl::ExecuteSAddU32, this, std::placeholders::_1);
    m_instructionTable[0x80010000] =
        std::bind(&Impl::ExecuteSSubU32, this, std::placeholders::_1);
    m_instructionTable[0x80020000] =
        std::bind(&Impl::ExecuteSMulU32, this, std::placeholders::_1);
    m_instructionTable[0x80030000] =
        std::bind(&Impl::ExecuteSAndB32, this, std::placeholders::_1);
    m_instructionTable[0x80040000] =
        std::bind(&Impl::ExecuteSOrB32, this, std::placeholders::_1);
    m_instructionTable[0x80050000] =
        std::bind(&Impl::ExecuteSXorB32, this, std::placeholders::_1);
    m_instructionTable[0x80060000] =
        std::bind(&Impl::ExecuteSLshlB32, this, std::placeholders::_1);
    m_instructionTable[0x80070000] =
        std::bind(&Impl::ExecuteSLshrB32, this, std::placeholders::_1);
    m_instructionTable[0x80080000] =
        std::bind(&Impl::ExecuteSAshrI32, this, std::placeholders::_1);
    m_instructionTable[0x80090000] =
        std::bind(&Impl::ExecuteSCmpEqU32, this, std::placeholders::_1);
    m_instructionTable[0x01] =
        std::bind(&Impl::ExecuteVMovB32, this, std::placeholders::_1);
    m_instructionTable[0x02] =
        std::bind(&Impl::ExecuteVAddF32, this, std::placeholders::_1);
    m_instructionTable[0x03] =
        std::bind(&Impl::ExecuteVSubF32, this, std::placeholders::_1);
    m_instructionTable[0x04] =
        std::bind(&Impl::ExecuteVMulF32, this, std::placeholders::_1);
    m_instructionTable[0x05] =
        std::bind(&Impl::ExecuteVMadF32, this, std::placeholders::_1);
    m_instructionTable[0x06] =
        std::bind(&Impl::ExecuteVMinF32, this, std::placeholders::_1);
    m_instructionTable[0x07] =
        std::bind(&Impl::ExecuteVMaxF32, this, std::placeholders::_1);
    m_instructionTable[0x20] =
        std::bind(&Impl::ExecuteVCmpEqF32, this, std::placeholders::_1);
    m_instructionTable[0x21] =
        std::bind(&Impl::ExecuteVCmpGtF32, this, std::placeholders::_1);
    m_instructionTable[0x40] =
        std::bind(&Impl::ExecuteBufferLoadDword, this, std::placeholders::_1);
    m_instructionTable[0x41] =
        std::bind(&Impl::ExecuteBufferStoreDword, this, std::placeholders::_1);
    m_instructionTable[0xDC000000] =
        std::bind(&Impl::ExecuteFlatLoadDword, this, std::placeholders::_1);
    m_instructionTable[0xDC010000] =
        std::bind(&Impl::ExecuteFlatStoreDword, this, std::placeholders::_1);
    m_instructionTable[0xA0] =
        std::bind(&Impl::ExecuteSBranch, this, std::placeholders::_1);
    m_instructionTable[0xBF800000] =
        std::bind(&Impl::ExecuteSCbranchScc0, this, std::placeholders::_1);
    m_instructionTable[0xBF820000] =
        std::bind(&Impl::ExecuteSCbranchVccz, this, std::placeholders::_1);
    m_instructionTable[0xBF810000] =
        std::bind(&Impl::ExecuteSEndpgm, this, std::placeholders::_1);
    m_instructionTable[0x08] =
        std::bind(&Impl::ExecuteVCvtF32U32, this, std::placeholders::_1);
    m_instructionTable[0x09] =
        std::bind(&Impl::ExecuteVCvtU32F32, this, std::placeholders::_1);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "InitializeInstructionTable: Registered {} instructions, latency={}us",
        m_instructionTable.size(), latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("InitializeInstructionTable failed: {}", e.what());
    throw ShaderEmulatorException("InitializeInstructionTable failed: " +
                                  std::string(e.what()));
  }
}

bool ShaderEmulator::Impl::DecodeInstruction(uint32_t word,
                                             GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    instr.opcode = word & 0xFFFF0000;
    instr.dst = (word >> 16) & 0xFF;
    instr.src0 = (word >> 8) & 0xFF;
    instr.src1 = word & 0xFF;
    instr.imm = 0;
    instr.predicated = false;
    instr.predicate = 0;
    bool valid = m_instructionTable.count(instr.opcode) > 0;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    if (valid) {
      spdlog::trace("DecodeInstruction: opcode=0x{:x}, dst={}, src0={}, "
                    "src1={}, latency={}us",
                    instr.opcode, instr.dst, instr.src0, instr.src1, latency);
    } else {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::warn("DecodeInstruction: Unknown opcode 0x{:x}, latency={}us",
                   instr.opcode, latency);
    }
    return valid;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("DecodeInstruction failed: {}", e.what());
    return false;
  }
}

bool ShaderEmulator::Impl::ExecuteInstruction(const GCNInstruction &instr) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_emulatorMutex);
  try {
    auto it = m_instructionTable.find(instr.opcode);
    if (it == m_instructionTable.end()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ExecuteInstruction: Unknown opcode: 0x{:x}", instr.opcode);
      return false;
    }
    bool result = it->second(instr);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ExecuteInstruction: opcode=0x{:x}, result={}, latency={}us",
                  instr.opcode, result, latency);
    return result;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ExecuteInstruction failed: {}", e.what());
    return false;
  }
}

// ShaderEmulator public methods
ShaderEmulator::ShaderEmulator(GNMRegisterState &gnmState,
                               TileManager &tileManager,
                               GNMShaderTranslator &shaderTranslator,
                               CommandProcessor &commandProcessor)
    : pImpl(std::make_unique<Impl>(gnmState, tileManager, shaderTranslator,
                                   commandProcessor)) {
  spdlog::info("ShaderEmulator constructed");
}

ShaderEmulator::~ShaderEmulator() { spdlog::info("ShaderEmulator destroyed"); }

bool ShaderEmulator::LoadShader(const std::vector<uint32_t> &bytecode,
                                GCNShaderType type) {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    bool result = pImpl->LoadShader(bytecode, type);
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("ShaderEmulator::LoadShader: result={}, latency={}us", result,
                  latency);
    return result;
  } catch (const std::exception &e) {
    spdlog::error("ShaderEmulator::LoadShader failed: {}", e.what());
    throw ShaderEmulatorException("LoadShader failed: " +
                                  std::string(e.what()));
  }
}

bool ShaderEmulator::Execute() {
  auto start = std::chrono::high_resolution_clock::now();
  try {
    bool result = pImpl->Execute();
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("ShaderEmulator::Execute: result={}, latency={}us", result,
                  latency);
    return result;
  } catch (const std::exception &e) {
    spdlog::error("ShaderEmulator::Execute failed: {}", e.what());
    throw ShaderEmulatorException("Execute failed: " + std::string(e.what()));
  }
}

bool ShaderEmulator::GetCachedInstructionResult(
    uint64_t instructionHash, std::vector<uint32_t> &results) const {
  return pImpl->GetCachedInstructionResult(instructionHash, results);
}

void ShaderEmulator::ClearInstructionCache() { pImpl->ClearInstructionCache(); }

ShaderEmulatorStats ShaderEmulator::GetStats() const {
  return pImpl->GetStats();
}

void ShaderEmulator::SaveState(std::ostream &out) const {
  pImpl->SaveState(out);
}

void ShaderEmulator::LoadState(std::istream &in) { pImpl->LoadState(in); }

void ShaderEmulator::SetShaderExecutionCallback(
    const ShaderExecutionCallback &callback) {
  pImpl->SetShaderExecutionCallback(callback);
}

// SIMD Lane Implementation
SIMDLane::SIMDLane() {
  std::unique_lock<std::shared_mutex> lock(m_laneMutex);
  m_scalarRegisters.fill(0);
  for (auto &threadRegs : m_vectorRegisters) {
    threadRegs.fill(0);
  }
  m_executionMask = 0xFFFF;
  m_stats = {};
}

SIMDLane::~SIMDLane() { Shutdown(); }

bool SIMDLane::Initialize() {
  std::unique_lock<std::shared_mutex> lock(m_laneMutex);
  try {
    m_scalarRegisters.fill(0);
    for (auto &threadRegs : m_vectorRegisters) {
      threadRegs.fill(0);
    }
    m_executionMask = 0xFFFF;
    m_stats = {};
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    throw ShaderEmulatorException("SIMDLane initialization failed: " +
                                  std::string(e.what()));
  }
}

void SIMDLane::Shutdown() {
  std::unique_lock<std::shared_mutex> lock(m_laneMutex);
  m_scalarRegisters.fill(0);
  for (auto &threadRegs : m_vectorRegisters) {
    threadRegs.fill(0);
  }
  m_stats = {};
}

void SIMDLane::ExecuteInstruction(const GCNInstruction &instr) {
  std::unique_lock<std::shared_mutex> lock(m_laneMutex);
  try {
    m_stats.instructionCount++;
    if ((instr.opcode & 0xFF000000) == 0xBE000000) {
      ExecuteScalarInstruction(instr);
    } else if ((instr.opcode & 0xFF000000) == 0xD8000000) {
      ExecuteVectorInstruction(instr);
    } else if ((instr.opcode & 0xFF000000) == 0xDC000000) {
      ExecuteMemoryInstruction(instr);
    } else if ((instr.opcode & 0xFF000000) == 0xBF000000) {
      ExecuteControlInstruction(instr);
    } else {
      m_stats.errorCount++;
      throw ShaderEmulatorException("Unsupported instruction opcode: " +
                                    std::to_string(instr.opcode));
    }
    const_cast<SIMDLaneStats &>(m_stats).cacheHits++;
  } catch (const std::exception &e) {
    const_cast<SIMDLaneStats &>(m_stats).errorCount++;
    const_cast<SIMDLaneStats &>(m_stats).cacheMisses++;
    throw ShaderEmulatorException("SIMDLane instruction execution failed: " +
                                  std::string(e.what()));
  }
}

uint32_t SIMDLane::GetScalarRegister(uint32_t index) const {
  std::shared_lock<std::shared_mutex> lock(m_laneMutex);
  if (index >= NUM_SCALAR_REGS) {
    throw ShaderEmulatorException("Invalid scalar register index: " +
                                  std::to_string(index));
  }
  m_stats.cacheHits++;
  return m_scalarRegisters[index];
}

void SIMDLane::SetScalarRegister(uint32_t index, uint32_t value) {
  std::unique_lock<std::shared_mutex> lock(m_laneMutex);
  if (index >= NUM_SCALAR_REGS) {
    throw ShaderEmulatorException("Invalid scalar register index: " +
                                  std::to_string(index));
  }
  m_scalarRegisters[index] = value;
  m_stats.cacheHits++;
}

uint32_t SIMDLane::GetVectorRegister(uint32_t thread, uint32_t index) const {
  std::shared_lock<std::shared_mutex> lock(m_laneMutex);
  if (thread >= NUM_THREADS || index >= NUM_VECTOR_REGS) {
    throw ShaderEmulatorException(
        "Invalid vector register access: thread=" + std::to_string(thread) +
        ", index=" + std::to_string(index));
  }
  m_stats.cacheHits++;
  return m_vectorRegisters[thread][index];
}

void SIMDLane::SetVectorRegister(uint32_t thread, uint32_t index,
                                 uint32_t value) {
  std::unique_lock<std::shared_mutex> lock(m_laneMutex);
  if (thread >= NUM_THREADS || index >= NUM_VECTOR_REGS) {
    throw ShaderEmulatorException(
        "Invalid vector register access: thread=" + std::to_string(thread) +
        ", index=" + std::to_string(index));
  }
  m_vectorRegisters[thread][index] = value;
  m_stats.cacheHits++;
}

uint16_t SIMDLane::GetExecutionMask() const {
  std::shared_lock<std::shared_mutex> lock(m_laneMutex);
  m_stats.cacheHits++;
  return m_executionMask;
}

void SIMDLane::SetExecutionMask(uint16_t mask) {
  std::unique_lock<std::shared_mutex> lock(m_laneMutex);
  m_executionMask = mask;
  m_stats.cacheHits++;
}

SIMDLaneStats SIMDLane::GetStats() const {
  std::shared_lock<std::shared_mutex> lock(m_laneMutex);
  m_stats.cacheHits++;
  return m_stats;
}

void SIMDLane::ExecuteScalarInstruction(const GCNInstruction &instr) {
  if (instr.dst < NUM_SCALAR_REGS && instr.src0 < NUM_SCALAR_REGS) {
    m_scalarRegisters[instr.dst] = m_scalarRegisters[instr.src0];
  }
}

void SIMDLane::ExecuteVectorInstruction(const GCNInstruction &instr) {
  for (uint32_t thread = 0; thread < NUM_THREADS; ++thread) {
    if ((m_executionMask & (1 << thread)) && instr.dst < NUM_VECTOR_REGS &&
        instr.src0 < NUM_SCALAR_REGS) {
      m_vectorRegisters[thread][instr.dst] = m_scalarRegisters[instr.src0];
    }
  }
}

void SIMDLane::ExecuteMemoryInstruction(const GCNInstruction &instr) {
  m_stats.cacheHits++;
}

void SIMDLane::ExecuteControlInstruction(const GCNInstruction &instr) {
  m_stats.cacheHits++;
}

// ComputeUnit Implementation
ComputeUnit::ComputeUnit() {
  std::unique_lock<std::shared_mutex> lock(m_unitMutex);
  m_stats = {};
}

ComputeUnit::~ComputeUnit() { Shutdown(); }

bool ComputeUnit::Initialize() {
  std::unique_lock<std::shared_mutex> lock(m_unitMutex);
  try {
    for (auto &lane : m_simdLanes) {
      if (!lane.Initialize()) {
        throw ShaderEmulatorException("Failed to initialize SIMD lane");
      }
    }
    m_stats = {};
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    throw ShaderEmulatorException("ComputeUnit initialization failed: " +
                                  std::string(e.what()));
  }
}

void ComputeUnit::Shutdown() {
  std::unique_lock<std::shared_mutex> lock(m_unitMutex);
  for (auto &lane : m_simdLanes) {
    lane.Shutdown();
  }
  m_stats = {};
}

void ComputeUnit::ExecuteInstruction(const GCNInstruction &instr) {
  std::unique_lock<std::shared_mutex> lock(m_unitMutex);
  try {
    m_stats.instructionCount++;
    for (auto &lane : m_simdLanes) {
      lane.ExecuteInstruction(instr);
    }
    m_stats.cacheHits++;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    throw ShaderEmulatorException("ComputeUnit instruction execution failed: " +
                                  std::string(e.what()));
  }
}

SIMDLane &ComputeUnit::GetSIMDLane(uint32_t index) {
  std::unique_lock<std::shared_mutex> lock(m_unitMutex);
  if (index >= NUM_SIMD_LANES) {
    throw ShaderEmulatorException("Invalid SIMD lane index: " +
                                  std::to_string(index));
  }
  return m_simdLanes[index];
}

ComputeUnitStats ComputeUnit::GetStats() const {
  std::shared_lock<std::shared_mutex> lock(m_unitMutex);
  return m_stats;
}

// ShaderProgram Implementation
void ShaderProgram::Serialize(std::ostream &out) const {
  uint64_t instrCount = instructions.size();
  out.write(reinterpret_cast<const char *>(&instrCount), sizeof(instrCount));
  for (const auto &instr : instructions) {
    instr.Serialize(out);
  }
  out.write(reinterpret_cast<const char *>(&entryPoint), sizeof(entryPoint));
  out.write(reinterpret_cast<const char *>(&numRegisters),
            sizeof(numRegisters));
  out.write(reinterpret_cast<const char *>(&numThreads), sizeof(numThreads));
  out.write(reinterpret_cast<const char *>(&cacheHits), sizeof(cacheHits));
  out.write(reinterpret_cast<const char *>(&cacheMisses), sizeof(cacheMisses));
}

void ShaderProgram::Deserialize(std::istream &in) {
  uint64_t instrCount;
  in.read(reinterpret_cast<char *>(&instrCount), sizeof(instrCount));
  if (!in.good() || instrCount > 100000) {
    throw ShaderEmulatorException(
        "Invalid instruction count in shader program");
  }
  instructions.resize(instrCount);
  for (auto &instr : instructions) {
    instr.Deserialize(in);
  }
  in.read(reinterpret_cast<char *>(&entryPoint), sizeof(entryPoint));
  in.read(reinterpret_cast<char *>(&numRegisters), sizeof(numRegisters));
  in.read(reinterpret_cast<char *>(&numThreads), sizeof(numThreads));
  in.read(reinterpret_cast<char *>(&cacheHits), sizeof(cacheHits));
  in.read(reinterpret_cast<char *>(&cacheMisses), sizeof(cacheMisses));
  if (!in.good()) {
    throw ShaderEmulatorException("Failed to deserialize shader program");
  }
}

// ShaderContext Implementation
void ShaderContext::Serialize(std::ostream &out) const {
  out.write(reinterpret_cast<const char *>(&programCounter),
            sizeof(programCounter));
  out.write(reinterpret_cast<const char *>(&stackPointer),
            sizeof(stackPointer));
  uint64_t stackSize = stack.size();
  out.write(reinterpret_cast<const char *>(&stackSize), sizeof(stackSize));
  if (stackSize > 0) {
    out.write(reinterpret_cast<const char *>(stack.data()),
              stackSize * sizeof(uint32_t));
  }
  out.write(reinterpret_cast<const char *>(&running), sizeof(running));
  out.write(reinterpret_cast<const char *>(&cacheHits), sizeof(cacheHits));
  out.write(reinterpret_cast<const char *>(&cacheMisses), sizeof(cacheMisses));
}

void ShaderContext::Deserialize(std::istream &in) {
  in.read(reinterpret_cast<char *>(&programCounter), sizeof(programCounter));
  in.read(reinterpret_cast<char *>(&stackPointer), sizeof(stackPointer));
  uint64_t stackSize;
  in.read(reinterpret_cast<char *>(&stackSize), sizeof(stackSize));
  if (!in.good() || stackSize > 10000) {
    throw ShaderEmulatorException("Invalid stack size in shader context");
  }
  stack.resize(stackSize);
  if (stackSize > 0) {
    in.read(reinterpret_cast<char *>(stack.data()),
            stackSize * sizeof(uint32_t));
  }
  in.read(reinterpret_cast<char *>(&running), sizeof(running));
  in.read(reinterpret_cast<char *>(&cacheHits), sizeof(cacheHits));
  in.read(reinterpret_cast<char *>(&cacheMisses), sizeof(cacheMisses));
  if (!in.good()) {
    throw ShaderEmulatorException("Failed to deserialize shader context");
  }
}

} // namespace ps4
