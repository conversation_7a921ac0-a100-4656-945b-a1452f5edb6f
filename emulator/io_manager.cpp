#include "io_manager.h"
#include "../common/lock_ordering.h"
#include "../ps4/ps4_emulator.h"
#include "interrupt_handler.h"
#include <algorithm>
#include <chrono>
#include <cstdint>
#include <cstring>
#include <string>
#include <utility>
#include <vector>

// Simple logging replacement
namespace spdlog {
template <typename... Args> static void info(const char *fmt, Args &&...args) {}
template <typename... Args> static void warn(const char *fmt, Args &&...args) {}
template <typename... Args>
static void error(const char *fmt, Args &&...args) {}
template <typename... Args>
static void debug(const char *fmt, Args &&...args) {}
template <typename... Args>
static void trace(const char *fmt, Args &&...args) {}
} // namespace spdlog

// Include the necessary header for MemoryDiagnostics
#include "../memory/memory_diagnostics.h"

namespace x86_64 {
struct IOException : std::runtime_error {
  explicit IOException(const std::string &msg) : std::runtime_error(msg) {}
};
/**
 * @brief Destructs the IOManager, ensuring cleanup.
 */
IOManager::~IOManager() {
  IO_LOCK(m_deviceMutex, "DeviceMutex");
  m_legacyDevices.clear();
  m_devices.clear();
  spdlog::info("IOManager destroyed");
}

/**
 * @brief Registers a legacy device with read/write handlers.
 * @details Associates a device with a base address, size, and handlers.
 * @param name Device name.
 * @param baseAddr Base address for MMIO/port I/O.
 * @param size Address range size.
 * @param readHandler Read handler function.
 * @param writeHandler Write handler function.
 * @return True on success, false if already registered.
 */
bool IOManager::RegisterDevice(
    const std::string &name, uint64_t baseAddr, uint64_t size,
    std::function<void(uint64_t, uint8_t *, size_t)> readHandler,
    std::function<void(uint64_t, const uint8_t *, size_t)> writeHandler) {
  IO_LOCK(m_deviceMutex, "DeviceMutex");
  if (m_legacyDevices.find(name) != m_legacyDevices.end() ||
      m_devices.find(name) != m_devices.end()) {
    spdlog::warn("Device {} already registered", name);
    return false;
  }

  LegacyDevice dev;
  dev.name = name;
  dev.baseAddr = baseAddr;
  dev.size = size;
  dev.readHandler = std::move(readHandler);
  dev.writeHandler = std::move(writeHandler);

  m_legacyDevices.emplace(name, std::move(dev));
  // RACE CONDITION FIX: Use atomic operations for statistics
  m_stats.device_registrations.fetch_add(1, std::memory_order_relaxed);
  spdlog::info("Registered legacy device {} at 0x{:x} (size: 0x{:x})", name,
               baseAddr, size);
  ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  return true;
}

/**
 * @brief Registers a modern device with a Device interface.
 * @details Associates a device object with a base address and size.
 * @param device Unique pointer to the device.
 * @param baseAddr Base address for MMIO/port I/O.
 * @param size Address range size.
 * @return True on success, false if already registered.
 */
bool IOManager::RegisterDevice(std::unique_ptr<x86_64::Device> device,
                               uint64_t baseAddr, uint64_t size) {
  IO_LOCK(m_deviceMutex, "DeviceMutex");
  auto name = device->GetName();
  if (m_legacyDevices.find(name) != m_legacyDevices.end() ||
      m_devices.find(name) != m_devices.end()) {
    spdlog::warn("Device {} already registered", name);
    return false;
  }

  RegisteredDevice regDev;
  regDev.device = std::move(device);
  regDev.baseAddr = baseAddr;
  regDev.size = size;
  m_devices.emplace(name, std::move(regDev));
  // RACE CONDITION FIX: Use atomic operations for statistics
  m_stats.device_registrations.fetch_add(1, std::memory_order_relaxed);
  spdlog::info("Registered device {} at 0x{:x} (size: 0x{:x})", name, baseAddr,
               size);
  ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  return true;
}

/**
 * @brief Unregisters a device by name.
 * @details Removes a legacy or modern device from the manager.
 * @param name Device name.
 */
void IOManager::UnregisterDevice(const std::string &name) {
  IO_LOCK(m_deviceMutex, "DeviceMutex");
  auto legacyIt = m_legacyDevices.find(name);
  if (legacyIt != m_legacyDevices.end()) {
    spdlog::info("Unregistered legacy device {}", name);
    m_legacyDevices.erase(legacyIt);
    // RACE CONDITION FIX: Use atomic operations for statistics
    m_stats.device_unregistrations.fetch_add(1, std::memory_order_relaxed);
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    return;
  }
  auto devIt = m_devices.find(name);
  if (devIt != m_devices.end()) {
    spdlog::info("Unregistered device {}", name);
    m_devices.erase(devIt);
    // RACE CONDITION FIX: Use atomic operations for statistics
    m_stats.device_unregistrations.fetch_add(1, std::memory_order_relaxed);
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    return;
  }
  spdlog::warn("Attempt to unregister unknown device {}", name);
}

/**
 * @brief Initializes standard devices (PIT, PIC, Keyboard, DMA).
 * @return True on success, false on failure.
 */
bool IOManager::InitializeStandardDevices() {
  try {
    InitializePIT();
    InitializePIC();
    InitializeKeyboard();
    InitializeDMA();
    spdlog::info("Standard devices initialized");
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    return true;
  } catch (const IOException &e) {
    spdlog::error("Failed to initialize devices: {}", e.what());
    return false;
  }
}

/**
 * @brief Reads from an MMIO address.
 * @details Attempts to read from a registered device’s address range.
 * @param addr MMIO address.
 * @param data Output buffer.
 * @param size Data size.
 * @return True on success, false if no device is mapped.
 */
bool IOManager::ReadMMIO(uint64_t addr, uint8_t *data, size_t size) {
  auto start = std::chrono::steady_clock::now();
  IO_LOCK(m_deviceMutex, "DeviceMutex");
  for (const auto &entry : m_devices) {
    const auto &name = entry.first;
    const auto &dev = entry.second;
    if (addr >= dev.baseAddr && addr + size <= dev.baseAddr + dev.size) {
      try {
        // Convert x86_64::Device interface to IOManager interface
        uint64_t value = dev.device->Read(addr - dev.baseAddr, static_cast<uint8_t>(size));
        // Copy the value to the data buffer based on size
        switch (size) {
        case 1:
          *data = static_cast<uint8_t>(value);
          break;
        case 2:
          *reinterpret_cast<uint16_t*>(data) = static_cast<uint16_t>(value);
          break;
        case 4:
          *reinterpret_cast<uint32_t*>(data) = static_cast<uint32_t>(value);
          break;
        case 8:
          *reinterpret_cast<uint64_t*>(data) = value;
          break;
        default:
          spdlog::error("Unsupported read size: {}", size);
          return false;
        }
        spdlog::trace("MMIO read from device {} at 0x{:x} (size: {})", name,
                      addr, size);
        // RACE CONDITION FIX: Use atomic operations for statistics
        m_stats.reads.fetch_add(1, std::memory_order_relaxed);
        auto end = std::chrono::steady_clock::now();
        m_stats.total_latency_us.fetch_add(
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count(),
            std::memory_order_relaxed);
        ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
        return true;
      } catch (const std::exception &e) {
        spdlog::warn("MMIO read error for device {} at 0x{:x}: {}", name, addr,
                     e.what());
        return false;
      }
    }
  }
  for (const auto &entry : m_legacyDevices) {
    const auto &name = entry.first;
    const auto &dev = entry.second;
    if (addr >= dev.baseAddr && addr + size <= dev.baseAddr + dev.size) {
      if (dev.readHandler) {
        try {
          dev.readHandler(addr - dev.baseAddr, data, size);
          spdlog::trace("MMIO read from legacy device {} at 0x{:x} (size: {})",
                        name, addr, size);
          // RACE CONDITION FIX: Use atomic operations for statistics
          m_stats.reads.fetch_add(1, std::memory_order_relaxed);
          auto end = std::chrono::steady_clock::now();
          m_stats.total_latency_us.fetch_add(
              std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                  .count(),
              std::memory_order_relaxed);
          ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
          return true;
        } catch (const std::exception &e) {
          spdlog::warn("MMIO read error for legacy device {} at 0x{:x}: {}",
                       name, addr, e.what());
          return false;
        }
      }
    }
  }
  spdlog::warn("No device mapped for MMIO read at 0x{:x}", addr);
  return false;
}

/**
 * @brief Writes to an MMIO address.
 * @details Attempts to write to a registered device’s address range.
 * @param addr MMIO address.
 * @param data Input buffer.
 * @param size Data size.
 * @return True on success, false if no device is mapped.
 */
bool IOManager::WriteMMIO(uint64_t addr, const uint8_t *data, size_t size) {
  auto start = std::chrono::steady_clock::now();
  IO_LOCK(m_deviceMutex, "DeviceMutex");
  for (const auto &entry : m_devices) {
    const auto &name = entry.first;
    const auto &dev = entry.second;
    if (addr >= dev.baseAddr && addr + size <= dev.baseAddr + dev.size) {
      try {
        // Convert IOManager interface to x86_64::Device interface
        uint64_t value = 0;
        switch (size) {
        case 1:
          value = *data;
          break;
        case 2:
          value = *reinterpret_cast<const uint16_t*>(data);
          break;
        case 4:
          value = *reinterpret_cast<const uint32_t*>(data);
          break;
        case 8:
          value = *reinterpret_cast<const uint64_t*>(data);
          break;
        default:
          spdlog::error("Unsupported write size: {}", size);
          return false;
        }
        dev.device->Write(addr - dev.baseAddr, value, static_cast<uint8_t>(size));
        spdlog::trace("MMIO write to device {} at 0x{:x} (size: {})", name,
                      addr, size);
        // RACE CONDITION FIX: Use atomic operations for statistics
        m_stats.writes.fetch_add(1, std::memory_order_relaxed);
        auto end = std::chrono::steady_clock::now();
        m_stats.total_latency_us.fetch_add(
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count(),
            std::memory_order_relaxed);
        ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
        return true;
      } catch (const std::exception &e) {
        spdlog::warn("MMIO write error for device {} at 0x{:x}: {}", name, addr,
                     e.what());
        return false;
      }
    }
  }
  for (const auto &entry : m_legacyDevices) {
    const auto &name = entry.first;
    const auto &dev = entry.second;
    if (addr >= dev.baseAddr && addr + size <= dev.baseAddr + dev.size) {
      if (dev.writeHandler) {
        try {
          dev.writeHandler(addr - dev.baseAddr, data, size);
          spdlog::trace("MMIO write to legacy device {} at 0x{:x} (size: {})",
                        name, addr, size);
          // RACE CONDITION FIX: Use atomic operations for statistics
          m_stats.writes.fetch_add(1, std::memory_order_relaxed);
          auto end = std::chrono::steady_clock::now();
          m_stats.total_latency_us.fetch_add(
              std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                  .count(),
              std::memory_order_relaxed);
          ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
          return true;
        } catch (const std::exception &e) {
          spdlog::warn("MMIO write error for legacy device {} at 0x{:x}: {}",
                       name, addr, e.what());
          return false;
        }
      }
    }
  }
  spdlog::warn("No device mapped for MMIO write at 0x{:x}", addr);
  return false;
}

/**
 * @brief Reads from a port.
 * @details Attempts to read from a registered device’s port range.
 * @param port Port number.
 * @param data Output buffer.
 * @param size Data size.
 * @return True on success, false if no device is mapped.
 */
bool IOManager::ReadPort(uint16_t port, uint8_t *data, size_t size) {
  auto start = std::chrono::steady_clock::now();
  // RACE CONDITION FIX: Use atomic operations for statistics
  m_stats.reads.fetch_add(1, std::memory_order_relaxed);
  bool result = HandlePortIO(port, data, size, true);
  if (result) {
    auto end = std::chrono::steady_clock::now();
    m_stats.total_latency_us.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
  }
  ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  return result;
}

/**
 * @brief Writes to a port.
 * @details Attempts to write to a registered device’s port range.
 * @param port Port number.
 * @param data Input buffer.
 * @param size Data size.
 * @return True on success, false if no device is mapped.
 */
bool IOManager::WritePort(uint16_t port, const uint8_t *data, size_t size) {
  auto start = std::chrono::steady_clock::now();
  // RACE CONDITION FIX: Use atomic operations for statistics
  m_stats.writes.fetch_add(1, std::memory_order_relaxed);
  bool result = HandlePortIO(port, const_cast<uint8_t *>(data), size, false);
  if (result) {
    auto end = std::chrono::steady_clock::now();
    m_stats.total_latency_us.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
  }
  ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  return result;
}

/**
 * @brief Raises an IRQ with specified priority.
 * @details Queues an interrupt via the interrupt handler with priority.
 * @param irqNumber IRQ number.
 * @param priority Interrupt priority.
 */
void IOManager::RaiseIRQ(uint8_t irqNumber, uint8_t priority) {
  // CRITICAL: Bounds check for IRQ number
  if (irqNumber >= 256) { // Maximum IRQ number
    spdlog::error("Invalid IRQ number: {}", irqNumber);
    return;
  }

  // CRITICAL FIX: Prevent deadlock by not holding m_irqMutex when calling
  // TriggerInterrupt Update IRQ state first, then trigger interrupt without
  // holding our mutex
  {
    IO_LOCK(m_irqMutex, "IRQMutex");
    auto &state = m_irqStates[irqNumber];
    state.active = true;
    state.priority = std::max<uint8_t>(state.priority, priority);
    state.nestCount++;
    // RACE CONDITION FIX: Use atomic operations for statistics
    m_stats.irqsRaised.fetch_add(1, std::memory_order_relaxed);
  }

  // CRITICAL FIX: Actually trigger the interrupt via InterruptHandler
  // Map IRQ numbers to interrupt vectors (standard PC mapping)
  uint8_t interruptVector;
  if (irqNumber < 8) {
    // Master PIC IRQs (0-7) map to vectors 32-39
    interruptVector = 32 + irqNumber;
  } else if (irqNumber < 16) {
    // Slave PIC IRQs (8-15) map to vectors 40-47
    interruptVector = 40 + (irqNumber - 8);
  } else {
    // Custom/System IRQs map to higher vectors
    interruptVector = 48 + irqNumber;
    if (interruptVector > 255) {
      spdlog::error("IRQ {} maps to invalid vector {}", irqNumber,
                    interruptVector);
      return;
    }
  }

  try {
    // Trigger the interrupt through the interrupt handler
    m_interruptHandler.HandleInterrupt(interruptVector, 0, false);
    spdlog::trace("Successfully triggered interrupt vector {} for IRQ {} with "
                  "priority {}",
                  interruptVector, irqNumber, priority);
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::error("Failed to trigger interrupt for IRQ {}: {}", irqNumber,
                  e.what());
  }
}

/**
 * @brief Lowers an IRQ.
 * @details Decrements the nest count or deactivates the IRQ.
 * @param irqNumber IRQ number.
 */
void IOManager::LowerIRQ(uint8_t irqNumber) {
  IO_LOCK(m_irqMutex, "IRQMutex");
  auto it = m_irqStates.find(irqNumber);
  if (it != m_irqStates.end()) {
    auto &state = it->second;
    if (state.nestCount > 0) {
      state.nestCount--;
      if (state.nestCount == 0) {
        state.active = false;
        m_irqStates.erase(it);
      }
    }
    // RACE CONDITION FIX: Use atomic operations for statistics
    m_stats.irqsLowered.fetch_add(1, std::memory_order_relaxed);
    spdlog::trace("Lowered IRQ {}", irqNumber);
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  }
}

/**
 * @brief Checks if an IRQ is active.
 * @param irqNumber IRQ number.
 * @return True if active, false otherwise.
 */
bool IOManager::IsIRQActive(uint8_t irqNumber) const {
  IO_LOCK(m_irqMutex, "IRQMutex");
  auto it = m_irqStates.find(irqNumber);
  return it != m_irqStates.end() && it->second.active;
}

/**
 * @brief Retrieves all pending IRQs.
 * @return Vector of active IRQ numbers.
 */
std::vector<uint8_t> IOManager::GetPendingIRQs() const {
  IO_LOCK(m_irqMutex, "IRQMutex");
  std::vector<uint8_t> pending;
  for (const auto &entry : m_irqStates) {
    uint8_t irq = entry.first;
    const auto &state = entry.second;
    if (state.active) {
      pending.push_back(irq);
    }
  }
  return pending;
}

/**
 * @brief Retrieves the keyboard state.
 * @details Reads buffered keyboard input.
 * @param state Output buffer.
 * @param size Buffer size.
 * @return True if data was read, false if buffer is empty.
 */
bool IOManager::GetKeyboardState(uint8_t *state, size_t size) {
  auto start = std::chrono::steady_clock::now();
  IO_LOCK(m_keyboardMutex, "KeyboardMutex");
  if (size == 0 || m_keyboard.head == m_keyboard.tail) {
    return false;
  }
  size_t available =
      (m_keyboard.head >= m_keyboard.tail)
          ? m_keyboard.head - m_keyboard.tail
          : sizeof(m_keyboard.buffer) - m_keyboard.tail + m_keyboard.head;
  size_t toCopy = std::min(size, available);
  for (size_t i = 0; i < toCopy; ++i) {
    state[i] = m_keyboard.buffer[m_keyboard.tail];
    m_keyboard.tail = (m_keyboard.tail + 1) % sizeof(m_keyboard.buffer);
  }
  spdlog::trace("Read {} bytes from keyboard buffer", toCopy);
  // RACE CONDITION FIX: Use atomic operations for statistics
  m_stats.keyboard_reads.fetch_add(toCopy, std::memory_order_relaxed);
  auto end = std::chrono::steady_clock::now();
  m_stats.total_latency_us.fetch_add(
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count(),
      std::memory_order_relaxed);
  ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  return true;
}

/**
 * @brief Sets the PIT frequency.
 * @details Configures the Programmable Interval Timer frequency.
 * @param hz Frequency in Hz.
 */
void IOManager::SetTimerFrequency(uint64_t hz) {
  IO_LOCK(m_pitMutex, "PITMutex");
  if (hz > 0) {
    m_pit.frequency = hz;
    spdlog::info("Set PIT frequency to {} Hz", hz);
    // RACE CONDITION FIX: Use atomic operations for statistics
    m_stats.pit_frequency_changes.fetch_add(1, std::memory_order_relaxed);
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  }
}

/**
 * @brief Retrieves the current PIT tick count.
 * @return Number of ticks.
 */
uint64_t IOManager::GetTimerTicks() const {
  IO_LOCK(m_pitMutex, "PITMutex");
  return m_pit.ticks;
}

/**
 * @brief Initializes the PIT device.
 * CRITICAL FIX: Enhanced PIT implementation with proper channel support
 */
void IOManager::InitializePIT() {
  auto readPIT = [this](uint64_t addr, uint8_t *data, size_t size) {
    IO_LOCK(m_pitMutex, "PITMutex");
    if (size == 1) {
      switch (addr) {
      case 0: // Channel 0 counter
        *data = static_cast<uint8_t>(m_pit.channels[0].counter & 0xFF);
        break;
      case 1: // Channel 1 counter
        *data = static_cast<uint8_t>(m_pit.channels[1].counter & 0xFF);
        break;
      case 2: // Channel 2 counter
        *data = static_cast<uint8_t>(m_pit.channels[2].counter & 0xFF);
        break;
      case 3:      // Control register - read back command
        *data = 0; // Status byte would be returned here
        break;
      default:
        *data = 0;
        break;
      }
      // RACE CONDITION FIX: Use atomic operations for statistics
      m_stats.pit_reads.fetch_add(1, std::memory_order_relaxed);
      ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    }
  };
  auto writePIT = [this](uint64_t addr, const uint8_t *data, size_t size) {
    IO_LOCK(m_pitMutex, "PITMutex");
    if (size == 1) {
      switch (addr) {
      case 0: // Channel 0 counter
        if (m_pit.channels[0].latch_mode) {
          m_pit.channels[0].counter =
              (m_pit.channels[0].counter & 0xFF00) | *data;
          m_pit.channels[0].latch_mode = false;
        } else {
          m_pit.channels[0].counter =
              (m_pit.channels[0].counter & 0x00FF) | (*data << 8);
          m_pit.channels[0].latch_mode = true;
        }
        break;
      case 1: // Channel 1 counter
        if (m_pit.channels[1].latch_mode) {
          m_pit.channels[1].counter =
              (m_pit.channels[1].counter & 0xFF00) | *data;
          m_pit.channels[1].latch_mode = false;
        } else {
          m_pit.channels[1].counter =
              (m_pit.channels[1].counter & 0x00FF) | (*data << 8);
          m_pit.channels[1].latch_mode = true;
        }
        break;
      case 2: // Channel 2 counter
        if (m_pit.channels[2].latch_mode) {
          m_pit.channels[2].counter =
              (m_pit.channels[2].counter & 0xFF00) | *data;
          m_pit.channels[2].latch_mode = false;
        } else {
          m_pit.channels[2].counter =
              (m_pit.channels[2].counter & 0x00FF) | (*data << 8);
          m_pit.channels[2].latch_mode = true;
        }
        break;
      case 3: // Control register
      {
        uint8_t channel = (*data >> 6) & 0x3;
        uint8_t access_mode = (*data >> 4) & 0x3;
        uint8_t operating_mode = (*data >> 1) & 0x7;
        bool bcd_mode = *data & 0x1;

        // CRITICAL: Bounds check for channel access
        if (channel < 3 && channel < sizeof(m_pit.channels)/sizeof(m_pit.channels[0])) {
          m_pit.channels[channel].access_mode = access_mode;
          m_pit.channels[channel].operating_mode = operating_mode;
          m_pit.channels[channel].bcd_mode = bcd_mode;
          m_pit.channels[channel].latch_mode = false;
        }
      } break;
      }
      // RACE CONDITION FIX: Use atomic operations for statistics
      m_stats.pit_writes.fetch_add(1, std::memory_order_relaxed);
      ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    }
  };
  if (!RegisterDevice("PIT", 0x40, 4, readPIT, writePIT)) {
    throw IOException("Failed to register PIT device");
  }
  spdlog::info("Initialized enhanced PIT at port 0x40 with 3 channels");
}

/**
 * @brief Initializes the PIC devices (master and slave).
 * CRITICAL FIX: Enhanced PIC implementation with proper EOI and ICW handling
 */
void IOManager::InitializePIC() {
  auto readPIC = [this](uint64_t addr, uint8_t *data, size_t size) {
    IO_LOCK(m_deviceMutex, "DeviceMutex");
    if (size == 1) {
      switch (addr) {
      case 0: // Command/Status register
        if (m_pic.read_isr) {
          *data = m_pic.isr; // In-Service Register
        } else {
          *data = m_pic.irr; // Interrupt Request Register
        }
        break;
      case 1: // Data register
        if (m_pic.init_state > 0) {
          // During initialization, return ICW values
          *data = m_pic.icw[m_pic.init_state - 1];
        } else {
          *data = m_pic.imr; // Interrupt Mask Register
        }
        break;
      default:
        *data = 0;
        break;
      }
      // RACE CONDITION FIX: Use atomic operations for statistics
      m_stats.pic_reads.fetch_add(1, std::memory_order_relaxed);
      ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    }
  };
  auto writePIC = [this](uint64_t addr, const uint8_t *data, size_t size) {
    IO_LOCK(m_deviceMutex, "DeviceMutex");
    if (size == 1) {
      switch (addr) {
      case 0: // Command register
        if (*data & 0x10) {
          // ICW1 - Initialization Command Word 1
          m_pic.icw[0] = *data;
          m_pic.init_state = 1;
          m_pic.isr = 0;
          m_pic.irr = 0;
          m_pic.imr = 0xFF; // Mask all interrupts during init
        } else if (*data & 0x08) {
          // OCW3 - Operation Command Word 3
          if (*data & 0x04) {
            m_pic.read_isr = true;
          } else {
            m_pic.read_isr = false;
          }
        } else if ((*data & 0x18) == 0) {
          // OCW2 - Operation Command Word 2 (EOI commands)
          uint8_t eoi_type = (*data >> 5) & 0x7;
          if (eoi_type == 1) {
            // Non-specific EOI
            for (int i = 0; i < 8; i++) {
              if (m_pic.isr & (1 << i)) {
                m_pic.isr &= ~(1 << i);
                LowerIRQ(i);
                break;
              }
            }
          } else if (eoi_type == 3) {
            // Specific EOI
            uint8_t irq_level = *data & 0x7;
            m_pic.isr &= ~(1 << irq_level);
            LowerIRQ(irq_level);
          }
        }
        break;
      case 1: // Data register
        if (m_pic.init_state == 1) {
          // ICW2 - Interrupt vector base
          m_pic.icw[1] = *data;
          m_pic.init_state = 2;
        } else if (m_pic.init_state == 2) {
          // ICW3 - Cascade information
          m_pic.icw[2] = *data;
          m_pic.init_state =
              (m_pic.icw[0] & 0x01) ? 0 : 3; // Check if ICW4 needed
        } else if (m_pic.init_state == 3) {
          // ICW4 - Additional information
          m_pic.icw[3] = *data;
          m_pic.init_state = 0; // Initialization complete
          m_pic.imr = 0xFF;     // Start with all interrupts masked
        } else {
          // OCW1 - Interrupt Mask Register
          m_pic.imr = *data;
        }
        break;
      }
      // RACE CONDITION FIX: Use atomic operations for statistics
      m_stats.pic_writes.fetch_add(1, std::memory_order_relaxed);
      ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    }
  };
  if (!RegisterDevice("PIC1", 0x20, 2, readPIC, writePIC) ||
      !RegisterDevice("PIC2", 0xA0, 2, readPIC, writePIC)) {
    throw IOException("Failed to register PIC devices");
  }
  spdlog::info(
      "Initialized enhanced PIC at ports 0x20, 0xA0 with proper EOI handling");
}

/**
 * @brief Initializes the PS/2 keyboard device.
 */
void IOManager::InitializeKeyboard() {
  auto readKeyboard = [this](uint64_t addr, uint8_t *data, size_t size) {
    IO_LOCK(m_keyboardMutex, "KeyboardMutex");
    if (addr == 0 && size == 1) {
      GetKeyboardState(data, 1);
      // RACE CONDITION FIX: Use atomic operations for statistics
      m_stats.keyboard_reads.fetch_add(1, std::memory_order_relaxed);
      ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    }
  };
  auto writeKeyboard = [this](uint64_t addr, const uint8_t *data, size_t size) {
    IO_LOCK(m_keyboardMutex, "KeyboardMutex");
    if (addr == 0 && size == 1) {
      size_t nextHead = (m_keyboard.head + 1) % sizeof(m_keyboard.buffer);
      if (nextHead != m_keyboard.tail) {
        m_keyboard.buffer[m_keyboard.head] = *data;
        m_keyboard.head = nextHead;
        Event event;
        event.type = Event::Type::Custom;
        event.timestamp =
            std::chrono::duration_cast<std::chrono::nanoseconds>(
                std::chrono::steady_clock::now().time_since_epoch())
                .count();
        event.callback = [this]() {
          RaiseIRQ(1, 64); // Medium priority for keyboard
          ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
        };
        {
          IO_LOCK(m_eventMutex, "EventMutex");
          m_eventQueue.push(std::move(event));
        }
        // RACE CONDITION FIX: Use atomic operations for statistics
        m_stats.keyboard_writes.fetch_add(1, std::memory_order_relaxed);
        ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
      }
    }
  };
  if (!RegisterDevice("Keyboard", 0x60, 1, readKeyboard, writeKeyboard)) {
    throw IOException("Failed to register Keyboard device");
  }
  spdlog::info("Initialized PS/2 keyboard at port 0x60");
}

/**
 * @brief Initializes the DMA controller.
 * CRITICAL FIX: Enhanced DMA implementation with proper channel support and
 * transfers
 */
void IOManager::InitializeDMA() {
  auto readDMA = [this](uint64_t addr, uint8_t *data, size_t size) {
    IO_LOCK(m_deviceMutex, "DeviceMutex");
    if (size == 1) {
      switch (addr) {
      case 0:
      case 2:
      case 4:
      case 6: // Channel 0-3 address registers
      {
        uint8_t channel = addr / 2;
        if (channel < 4) {
          if (m_dma.channels[channel].address_latch) {
            *data = (m_dma.channels[channel].address >> 8) & 0xFF;
            m_dma.channels[channel].address_latch = false;
          } else {
            *data = m_dma.channels[channel].address & 0xFF;
            m_dma.channels[channel].address_latch = true;
          }
        }
      } break;
      case 1:
      case 3:
      case 5:
      case 7: // Channel 0-3 count registers
      {
        uint8_t channel = (addr - 1) / 2;
        if (channel < 4) {
          if (m_dma.channels[channel].count_latch) {
            *data = (m_dma.channels[channel].count >> 8) & 0xFF;
            m_dma.channels[channel].count_latch = false;
          } else {
            *data = m_dma.channels[channel].count & 0xFF;
            m_dma.channels[channel].count_latch = true;
          }
        }
      } break;
      case 8: // Status register
        *data = m_dma.status;
        break;
      case 10: // Mask register
        *data = m_dma.mask;
        break;
      default:
        *data = 0;
        break;
      }
      // RACE CONDITION FIX: Use atomic operations for statistics
      m_stats.dma_reads.fetch_add(1, std::memory_order_relaxed);
      ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    }
  };
  auto writeDMA = [this](uint64_t addr, const uint8_t *data, size_t size) {
    IO_LOCK(m_deviceMutex, "DeviceMutex");
    if (size == 1) {
      switch (addr) {
      case 0:
      case 2:
      case 4:
      case 6: // Channel 0-3 address registers
      {
        uint8_t channel = addr / 2;
        if (channel < 4) {
          if (m_dma.channels[channel].address_latch) {
            m_dma.channels[channel].address =
                (m_dma.channels[channel].address & 0x00FF) | (*data << 8);
            m_dma.channels[channel].address_latch = false;
          } else {
            m_dma.channels[channel].address =
                (m_dma.channels[channel].address & 0xFF00) | *data;
            m_dma.channels[channel].address_latch = true;
          }
        }
      } break;
      case 1:
      case 3:
      case 5:
      case 7: // Channel 0-3 count registers
      {
        uint8_t channel = (addr - 1) / 2;
        if (channel < 4) {
          if (m_dma.channels[channel].count_latch) {
            m_dma.channels[channel].count =
                (m_dma.channels[channel].count & 0x00FF) | (*data << 8);
            m_dma.channels[channel].count_latch = false;
          } else {
            m_dma.channels[channel].count =
                (m_dma.channels[channel].count & 0xFF00) | *data;
            m_dma.channels[channel].count_latch = true;
          }
        }
      } break;
      case 8: // Command register
        m_dma.command = *data;
        break;
      case 9: // Request register
      {
        uint8_t channel = *data & 0x3;
        bool set_request = (*data & 0x4) != 0;
        if (channel < 4) {
          if (set_request) {
            m_dma.request |= (1 << channel);
          } else {
            m_dma.request &= ~(1 << channel);
          }
        }
      } break;
      case 10: // Single mask register
      {
        uint8_t channel = *data & 0x3;
        bool mask_channel = (*data & 0x4) != 0;
        if (channel < 4) {
          if (mask_channel) {
            m_dma.mask |= (1 << channel);
          } else {
            m_dma.mask &= ~(1 << channel);
          }
        }
      } break;
      case 11: // Mode register
      {
        uint8_t channel = *data & 0x3;
        if (channel < 4) {
          m_dma.channels[channel].mode = *data;
        }
      } break;
      case 12: // Clear byte pointer flip-flop
        for (int i = 0; i < 4; i++) {
          m_dma.channels[i].address_latch = false;
          m_dma.channels[i].count_latch = false;
        }
        break;
      case 13: // Master clear
        for (int i = 0; i < 4; i++) {
          m_dma.channels[i].address = 0;
          m_dma.channels[i].count = 0;
          m_dma.channels[i].mode = 0;
          m_dma.channels[i].address_latch = false;
          m_dma.channels[i].count_latch = false;
        }
        m_dma.command = 0;
        m_dma.status = 0;
        m_dma.request = 0;
        m_dma.mask = 0xF; // Mask all channels
        break;
      case 14: // Clear mask register
        m_dma.mask = 0;
        break;
      case 15: // All mask register
        m_dma.mask = *data & 0xF;
        break;
      }
      // RACE CONDITION FIX: Use atomic operations for statistics
      m_stats.dma_writes.fetch_add(1, std::memory_order_relaxed);
      ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    }
  };
  if (!RegisterDevice("DMA", 0x00, 16, readDMA, writeDMA)) {
    throw IOException("Failed to register DMA device");
  }
  spdlog::info("Initialized enhanced DMA at port 0x00 with 4 channels and "
               "transfer support");
}




/**
 * @brief Handles port I/O operations.
 * @details Routes port I/O to the appropriate device handler.
 * @param port Port number.
 * @param data Data buffer.
 * @param size Data size.
 * @param isRead True for read, false for write.
 * @return True on success, false if no device is mapped.
 */
bool IOManager::HandlePortIO(uint16_t port, uint8_t *data, size_t size,
                             bool isRead) {
  IO_LOCK(m_deviceMutex, "DeviceMutex");
  for (const auto &entry : m_devices) {
    const auto &name = entry.first;
    const auto &dev = entry.second;
    if (port >= dev.baseAddr && port + size <= dev.baseAddr + dev.size) {
      try {
        if (isRead) {
          // Convert x86_64::Device interface to IOManager interface
          uint64_t value = dev.device->Read(port - dev.baseAddr, static_cast<uint8_t>(size));
          // Copy the value to the data buffer based on size
          switch (size) {
          case 1:
            *data = static_cast<uint8_t>(value);
            break;
          case 2:
            *reinterpret_cast<uint16_t*>(data) = static_cast<uint16_t>(value);
            break;
          case 4:
            *reinterpret_cast<uint32_t*>(data) = static_cast<uint32_t>(value);
            break;
          case 8:
            *reinterpret_cast<uint64_t*>(data) = value;
            break;
          default:
            spdlog::error("Unsupported port read size: {}", size);
            return false;
          }
        } else {
          // Convert IOManager interface to x86_64::Device interface
          uint64_t value = 0;
          switch (size) {
          case 1:
            value = *data;
            break;
          case 2:
            value = *reinterpret_cast<const uint16_t*>(data);
            break;
          case 4:
            value = *reinterpret_cast<const uint32_t*>(data);
            break;
          case 8:
            value = *reinterpret_cast<const uint64_t*>(data);
            break;
          default:
            spdlog::error("Unsupported port write size: {}", size);
            return false;
          }
          dev.device->Write(port - dev.baseAddr, value, static_cast<uint8_t>(size));
        }
        spdlog::trace("Port {} from device {} at 0x{:x} (size: {})",
                      isRead ? "read" : "write", name, port, size);
        ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
        return true;
      } catch (const std::exception &e) {
        spdlog::warn("Port {} error for device {} at 0x{:x}: {}",
                     isRead ? "read" : "write", name, port, e.what());
        return false;
      }
    }
  }
  for (const auto &entry : m_legacyDevices) {
    const auto &name = entry.first;
    const auto &dev = entry.second;
    if (port >= dev.baseAddr && port + size <= dev.baseAddr + dev.size) {
      try {
        if (isRead && dev.readHandler) {
          dev.readHandler(port - dev.baseAddr, data, size);
          spdlog::trace("Port read from legacy device {} at 0x{:x} (size: {})",
                        name, port, size);
          ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
          return true;
        } else if (!isRead && dev.writeHandler) {
          dev.writeHandler(port - dev.baseAddr, data, size);
          spdlog::trace("Port write to legacy device {} at 0x{:x} (size: {})",
                        name, port, size);
          ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
          return true;
        }
      } catch (const std::exception &e) {
        spdlog::warn("Port {} error for legacy device {} at 0x{:x}: {}",
                     isRead ? "read" : "write", name, port, e.what());
        return false;
      }
    }
  }
  spdlog::warn("No device mapped for port {} at 0x{:x}",
               isRead ? "read" : "write", port);
  return false;
}

/**
 * @brief Processes the event queue and updates PIT ticks.
 * @details Integrates with FiberManager for fiber-aware event handling.
 */
void IOManager::Cycle() {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL FIX: Prevent deadlock by minimizing lock scope and avoiding nested
  // locks
  bool shouldRaiseTimerIRQ = false;
  {
    IO_LOCK(m_pitMutex, "PITMutex");
    m_pit.ticks++;
    if (m_pit.frequency > 0 &&
        m_pit.ticks % (1000000000 / m_pit.frequency) == 0) {
      shouldRaiseTimerIRQ = true;
      // RACE CONDITION FIX: Use atomic operations for statistics
      m_stats.pit_ticks.fetch_add(1, std::memory_order_relaxed);
    }
  }

  // Create timer event outside of PIT mutex to prevent deadlock
  if (shouldRaiseTimerIRQ) {
    Event event;
    event.type = Event::Type::IRQ;
    event.timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
                          std::chrono::steady_clock::now().time_since_epoch())
                          .count();
    event.callback = [this]() {
      RaiseIRQ(0, 128); // High priority for timer
      ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    };

    // Add event to queue with separate lock
    {
      IO_LOCK(m_eventMutex, "EventMutex");
      m_eventQueue.push(std::move(event));
    }
  }

  std::vector<Event> events_to_process;
  {
    IO_LOCK(m_eventMutex, "EventMutex");
    auto now_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                      std::chrono::steady_clock::now().time_since_epoch())
                      .count();
    while (!m_eventQueue.empty() && m_eventQueue.top().timestamp <= now_ns) {
      events_to_process.push_back(
          std::move(const_cast<Event &>(m_eventQueue.top())));
      m_eventQueue.pop();
    }
  }
  // Implement proper fiber management integration
  auto &fiberManager = m_emulator.GetFiberManager();
  uint64_t currentFiberId = fiberManager.GetCurrentFiberId();

  for (auto &evt : events_to_process) {
    if (evt.callback) {
      try {
        // Check if we need to yield to other fibers during event processing
        if (currentFiberId != 0 && events_to_process.size() > 10) {
          // For large event batches, yield occasionally to maintain
          // responsiveness
          static thread_local size_t eventCounter = 0;
          if ((++eventCounter % 5) == 0) {
            fiberManager.YieldFiber();
          }
        }

        evt.callback();
        // RACE CONDITION FIX: Use atomic operations for statistics
        m_stats.event_callbacks.fetch_add(1, std::memory_order_relaxed);
      } catch (const std::exception &e) {
        spdlog::error("Exception in IOManager event callback: {}", e.what());
      }
    }
  }

  auto end = std::chrono::steady_clock::now();
  m_stats.total_latency_us.fetch_add(
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count(),
      std::memory_order_relaxed);
  ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
}

/**
 * @brief Saves the IOManager state.
 * @details Serializes device states, statistics, and event queue metrics.
 * @param out The output stream.
 */
void IOManager::SaveState(std::ostream &out) const {
  IO_LOCK(m_deviceMutex, "DeviceMutex");
  uint32_t version = 1;
  out.write(reinterpret_cast<const char *>(&version), sizeof(version));
  uint32_t deviceCount = static_cast<uint32_t>(m_devices.size());
  out.write(reinterpret_cast<const char *>(&deviceCount), sizeof(deviceCount));
  for (const auto &entry : m_devices) {
    entry.second.device->SaveState(out);
  }
  out.write(reinterpret_cast<const char *>(&m_pit), sizeof(m_pit));
  out.write(reinterpret_cast<const char *>(&m_keyboard), sizeof(m_keyboard));
  out.write(reinterpret_cast<const char *>(&m_pic), sizeof(m_pic));
  out.write(reinterpret_cast<const char *>(&m_dma), sizeof(m_dma));
  out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));
  uint32_t eventQueueSize = static_cast<uint32_t>(m_eventQueue.size());
  out.write(reinterpret_cast<const char *>(&eventQueueSize),
            sizeof(eventQueueSize));
  spdlog::info("IOManager state saved: {} devices, {} queued events",
               deviceCount, eventQueueSize);
}

/**
 * @brief Loads the IOManager state.
 * @details Deserializes device states, statistics, and clears event queue.
 * @param in The input stream.
 */
void IOManager::LoadState(std::istream &in) {
  IO_LOCK(m_deviceMutex, "DeviceMutex");
  uint32_t version;
  in.read(reinterpret_cast<char *>(&version), sizeof(version));
  if (version != 1) {
    spdlog::error("Unsupported IOManager state version: {}", version);
    throw IOException("Invalid IOManager state version");
  }
  uint32_t deviceCount;
  in.read(reinterpret_cast<char *>(&deviceCount), sizeof(deviceCount));
  for (uint32_t i = 0; i < deviceCount && in.good(); ++i) {
    for (auto &entry : m_devices) {
      entry.second.device->LoadState(in);
    }
  }
  in.read(reinterpret_cast<char *>(&m_pit), sizeof(m_pit));
  in.read(reinterpret_cast<char *>(&m_keyboard), sizeof(m_keyboard));
  in.read(reinterpret_cast<char *>(&m_pic), sizeof(m_pic));
  in.read(reinterpret_cast<char *>(&m_dma), sizeof(m_dma));
  in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
  uint32_t eventQueueSize;
  in.read(reinterpret_cast<char *>(&eventQueueSize), sizeof(eventQueueSize));
  while (!m_eventQueue.empty()) {
    m_eventQueue.pop();
  }
  spdlog::info("IOManager state loaded: {} devices, cleared {} queued events",
               deviceCount, eventQueueSize);
  ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
}
} // namespace x86_64
