#include "ps4_mmu.h"
#include "../common/lock_ordering.h"
#include "../debug/vector_debug.h"
// Undefine STRICT macro conflicting with PS4MMU fiber integration
#ifdef STRICT
#undef STRICT
#endif
#include "../ps4/ps4_emulator.h"
#include "fmt/core.h"
#include "memory.h"
#include <algorithm>
#include <chrono>
#include <compressapi.h>
#include <cstring>
#include <fstream>
#include <memory>
#include <new>
#include <nlohmann/json.hpp>
#include <spdlog/fmt/ostr.h>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <thread>

#ifdef _WIN32
#include <memoryapi.h>
#endif
#include <compressapi.h>
#include <iterator>

// Undefine Windows macros that interfere with std::min/max
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif

using namespace ps4;

/**
 * @brief Helper function to calculate latency in microseconds avoiding
 * consteval issues
 * @param start Start time point
 * @param end End time point
 * @return Latency in microseconds
 */
static uint64_t
CalculateLatencyMicroseconds(const std::chrono::steady_clock::time_point &start,
                             const std::chrono::steady_clock::time_point &end) {
  return std::chrono::duration_cast<std::chrono::microseconds>(end - start)
      .count();
}

/**
 * @brief Constructs an MMU with a memory reference.
 * @param memory Reference to the memory backend.
 */
PS4MMU::PS4MMU(Memory &memory)
    : m_memory(&memory), m_emulator(&PS4Emulator::GetInstance()) {
  auto start = std::chrono::steady_clock::now();
  spdlog::info("Constructing PS4MMU with memory reference...");
  try {
    m_size = TOTAL_SIZE;
    auto sizeMB = m_size / (1024 * 1024);
    spdlog::info("Attempting to allocate {} MB of physical memory...",
                 sizeMB); // Try to allocate the physical memory buffer with
                          // error handling
    try {
      m_physicalMemory.resize(m_size);
      auto successSizeMB = m_size / (1024 * 1024);
      spdlog::info("Successfully allocated {} MB of physical memory",
                   successSizeMB);
    } catch (const std::bad_alloc &e) {
      // If we can't allocate the full size, try a smaller amount
      m_size = 512ULL * 1024 * 1024; // 512MB fallback
      auto totalSizeGB = TOTAL_SIZE / (1024 * 1024 * 1024);
      auto fallbackSizeMB = m_size / (1024 * 1024);
      spdlog::warn("Failed to allocate {}GB, falling back to {}MB", totalSizeGB,
                   fallbackSizeMB);
      m_physicalMemory.resize(m_size);
      auto finalSizeMB = m_size / (1024 * 1024);
      spdlog::info("Successfully allocated fallback {} MB of physical memory",
                   finalSizeMB);
    }

    m_physAlloc = std::make_unique<PhysicalMemoryAllocator>();
    m_physAlloc->Initialize(m_size);
    m_prefetcher = std::make_unique<MemoryPrefetcher>();
    m_compressor = std::make_unique<MemoryCompressor>();
    m_swapManager =
        std::make_unique<SwapManager>("ps4_swap.bin", TOTAL_SIZE / 2);
    m_compressionEnabled = false;
    m_prefetchHint = PrefetchHint::PREFETCH_NONE;
    m_tlb = std::make_unique<ps4::TLB>();
    if (!m_compressor->Initialize()) {
      throw std::runtime_error("Memory compressor initialization failed");
    }
    if (!m_swapManager->Initialize()) {
      throw std::runtime_error("Swap manager initialization failed");
    }

    // Initialize memory statistics properly
    auto totalPages = m_size / PAGE_SIZE;
    m_stats.totalPages.store(totalPages);
    m_stats.freePages.store(totalPages);
    m_stats.usedPages.store(0);
    // LOGIC FIX: Load atomic values to avoid consteval issues
    auto totalPagesLoaded = m_stats.totalPages.load();
    auto freePagesLoaded = m_stats.freePages.load();
    spdlog::info(
        "PS4MMU: Initialized memory stats - total pages: {}, free pages: {}",
        totalPagesLoaded, freePagesLoaded);

    auto end = std::chrono::steady_clock::now();
    // LOGIC FIX: Use helper to avoid consteval issues
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("PS4MMU constructed with size: {:#x} bytes", m_size);
  } catch (const std::exception &e) {
    spdlog::critical("PS4MMU construction failed: {}", e.what());
    throw;
  }
}

/**
 * @brief Default constructor.
 */
PS4MMU::PS4MMU() : m_memory(nullptr), m_emulator(&PS4Emulator::GetInstance()) {
  auto start = std::chrono::steady_clock::now();
  spdlog::info("Constructing PS4MMU (default)...");
  try {
    m_size = TOTAL_SIZE;
    auto sizeMB = m_size / (1024 * 1024);
    spdlog::info("Attempting to allocate {} MB of physical memory...", sizeMB);

    // Try to allocate the physical memory buffer with error handling
    try {
      m_physicalMemory.resize(m_size);
      spdlog::info("Successfully allocated {} MB of physical memory",
                   m_size / (1024 * 1024));
    } catch (const std::bad_alloc &e) {
      // If we can't allocate the full size, try a smaller amount
      m_size = 512ULL * 1024 * 1024; // 512MB fallback
      spdlog::warn("Failed to allocate {}MB, falling back to {}MB",
                   TOTAL_SIZE / (1024 * 1024), m_size / (1024 * 1024));
      m_physicalMemory.resize(m_size);
      spdlog::info("Successfully allocated fallback {} MB of physical memory",
                   m_size / (1024 * 1024));
    }

    m_physAlloc = std::make_unique<PhysicalMemoryAllocator>();
    m_physAlloc->Initialize(m_size);
    m_prefetcher = std::make_unique<MemoryPrefetcher>();
    m_compressor = std::make_unique<MemoryCompressor>();
    m_swapManager =
        std::make_unique<SwapManager>("ps4_swap.bin", TOTAL_SIZE / 2);
    m_compressionEnabled = false;
    m_prefetchHint = PrefetchHint::PREFETCH_NONE;
    m_tlb = std::make_unique<ps4::TLB>();
    if (!m_compressor->Initialize()) {
      throw std::runtime_error("Memory compressor initialization failed");
    }
    if (!m_swapManager->Initialize()) {
      throw std::runtime_error("Swap manager initialization failed");
    }

    // Initialize memory statistics properly
    auto totalPages = m_size / PAGE_SIZE;
    m_stats.totalPages.store(totalPages);
    m_stats.freePages.store(totalPages);
    m_stats.usedPages.store(0);
    spdlog::info(
        "PS4MMU: Initialized memory stats - total pages: {}, free pages: {}",
        m_stats.totalPages.load(), m_stats.freePages.load());

    auto end = std::chrono::steady_clock::now();
    // LOGIC FIX: Use helper to avoid consteval issues
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("PS4MMU default constructed with size: {:#x} bytes", m_size);
  } catch (const std::exception &e) {
    spdlog::critical("PS4MMU default construction failed: {}", e.what());
    throw;
  }
}

/**
 * @brief Constructs an MMU with memory and emulator references.
 * @param memory Reference to the memory backend.
 * @param emulator Reference to the PS4 emulator.
 */
PS4MMU::PS4MMU(Memory &memory, PS4Emulator &emulator)
    : m_memory(&memory), m_emulator(&emulator) {
  auto start = std::chrono::steady_clock::now();
  spdlog::info("Constructing PS4MMU with memory and emulator references...");
  try {
    m_size = TOTAL_SIZE;
    auto sizeMB = m_size / (1024 * 1024);
    spdlog::info("Attempting to allocate {} MB of physical memory...",
                 sizeMB); // Try to allocate the physical memory buffer with
                          // error handling
    try {
      m_physicalMemory.resize(m_size);
      auto successSizeMB = m_size / (1024 * 1024);
      spdlog::info("Successfully allocated {} MB of physical memory",
                   successSizeMB);
    } catch (const std::bad_alloc &e) {
      // If we can't allocate the full size, try a smaller amount
      m_size = 512ULL * 1024 * 1024; // 512MB fallback
      auto totalSizeGB = TOTAL_SIZE / (1024 * 1024 * 1024);
      auto fallbackSizeMB = m_size / (1024 * 1024);
      spdlog::warn("Failed to allocate {}GB, falling back to {}MB", totalSizeGB,
                   fallbackSizeMB);
      m_physicalMemory.resize(m_size);
      auto finalSizeMB = m_size / (1024 * 1024);
      spdlog::info("Successfully allocated fallback {} MB of physical memory",
                   finalSizeMB);
    }

    m_physAlloc = std::make_unique<PhysicalMemoryAllocator>();
    m_physAlloc->Initialize(m_size);
    m_prefetcher = std::make_unique<MemoryPrefetcher>();
    m_compressor = std::make_unique<MemoryCompressor>();
    m_swapManager =
        std::make_unique<SwapManager>("ps4_swap.bin", TOTAL_SIZE / 2);
    m_compressionEnabled = false;
    m_prefetchHint = PrefetchHint::PREFETCH_NONE;
    m_tlb = std::make_unique<ps4::TLB>();
    if (!m_compressor->Initialize()) {
      throw std::runtime_error("Memory compressor initialization failed");
    }
    if (!m_swapManager->Initialize()) {
      throw std::runtime_error("Swap manager initialization failed");
    }

    // Initialize memory statistics properly
    auto totalPages = m_size / PAGE_SIZE;
    m_stats.totalPages.store(totalPages);
    m_stats.freePages.store(totalPages);
    m_stats.usedPages.store(0);
    // LOGIC FIX: Load atomic values to avoid consteval issues
    auto totalPagesLoaded = m_stats.totalPages.load();
    auto freePagesLoaded = m_stats.freePages.load();
    spdlog::info(
        "PS4MMU: Initialized memory stats - total pages: {}, free pages: {}",
        totalPagesLoaded, freePagesLoaded);

    auto end = std::chrono::steady_clock::now();
    // LOGIC FIX: Use helper to avoid consteval issues
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("PS4MMU constructed with size: {:#x} bytes", m_size);
  } catch (const std::exception &e) {
    spdlog::critical("PS4MMU construction failed: {}", e.what());
    throw;
  }
}

uint64_t ps4::PS4MMU::GetTotalAllocated() const {
  if (m_physAlloc) {
    return m_physAlloc->GetStats().totalAllocations;
  }
  return 0;
}

uint64_t ps4::PS4MMU::GetTotalFreed() const {
  if (m_physAlloc) {
    return m_physAlloc->GetStats().totalFrees;
  }
  return 0;
}

uint64_t ps4::PS4MMU::GetCurrentAllocated() const {
  if (m_physAlloc) {
    return m_physAlloc->GetStats().currentAllocations;
  }
  return 0;
}

uint64_t ps4::PS4MMU::GetPeakAllocated() const {
  if (m_physAlloc) {
    return m_physAlloc->GetStats().peakAllocations;
  }
  return 0;
}

uint64_t ps4::PS4MMU::GetAllocationCount() const {
  if (m_physAlloc) {
    return m_physAlloc->GetStats().allocationCount;
  }
  return 0;
}

uint64_t ps4::PS4MMU::GetFreeCount() const {
  if (m_physAlloc) {
    return m_physAlloc->GetStats().freeCount;
  }
  return 0;
}

/* Removed duplicate definition of PS4MMU::Initialize. */

/**
 * @brief Frees virtual memory.
 * @param virtAddr Virtual address to free.
 * @param processId Process ID.
 */

// Duplicate definition of PS4MMU::FreeVirtual removed to avoid redefinition
// errors.

/**
 * @brief Unmaps virtual memory.
 * @param virtAddr Virtual address.
 * @param size Size to unmap.
 * @param processId Process ID.
 * @return True on success, false otherwise.
 */
/* Duplicate definition of PS4MMU::UnmapMemory removed to avoid redefinition
 * errors */

/**
 * @brief Sets memory protection.
 * @param virtAddr Virtual address.
 * @param size Size to protect.
 * @param protection Protection flags.
 * @param processId Process ID.
 * @return True on success, false otherwise.
 */
bool PS4MMU::ProtectMemory(uint64_t virtAddr, size_t size, int protection,
                           uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");
  spdlog::info("Protecting memory: virt=0x{:x}, size={:#x}, protection=0x{:x}",
               virtAddr, size, protection);
  try {
    auto &pageTable = m_pageTables[processId];
    for (uint64_t addr = virtAddr; addr < virtAddr + size; addr += PAGE_SIZE) {
      uint64_t pageIndex = addr / PAGE_SIZE;
      if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present) {
        spdlog::error("Cannot protect invalid page: 0x{:x}", addr);
        return false;
      }
      pageTable[pageIndex].protection = protection;
      m_tlb->Invalidate(addr, processId);
    }
    auto end = std::chrono::steady_clock::now();
    // LOGIC FIX: Use helper to avoid consteval issues
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Protected memory: virt=0x{:x}, size={:#x}", virtAddr, size);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Memory protection failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Reads from virtual memory.
 * @param virtAddr Virtual address.
 * @param data Output buffer.
 * @param size Size to read.
 * @param processId Process ID.
 * @return True on success, false otherwise.
 */
bool PS4MMU::ReadVirtual(uint64_t virtAddr, void *data, size_t size,
                         uint64_t processId) const {
  auto start = std::chrono::steady_clock::now();

  // CRITICAL SAFETY: Validate input parameters before acquiring any locks
  if (data == nullptr) {
    spdlog::error("ReadVirtual: null data pointer for virt=0x{:x}, size={:#x}",
                  virtAddr, size);
    return false;
  }

  if (size == 0) {
    spdlog::warn("ReadVirtual: zero size read at virt=0x{:x}", virtAddr);
    return true; // Zero-size reads are technically successful
  }

  // CRITICAL SAFETY: Validate virtual address ranges
  if (virtAddr == 0 || virtAddr == 0xDEADBEEF || virtAddr == 0xCCCCCCCC ||
      virtAddr == 0xFEEEFEEE) {
    spdlog::error("ReadVirtual: invalid virtual address 0x{:x}", virtAddr);
    return false;
  }

  // Check for address overflow
  if (virtAddr > UINT64_MAX - size) {
    spdlog::error("ReadVirtual: address overflow virt=0x{:x}, size={:#x}",
                  virtAddr, size);
    return false;
  }

  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  spdlog::debug("Reading virtual memory: virt=0x{:x}, size={:#x}, process={}",
                virtAddr, size, processId);

  try {
    // CRITICAL SAFETY: Validate process ID exists
    if (m_pageTables.find(processId) == m_pageTables.end()) {
      spdlog::error("ReadVirtual: invalid process ID {}", processId);
      m_stats.pageFaults.fetch_add(1, std::memory_order_relaxed);
      return false;
    }

    uint64_t physAddr = VirtualToPhysical(virtAddr, processId, false);
    if (physAddr == ALLOC_FAILED) {
      spdlog::error("Invalid virtual read (VtoP failed): 0x{:x}", virtAddr);
      m_stats.pageFaults.fetch_add(1, std::memory_order_relaxed);
      return false;
    }

    // CRITICAL SAFETY: Additional physical address validation
    if (physAddr == 0 || physAddr == 0xDEADBEEF || physAddr == 0xCCCCCCCC) {
      spdlog::error(
          "ReadVirtual: invalid physical address 0x{:x} from virt=0x{:x}",
          physAddr, virtAddr);
      return false;
    }

    // CRITICAL: Safe map access to prevent std::out_of_range exceptions
    auto pageTableIt = m_pageTables.find(processId);
    if (pageTableIt == m_pageTables.end()) {
      spdlog::error("ReadVirtual: Process {} not found in page tables",
                    processId);
      m_stats.pageFaults.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    auto &pageTable = pageTableIt->second;
    uint64_t pageIndex = virtAddr / PAGE_SIZE;
    VALIDATE_VECTOR(pageTable, "PS4MMU::ReadVirtual pageTable");
    if (pageIndex >= pageTable.size() || !SAFE_VECTOR_ACCESS(pageTable, pageIndex, "PS4MMU::ReadVirtual pageTable entry").present) {
      spdlog::error("Page not present: 0x{:x}", virtAddr);
      m_stats.pageFaults.fetch_add(1, std::memory_order_relaxed);
      return false;
    }

    const auto& pageEntry = SAFE_VECTOR_ACCESS(pageTable, pageIndex, "PS4MMU::ReadVirtual pageTable entry");
    CheckProtection(pageEntry, false, virtAddr);
    CheckMemoryTypeAccess(pageEntry.type, false, virtAddr, pageEntry.protection);

    // Check for integer overflow before bounds checking
    if (size > 0 && physAddr > UINT64_MAX - size) {
      spdlog::error(
          "Integer overflow in read bounds check: phys=0x{:x}, size={:#x}",
          physAddr, size);
      return false;
    }

    if (physAddr + size > m_size) {
      spdlog::error("Read out of bounds: phys=0x{:x}, size={:#x}, m_size={:#x}",
                    physAddr, size, m_size);
      return false;
    }

    // CRITICAL SAFETY: Validate physical memory buffer before access
    VALIDATE_VECTOR(m_physicalMemory, "PS4MMU::ReadVirtual physicalMemory");
    if (m_physicalMemory.empty() || physAddr >= m_physicalMemory.size()) {
      spdlog::error(
          "ReadVirtual: physical memory buffer invalid or out of bounds");
      return false;
    }

    // Additional bounds check for memcpy operation
    if (physAddr + size > m_physicalMemory.size()) {
      spdlog::error("ReadVirtual: memcpy would exceed physical memory bounds: phys=0x{:x}, size=0x{:x}, buffer_size=0x{:x}",
                    physAddr, size, m_physicalMemory.size());
      return false;
    }

    std::memcpy(data, &SAFE_VECTOR_ACCESS(m_physicalMemory, physAddr, "PS4MMU::ReadVirtual memcpy source"), size);

    // RACE CONDITION FIX: Use atomic operations for statistics
    m_readBytes.fetch_add(size, std::memory_order_relaxed);

    // CRITICAL FIX: The mutex will be automatically released at the end of the
    // try block to prevent deadlock before calling HandleAccess

    spdlog::debug("Read virtual memory: virt=0x{:x}, size={:#x}", virtAddr,
                  size);
  } catch (const std::exception &e) {
    spdlog::error("Virtual read failed: {}", e.what());
    m_stats.pageFaults.fetch_add(1, std::memory_order_relaxed);
    return false;
  }

  // Use standard access handling (called after mutex is released)
  const_cast<PS4MMU *>(this)->HandleAccess(virtAddr, size, false, processId);

  auto end = std::chrono::steady_clock::now();
  auto latency = CalculateLatencyMicroseconds(start, end);
  m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

  return true;
}

/**
 * @brief Writes to virtual memory with timeout and chunking for large writes.
 * @param virtAddr Virtual address.
 * @param data Input buffer.
 * @param size Size to write.
 * @param processId Process ID.
 * @return True on success, false otherwise.
 */
bool PS4MMU::WriteVirtual(uint64_t virtAddr, const void *data, size_t size,
                          uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  const auto timeout =
      std::chrono::seconds(30); // 30 second timeout for large writes

  // CRITICAL SAFETY: Validate input parameters before acquiring any locks
  if (data == nullptr) {
    spdlog::error("WriteVirtual: null data pointer for virt=0x{:x}, size={:#x}",
                  virtAddr, size);
    return false;
  }

  if (size == 0) {
    spdlog::warn("WriteVirtual: zero size write at virt=0x{:x}", virtAddr);
    return true; // Zero-size writes are technically successful
  }

  // CRITICAL SAFETY: Validate virtual address ranges
  if (virtAddr == 0 || virtAddr == 0xDEADBEEF || virtAddr == 0xCCCCCCCC ||
      virtAddr == 0xFEEEFEEE) {
    spdlog::error("WriteVirtual: invalid virtual address 0x{:x}", virtAddr);
    return false;
  }

  // Check for address overflow
  if (virtAddr > UINT64_MAX - size) {
    spdlog::error("WriteVirtual: address overflow virt=0x{:x}, size={:#x}",
                  virtAddr, size);
    return false;
  }

  // For large writes (>1MB), break into chunks to prevent hanging
  const size_t CHUNK_SIZE = 1024 * 1024; // 1MB chunks
  if (size > CHUNK_SIZE) {
    spdlog::info("WriteVirtual: Large write detected (size=0x{:x}), breaking "
                 "into chunks",
                 size);

    size_t bytesWritten = 0;
    const uint8_t *dataPtr = static_cast<const uint8_t *>(data);

    while (bytesWritten < size) {
      // Check timeout
      if (std::chrono::steady_clock::now() - start > timeout) {
        spdlog::error("WriteVirtual: Timeout during chunked write at offset "
                      "0x{:x}/0x{:x}",
                      bytesWritten, size);
        return false;
      }

      size_t chunkSize = std::min(CHUNK_SIZE, size - bytesWritten);
      uint64_t chunkAddr = virtAddr + bytesWritten;

      spdlog::debug("WriteVirtual: Writing chunk {}/{} at 0x{:x}, size=0x{:x}",
                    (bytesWritten / CHUNK_SIZE) + 1,
                    (size + CHUNK_SIZE - 1) / CHUNK_SIZE, chunkAddr, chunkSize);

      if (!WriteVirtualChunk(chunkAddr, dataPtr + bytesWritten, chunkSize,
                             processId)) {
        spdlog::error("WriteVirtual: Failed to write chunk at offset 0x{:x}",
                      bytesWritten);
        return false;
      }

      bytesWritten += chunkSize;
    }

    auto end = std::chrono::steady_clock::now();
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

    spdlog::info("WriteVirtual: Successfully wrote 0x{:x} bytes in chunks",
                 size);
    return true;
  }

  // For smaller writes, use the original single-chunk approach
  return WriteVirtualChunk(virtAddr, data, size, processId);
}

/**
 * @brief Writes a single chunk to virtual memory (internal helper).
 * @param virtAddr Virtual address.
 * @param data Input buffer.
 * @param size Size to write.
 * @param processId Process ID.
 * @return True on success, false otherwise.
 */
bool PS4MMU::WriteVirtualChunk(uint64_t virtAddr, const void *data, size_t size,
                               uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  const auto timeout = std::chrono::seconds(10); // 10 second timeout per chunk

  uint64_t physAddr;
  int pageFaultRetries = 0;
  const int maxPageFaultRetries = 3;

  while (pageFaultRetries <= maxPageFaultRetries) {
    // Check timeout
    if (std::chrono::steady_clock::now() - start > timeout) {
      spdlog::error(
          "WriteVirtualChunk: Timeout after {}s for virt=0x{:x}, size=0x{:x}",
          timeout.count(), virtAddr, size);
      return false;
    }

    {
      MEMORY_LOCK(m_mutex, "MMUMutex");

      spdlog::debug(
          "Writing virtual memory chunk: virt=0x{:x}, size=0x{:x}, process={}",
          virtAddr, size, processId);

      // CRITICAL SAFETY: Validate process ID exists
      if (m_pageTables.find(processId) == m_pageTables.end()) {
        spdlog::error("WriteVirtualChunk: invalid process ID {}", processId);
        m_stats.pageFaults.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      physAddr = VirtualToPhysical(virtAddr, processId, true);
    } // Lock released here

    if (physAddr == ALLOC_FAILED) {
      pageFaultRetries++;
      spdlog::warn("WriteVirtualChunk: VtoP failed for virt=0x{:x}, triggering "
                   "page fault (attempt {}/{})",
                   virtAddr, pageFaultRetries, maxPageFaultRetries + 1);

      if (!HandlePageFault(virtAddr, processId, true)) {
        spdlog::error(
            "WriteVirtualChunk: page fault handling failed for virt=0x{:x}",
            virtAddr);
        ExportMemoryStats("memory_stats_pagefault.json");
        return false;
      }

      // Continue to retry
      continue;
    }

    // Successfully got physical address, break out of retry loop
    break;
  }

  if (physAddr == ALLOC_FAILED) {
    spdlog::error("WriteVirtualChunk: VtoP failed after {} page fault retries: "
                  "virt=0x{:x}",
                  maxPageFaultRetries, virtAddr);
    ExportMemoryStats("memory_stats_vtop.json");
    return false;
  }

  {
    MEMORY_LOCK(m_mutex, "MMUMutex");
    try {

      // CRITICAL: Safe map access to prevent std::out_of_range exceptions
      auto pageTableIt = m_pageTables.find(processId);
      if (pageTableIt == m_pageTables.end()) {
        spdlog::error("WriteVirtualChunk: Process {} not found in page tables",
                      processId);
        m_stats.pageFaults.fetch_add(1, std::memory_order_relaxed);
        return false;
      }
      auto &pageTable = pageTableIt->second;
      uint64_t pageIndex = virtAddr / PAGE_SIZE;
      if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present) {
        spdlog::error("WriteVirtualChunk: page not present: virt=0x{:x}",
                      virtAddr);
        m_stats.pageFaults.fetch_add(1, std::memory_order_relaxed);
        // Need to release lock to handle page fault
        return false; // Will handle page fault outside of lock
      }

      CheckProtection(pageTable[pageIndex], true, virtAddr);
      CheckMemoryTypeAccess(pageTable[pageIndex].type, true, virtAddr,
                            pageTable[pageIndex].protection);

      // Check for integer overflow before bounds checking
      if (size > 0 && physAddr > UINT64_MAX - size) {
        spdlog::error("WriteVirtualChunk: integer overflow in bounds check: "
                      "phys=0x{:x}, size=0x{:x}",
                      physAddr, size);
        return false;
      }

      if (physAddr + size > m_size) {
        spdlog::error("WriteVirtualChunk: out of bounds: phys=0x{:x}, "
                      "size=0x{:x}, m_size=0x{:x}",
                      physAddr, size, m_size);
        return false;
      }

      VALIDATE_VECTOR(m_physicalMemory, "PS4MMU::WriteVirtualChunk physicalMemory");
      if (m_physicalMemory.empty() || physAddr >= m_physicalMemory.size()) {
        spdlog::error("WriteVirtualChunk: physical memory buffer invalid or "
                      "out of bounds: phys=0x{:x}",
                      physAddr);
        return false;
      }

      // Additional bounds check for memcpy operation
      if (physAddr + size > m_physicalMemory.size()) {
        spdlog::error("WriteVirtualChunk: memcpy would exceed physical memory bounds: phys=0x{:x}, size=0x{:x}, buffer_size=0x{:x}",
                      physAddr, size, m_physicalMemory.size());
        return false;
      }

      std::memcpy(&SAFE_VECTOR_ACCESS(m_physicalMemory, physAddr, "PS4MMU::WriteVirtualChunk memcpy dest"), data, size);
      MarkPageDirty(virtAddr, processId);

      m_writeBytes.fetch_add(size, std::memory_order_relaxed);

      spdlog::debug("Wrote virtual memory chunk: virt=0x{:x}, size=0x{:x}",
                    virtAddr, size);
    } catch (const std::exception &e) {
      spdlog::error("WriteVirtualChunk failed: {}", e.what());
      m_stats.pageFaults.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
  } // Lock released here

  // Call HandleAccess after releasing mutex to prevent deadlock
  HandleAccess(virtAddr, size, true, processId);

  auto end = std::chrono::steady_clock::now();
  auto latency = CalculateLatencyMicroseconds(start, end);
  m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

  return true;
}
/**
 * @brief Maps virtual to physical memory.
 * @param virtAddr Virtual address.
 * @param physAddr Physical address.
 * @param size Size to map.
 * @param protection Protection flags.
 * @param processId Process ID.
 * @return True on success, false otherwise.
 */
bool PS4MMU::MapMemory(uint64_t virtAddr, uint64_t physAddr, size_t size,
                       int protection, uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");
  spdlog::info("Mapping memory: virt=0x{:x}, phys=0x{:x}, size={:#x}", virtAddr,
               physAddr, size);
  try {
    if (physAddr + size > m_size) {
      spdlog::error("Invalid physical mapping: phys=0x{:x}, size={:#x}",
                    physAddr, size);
      return false;
    }
    auto &pageTable = m_pageTables[processId];
    for (uint64_t addr = virtAddr; addr < virtAddr + size; addr += PAGE_SIZE) {
      uint64_t pageIndex = addr / PAGE_SIZE;

      PageTableEntry entry;
      entry.physAddr = physAddr + (addr - virtAddr);
      entry.protection = static_cast<int>(protection);
      entry.present = true;
      entry.writable = (protection & PROT_WRITE) != 0;
      entry.user = true;
      entry.accessed = false;
      entry.dirty = false;
      entry.shared = false;
      entry.processId = processId;
      entry.type = MemoryType::Default;
      pageTable[pageIndex] = entry;
      m_tlb->Insert(addr, pageTable[pageIndex].physAddr, PAGE_SIZE, processId);
    }
    auto end = std::chrono::steady_clock::now();
    // LOGIC FIX: Use helper to avoid consteval issues
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Mapped memory: virt=0x{:x}, phys=0x{:x}, size={:#x}",
                 virtAddr, physAddr, size);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Memory mapping failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Maps a virtual address range to a physical address range.
 * @param virtAddr Virtual address to map.
 * @param physAddr Physical address to map to.
 * @param size Size of the mapping.
 * @param processId Process ID for the mapping.
 * @return True on success, false otherwise.
 */
bool PS4MMU::MapVirtualToPhysical(uint64_t virtAddr, uint64_t physAddr,
                                  uint64_t size, uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");

  spdlog::info("Mapping virtual to physical: virt=0x{:x} -> phys=0x{:x}, "
               "size=0x{:x}, process={}",
               virtAddr, physAddr, size, processId);

  try {
    // Validate inputs
    if (size == 0) {
      spdlog::error("Invalid mapping size: 0");
      return false;
    }
    if (virtAddr % PAGE_SIZE != 0 || physAddr % PAGE_SIZE != 0) {
      spdlog::error("Non-page-aligned addresses: virt=0x{:x}, phys=0x{:x}",
                    virtAddr, physAddr);
      return false;
    }
    if (virtAddr > UINT64_MAX - size || physAddr > UINT64_MAX - size) {
      spdlog::error("Address overflow: virt=0x{:x}, phys=0x{:x}, size=0x{:x}",
                    virtAddr, physAddr, size);
      return false;
    }

    // Ensure process page table exists
    auto it = m_pageTables.find(processId);
    if (it == m_pageTables.end()) {
      spdlog::info("Creating new page table for process {}", processId);
      m_pageTables[processId] =
          std::vector<PageTableEntry>(INITIAL_PAGE_TABLE_SIZE);
      it = m_pageTables.find(processId);
    }
    auto &pageTable = it->second;

    // Calculate page-aligned addresses and size
    uint64_t alignedVirtAddr = virtAddr & ~(PAGE_SIZE - 1);
    uint64_t alignedPhysAddr = physAddr & ~(PAGE_SIZE - 1);
    uint64_t alignedSize = (size + PAGE_SIZE - 1) & ~(PAGE_SIZE - 1);
    uint64_t numPages = alignedSize / PAGE_SIZE;

    spdlog::debug("Aligned mapping: virt=0x{:x} -> phys=0x{:x}, pages={}",
                  alignedVirtAddr, alignedPhysAddr, numPages);

    // Timeout for large mappings
    const auto maxMapTime = std::chrono::milliseconds(5000);
    auto mapStart = std::chrono::steady_clock::now();

    // Map each page
    for (uint64_t i = 0; i < numPages; ++i) {
      if (std::chrono::steady_clock::now() - mapStart > maxMapTime) {
        spdlog::error(
            "Mapping timeout after {} pages: virt=0x{:x}, size=0x{:x}", i,
            virtAddr, size);
        ExportMemoryStats("memory_stats_timeout.json");
        return false;
      }

      uint64_t currentVirtAddr = alignedVirtAddr + (i * PAGE_SIZE);
      uint64_t currentPhysAddr = alignedPhysAddr + (i * PAGE_SIZE);
      uint64_t pageIndex = currentVirtAddr / PAGE_SIZE;

      // Check if page is already mapped
      if (pageTable[pageIndex].present) {
        spdlog::warn("Page already mapped at virt=0x{:x}, overwriting",
                     currentVirtAddr);
        if (pageTable[pageIndex].physAddr != currentPhysAddr) {
          m_physAlloc->Free(pageTable[pageIndex].physAddr, PAGE_SIZE);
          m_stats.freePages++;
          m_stats.usedPages--;
        }
      }

      // Set up page table entry
      PageTableEntry &entry = pageTable[pageIndex];
      entry.present = true;
      entry.writable = true;
      entry.user = true;
      entry.physAddr = currentPhysAddr;
      entry.protection = PROT_READ | PROT_WRITE | PROT_EXEC;
      entry.type = MemoryType::Default;
      entry.accessed = false;
      entry.dirty = false;
      entry.swapped = false;
      entry.compressedId = 0;

      // Update TLB with timeout
      const int tlbRetries = 3;
      int tlbAttempt = 0;
      bool tlbSuccess = false;
      while (tlbAttempt < tlbRetries) {
        try {
          m_tlb->Insert(currentVirtAddr, currentPhysAddr, PAGE_SIZE, processId);
          tlbSuccess = true;
          spdlog::debug("TLB inserted: virt=0x{:x}, phys=0x{:x}",
                        currentVirtAddr, currentPhysAddr);
          break;
        } catch (const std::exception &e) {
          tlbAttempt++;
          spdlog::warn("TLB insert attempt {}/{} failed: {}", tlbAttempt,
                       tlbRetries, e.what());
          std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
      }
      if (!tlbSuccess) {
        spdlog::error("TLB insert failed after {} retries: virt=0x{:x}",
                      tlbRetries, currentVirtAddr);
        ExportMemoryStats("memory_stats_tlb.json");
        return false;
      }

      spdlog::trace("Mapped page: virt=0x{:x} -> phys=0x{:x}", currentVirtAddr,
                    currentPhysAddr);
    }

    // Update x86-64 page tables if CR3 exists
    auto cr3It = m_processCR3.find(processId);
    if (cr3It != m_processCR3.end()) {
      if (!MapX86_64Memory(alignedVirtAddr, alignedPhysAddr, alignedSize,
                           PROT_READ | PROT_WRITE | PROT_EXEC, processId)) {
        spdlog::error("Failed to map x86-64 page tables for virt=0x{:x}",
                      alignedVirtAddr);
        ExportMemoryStats("memory_stats_x86.json");
        return false;
      }
    }

    // Update statistics
    m_stats.usedPages.fetch_add(numPages, std::memory_order_relaxed);
    m_stats.hits.fetch_add(1, std::memory_order_relaxed);

    auto end = std::chrono::steady_clock::now();
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

    spdlog::info("Successfully mapped {} pages from virt=0x{:x} to phys=0x{:x}",
                 numPages, alignedVirtAddr, alignedPhysAddr);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("MapVirtualToPhysical failed: {}", e.what());
    ExportMemoryStats("memory_stats_error.json");
    return false;
  }
}

/**
 * @brief Allocates a new page with enhanced error handling and timeouts.
 * @param virtAddr Virtual address.
 * @param processId Process ID.
 * @param protection Protection flags.
 * @param shared True if shared memory.
 * @param type Memory type.
 */
void PS4MMU::AllocatePage(uint64_t virtAddr, uint64_t processId, int protection,
                          bool shared, MemoryType type) {
  auto start_time = std::chrono::steady_clock::now();
  spdlog::debug(
      "AllocatePage: Starting allocation for virt=0x{:x}, processId={}",
      virtAddr, processId);

  try {
    spdlog::debug("AllocatePage: Getting page table for processId={}",
                  processId);
    auto &pageTable = m_pageTables[processId];
    uint64_t pageIndex = virtAddr / PAGE_SIZE;
    spdlog::debug("AllocatePage: Calculated pageIndex={} for virt=0x{:x}",
                  pageIndex, virtAddr);
    if (pageIndex >= MAX_PAGES_PER_PROCESS) {
      spdlog::error("AllocatePage: page index out of bounds: {} >= {}",
                    pageIndex, MAX_PAGES_PER_PROCESS);
      throw std::runtime_error("Page index out of bounds");
    }

    if (pageIndex >= pageTable.size()) {
      spdlog::debug("Resizing page table to accommodate index {}", pageIndex);
      pageTable.resize(pageIndex + 1);
    }

    if (pageTable[pageIndex].present) {
      spdlog::warn("AllocatePage: page already allocated at virt=0x{:x}",
                   virtAddr);
      return;
    }

    // Allocate with reduced timeout to prevent hangs
    uint64_t physAddr = 0;
    const int allocRetries = 2; // Reduced from 3 to 2
    int allocAttempt = 0;
    while (allocAttempt < allocRetries) {
      auto allocStart = std::chrono::steady_clock::now();
      physAddr = m_physAlloc->Allocate(PAGE_SIZE);
      auto allocDuration =
          std::chrono::duration_cast<std::chrono::milliseconds>(
              std::chrono::steady_clock::now() - allocStart)
              .count();
      if (physAddr != ALLOC_FAILED) {
        spdlog::debug("AllocatePage: Allocated phys=0x{:x} in {}ms", physAddr,
                      allocDuration);
        break;
      }
      allocAttempt++;
      spdlog::warn(
          "AllocatePage: Allocation attempt {}/{} failed, retrying after 50ms",
          allocAttempt, allocRetries);
      std::this_thread::sleep_for(
          std::chrono::milliseconds(50)); // Reduced from 100ms to 50ms
    }
    if (physAddr == ALLOC_FAILED) {
      spdlog::error("AllocatePage: Physical allocation failed after {} "
                    "retries: virt=0x{:x}",
                    allocRetries, virtAddr);
      // Log memory statistics to help diagnose the issue
      auto stats = m_physAlloc->GetStats();
      spdlog::error("Physical memory stats: allocationCount={}, "
                    "fragmentationCount={}, totalLatencyUs={}",
                    stats.allocationCount, stats.fragmentationCount,
                    stats.totalLatencyUs);
      ExportMemoryStats("memory_stats_phys_allocate.json");
      throw std::runtime_error(
          "Physical allocation failure - possibly out of memory");
    }

    PageTableEntry entry;
    entry.physAddr = physAddr;
    entry.protection = protection;
    entry.present = true;
    entry.writable = (protection & PROT_WRITE) != 0;
    entry.user = true;
    entry.accessed = false;
    entry.dirty = false;
    entry.shared = shared;
    entry.processId = processId;
    entry.type = type;
    entry.compressedId = 0;
    entry.accessCount = 0;
    entry.lastAccessTime = std::chrono::steady_clock::now();
    entry.swapped = false;
    entry.copyOnWrite = false;
    entry.executeDisable = (protection & PROT_EXEC) == 0;
    pageTable[pageIndex] = entry;

    m_stats.usedPages.fetch_add(1, std::memory_order_relaxed);
    auto currentFreePages = m_stats.freePages.load(std::memory_order_relaxed);
    if (currentFreePages > 0) {
      m_stats.freePages.fetch_sub(1, std::memory_order_relaxed);
    }

    // Update TLB with reduced timeout to prevent hangs
    spdlog::debug(
        "AllocatePage: About to insert into TLB: virt=0x{:x}, phys=0x{:x}",
        virtAddr, physAddr);
    const int tlbRetries = 2; // Reduced from 3 to 2
    int tlbAttempt = 0;
    bool tlbSuccess = false;
    while (tlbAttempt < tlbRetries) {
      try {
        spdlog::debug("AllocatePage: TLB insert attempt {}/{}", tlbAttempt + 1,
                      tlbRetries);
        m_tlb->Insert(virtAddr, physAddr, PAGE_SIZE, processId);
        tlbSuccess = true;
        spdlog::debug("AllocatePage: TLB inserted: virt=0x{:x}, phys=0x{:x}",
                      virtAddr, physAddr);
        break;
      } catch (const std::exception &e) {
        tlbAttempt++;
        spdlog::warn("AllocatePage: TLB insert attempt {}/{} failed: {}",
                     tlbAttempt, tlbRetries, e.what());
        std::this_thread::sleep_for(
            std::chrono::milliseconds(25)); // Reduced from 100ms to 25ms
      }
    }
    if (!tlbSuccess) {
      spdlog::error(
          "AllocatePage: TLB insert failed after {} retries: virt=0x{:x}",
          tlbRetries, virtAddr);
      m_physAlloc->Free(physAddr, PAGE_SIZE);
      ExportMemoryStats("memory_stats_tlb_allocate.json");
      throw std::runtime_error("TLB insert failure");
    }

    auto end_time = std::chrono::steady_clock::now();
    auto latency = CalculateLatencyMicroseconds(start_time, end_time);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Allocated page: virt=0x{:x}, phys=0x{:x}", virtAddr,
                 physAddr);
  } catch (const std::exception &e) {
    spdlog::error("AllocatePage for virt=0x{:x} failed: {}", virtAddr,
                  e.what());
    ExportMemoryStats("memory_stats_allocatepage_error.json");
    throw;
  }
}

/**
 * @brief Translates virtual to physical address with enhanced debugging.
 * @param virtAddr Virtual address.
 * @param processId Process ID.
 * @param write True if write access.
 * @return Physical address, or ALLOC_FAILED if invalid.
 */
uint64_t PS4MMU::VirtualToPhysical(uint64_t virtAddr, uint64_t processId,
                                   bool write) const {
  auto start = std::chrono::steady_clock::now();
  spdlog::info("Translating virtual to physical: virt=0x{:x}, process={}",
               virtAddr, processId);

  try {
    uint64_t tlbResult = 0;
    size_t pageSize = 0;
    bool tlbHit = m_tlb->Lookup(virtAddr, tlbResult, pageSize, processId);

    if (tlbHit) {
      spdlog::trace("TLB hit: virt=0x{:x} -> phys=0x{:x}", virtAddr, tlbResult);
      m_stats.hits.fetch_add(1, std::memory_order_relaxed);
      return tlbResult + (virtAddr % pageSize);
    }

    m_stats.misses.fetch_add(1, std::memory_order_relaxed);

    auto it = m_pageTables.find(processId);
    if (it == m_pageTables.end()) {
      spdlog::warn("No page table for process {}", processId);
      const_cast<PS4MMU *>(this)->ExportMemoryStats(
          "memory_stats_vtop_nopagetable.json");
      return ALLOC_FAILED;
    }
    const auto &pageTable = it->second;
    uint64_t pageIndex = virtAddr / PAGE_SIZE;

    if (pageIndex >= pageTable.size()) {
      spdlog::warn("Page index out of bounds: 0x{:x}, process={}", virtAddr,
                   processId);
      const_cast<PS4MMU *>(this)->ExportMemoryStats(
          "memory_stats_vtop_index.json");
      return ALLOC_FAILED;
    }

    const PageTableEntry &entry = pageTable[pageIndex];
    if (!entry.present) {
      spdlog::warn("Page entry not present: 0x{:x}", virtAddr);
      const_cast<PS4MMU *>(this)->ExportMemoryStats(
          "memory_stats_vtop_notpresent.json");
      return ALLOC_FAILED;
    }

    auto cr3It = m_processCR3.find(processId);
    if (cr3It != m_processCR3.end()) {
      uint64_t physAddrX86 =
          WalkPageTable(virtAddr, processId, write, cr3It->second);
      if (physAddrX86 != ALLOC_FAILED) {
        const_cast<PS4MMU *>(this)->m_tlb->Insert(
            virtAddr & ~(PAGE_SIZE - 1), physAddrX86 & ~(PAGE_SIZE - 1),
            PAGE_SIZE, processId);
        auto end = std::chrono::steady_clock::now();
        auto latency = CalculateLatencyMicroseconds(start, end);
        m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
        spdlog::trace("x86-64 walk: virt=0x{:x} -> phys=0x{:x}", virtAddr,
                      physAddrX86);
        return physAddrX86 + (virtAddr % PAGE_SIZE);
      }
    }

    try {
      CheckProtectionEnhanced(entry, write, entry.user, false, virtAddr);
      CheckMemoryTypeAccess(entry.type, write, virtAddr, entry.protection);
    } catch (const std::exception &e) {
      spdlog::error("Protection check failed for 0x{:x}: {}", virtAddr,
                    e.what());
      const_cast<PS4MMU *>(this)->ExportMemoryStats(
          "memory_stats_vtop_protection.json");
      return ALLOC_FAILED;
    }

    if (entry.compressedId != 0 || entry.swapped) {
      spdlog::warn(
          "Page is compressed/swapped, requires fault handling: 0x{:x}",
          virtAddr);
      const_cast<PS4MMU *>(this)->ExportMemoryStats(
          "memory_stats_vtop_compressed.json");
      return ALLOC_FAILED;
    }

    uint64_t offset = virtAddr % PAGE_SIZE;
    uint64_t physAddr = entry.physAddr + offset;

    const_cast<PS4MMU *>(this)->m_tlb->Insert(
        virtAddr & ~(PAGE_SIZE - 1), entry.physAddr, PAGE_SIZE, processId);

    auto end = std::chrono::steady_clock::now();
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Legacy walk: virt=0x{:x} -> phys=0x{:x}", virtAddr, physAddr);
    return physAddr;
  } catch (const std::exception &e) {
    spdlog::error("Virtual to physical translation failed: {}", e.what());
    const_cast<PS4MMU *>(this)->ExportMemoryStats(
        "memory_stats_vtop_error.json");
    return ALLOC_FAILED;
  }
}

/**
 * @brief Enhanced protection checking with full x86-64 semantics.
 * @param entry Page table entry.
 * @param write True if write access.
 * @param user True if user mode access.
 * @param execute True if execute access.
 * @param virtAddr Virtual address for error reporting.
 */
void PS4MMU::CheckProtectionEnhanced(const PageTableEntry &entry, bool write,
                                     bool user, bool execute,
                                     uint64_t virtAddr) const {
  if (!entry.present) {
    throw std::runtime_error(fmt::format("Page not present: 0x{:x}", virtAddr));
  }

  if (write && !(entry.protection & PROT_WRITE)) {
    throw std::runtime_error(
        fmt::format("Write access denied: 0x{:x}", virtAddr));
  }

  if (!(entry.protection & PROT_READ)) {
    throw std::runtime_error(
        fmt::format("Read access denied: 0x{:x}", virtAddr));
  }

  if (execute) {
    if (entry.executeDisable) {
      throw std::runtime_error(
          fmt::format("Execute access denied (NX): 0x{:x}", virtAddr));
    }
    if (!(entry.protection & PROT_EXEC)) {
      throw std::runtime_error(
          fmt::format("Execute access denied: 0x{:x}", virtAddr));
    }
  }

  if (user && !entry.user) {
    throw std::runtime_error(
        fmt::format("User access to supervisor page denied: 0x{:x}", virtAddr));
  }

  if (entry.cacheDisable) {
    spdlog::trace("Cache disabled for page: 0x{:x}", virtAddr);
  }

  if (entry.writeThrough) {
    spdlog::trace("Write-through caching for page: 0x{:x}", virtAddr);
  }
}

/**
 * @brief Finds a free virtual address range using an efficient algorithm.
 * PERFORMANCE FIX: Improved virtual address allocation algorithm
 * @param processId Process ID.
 * @param size Size to allocate.
 * @param alignment Alignment requirement.
 * @return Virtual address, or ALLOC_FAILED if not found.
 */
uint64_t PS4MMU::FindFreeVirtualRange(uint64_t processId, uint64_t size,
                                      uint64_t alignment) {
  auto start = std::chrono::steady_clock::now();

  auto &pageTable = m_pageTables[processId];
  uint64_t pagesNeeded = (size + PAGE_SIZE - 1) / PAGE_SIZE;

  // Start from a reasonable base address
  uint64_t virtAddr = USERLAND_BASE;

  // Align the starting address
  if (virtAddr % alignment != 0) {
    virtAddr = (virtAddr + alignment - 1) & ~(alignment - 1);
  }

  // Use a more efficient search strategy
  while (virtAddr + size <= USERLAND_BASE + USERLAND_SIZE) {
    uint64_t startPageIndex = virtAddr / PAGE_SIZE;
    uint64_t endPageIndex = startPageIndex + pagesNeeded;

    // Check if we need to extend the page table
    if (endPageIndex > pageTable.size()) {
      // If the range extends beyond current page table, it's free
      spdlog::info("Found free virtual range at 0x{:x} (beyond page table)",
                   virtAddr);
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
      return virtAddr;
    }

    // Check if the entire range is free
    bool isFree = true;
    uint64_t nextOccupiedPage = 0;

    for (uint64_t pageIndex = startPageIndex; pageIndex < endPageIndex;
         ++pageIndex) {
      if (pageTable[pageIndex].present) {
        isFree = false;
        nextOccupiedPage = pageIndex;
        break;
      }
    }

    if (isFree) {
      spdlog::info("Found free virtual range at 0x{:x}", virtAddr);
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
      return virtAddr;
    }

    // Jump to the next potential location after the occupied page
    virtAddr = (nextOccupiedPage + 1) * PAGE_SIZE;

    // Re-align if necessary
    if (virtAddr % alignment != 0) {
      virtAddr = (virtAddr + alignment - 1) & ~(alignment - 1);
    }
  }

  spdlog::error(
      "No free virtual range found for size {:#x} with alignment {:#x}", size,
      alignment);
  auto end = std::chrono::steady_clock::now();
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  return ALLOC_FAILED;
}

// ----- Page Fault Handling Implementations -----

/**
 * @brief Analyzes the type of page fault.
 * @param virtAddr The faulting virtual address.
 * @param processId The ID of the process that faulted.
 * @param write True if the fault was caused by a write access.
 * @return The type of fault.
 */
PS4MMU::FaultType PS4MMU::AnalyzeFaultType(uint64_t virtAddr,
                                           uint64_t processId,
                                           bool write) const {
  auto it = m_pageTables.find(processId);
  if (it == m_pageTables.end()) {
    // No page table for this process, so any access is a fresh allocation.
    return FaultType::DEMAND_PAGING;
  }

  const auto &table = it->second;
  uint64_t idx = virtAddr / PAGE_SIZE;

  if (idx >= table.size() || !table[idx].present) {
    // Page is not in the table or not marked present. This is demand paging.
    return FaultType::DEMAND_PAGING;
  }

  const auto &entry = table[idx];

  // Check for Copy-On-Write (COW)
  if (entry.copyOnWrite && write) {
    return FaultType::COPY_ON_WRITE;
  }

  // Check for protection fault. If it's a write to a read-only page.
  if (write && !(entry.protection & PROT_WRITE)) {
    // Could be a guard page or just a regular protection fault.
    // We'll check for guard pages specifically in its handler.
    return FaultType::GUARD_PAGE;
  }

  // If none of the above, the cause is unknown or another type of protection
  // fault.
  return FaultType::UNKNOWN;
}

/**
 * @brief Handles a guard page fault, typically for stack expansion.
 * @param virtAddr The faulting virtual address.
 * @param processId The ID of the process that faulted.
 * @return True if the fault was handled successfully.
 */
bool PS4MMU::HandleGuardPageFault(uint64_t virtAddr, uint64_t processId) {
  spdlog::warn("Handling Guard Page Fault: virt=0x{:x}, process={}", virtAddr,
               processId);

  uint64_t pageAddr = virtAddr & ~(PAGE_SIZE - 1);
  bool isStackGrowth = false;

  // Check if the faulting address is adjacent to a known stack region.
  // Stacks grow downwards, so a guard page is at `region.start - PAGE_SIZE`.
  for (const auto &region : m_memoryRegions) {
    if (region.type == MemoryType::Stack &&
        pageAddr == region.start - PAGE_SIZE) {
      isStackGrowth = true;
      break;
    }
  }

  if (isStackGrowth) {
    spdlog::info("Stack overflow detected. Extending stack for process {}.",
                 processId);
    try {
      // The faulting page was a guard page. We convert it to a normal stack
      // page.
      AllocatePage(pageAddr, processId, PROT_READ | PROT_WRITE, false,
                   MemoryType::Stack);

      // In a more complex system, we would also update the MemoryRegion
      // and place a *new* guard page below this one.

      spdlog::info("Successfully extended stack to virt=0x{:x}", pageAddr);
      return true;
    } catch (const std::exception &e) {
      spdlog::error("Failed to extend stack for virt=0x{:x}: {}", pageAddr,
                    e.what());
      return false;
    }
  }

  spdlog::error("Guard page fault at 0x{:x} is not a valid stack growth. This "
                "would trigger a segmentation fault.",
                virtAddr);
  return false;
}

/**
 * @brief Handles a demand paging fault by allocating a new page.
 * @param virtAddr The faulting virtual address.
 * @param processId The ID of the process that faulted.
 * @return True if the fault was handled successfully.
 */
bool PS4MMU::HandleDemandPaging(uint64_t virtAddr, uint64_t processId) {
  spdlog::info("Handling demand paging fault: virt=0x{:x}, process={}",
               virtAddr, processId);
  uint64_t pageAddr = virtAddr & ~(PAGE_SIZE - 1);

  try {
    // Allocate a new physical page and map it at the faulting virtual address.
    // Grant read/write permissions by default for general-purpose demand-paged
    // data.
    AllocatePage(pageAddr, processId, PROT_READ | PROT_WRITE, false,
                 MemoryType::Default);
    spdlog::info(
        "Demand paging successful: Allocated and mapped page at virt=0x{:x}",
        pageAddr);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Demand paging failed for virt=0x{:x}: {}", pageAddr,
                  e.what());
    return false;
  }
}

/**
 * @brief Handles a copy-on-write (COW) fault for a forked process.
 * @param virtAddr The faulting virtual address (write attempt on a shared
 * page).
 * @param processId The ID of the process that faulted.
 * @return True if the fault was handled successfully.
 */
bool PS4MMU::HandleCopyOnWrite(uint64_t virtAddr, uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  spdlog::info("Handling copy-on-write for virt=0x{:x}, process={}", virtAddr,
               processId);

  try {
    // CRITICAL: Safe map access to prevent std::out_of_range exceptions
    auto pageTableIt = m_pageTables.find(processId);
    if (pageTableIt == m_pageTables.end()) {
      spdlog::error("HandleCopyOnWrite: Process {} not found in page tables",
                    processId);
      return false;
    }
    auto &pageTable = pageTableIt->second;
    uint64_t pageIndex = virtAddr / PAGE_SIZE;

    if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present ||
        !pageTable[pageIndex].copyOnWrite) {
      spdlog::error("Invalid COW fault: Page not present or not marked for "
                    "COW. virt=0x{:x}",
                    virtAddr);
      return false;
    }

    const PageTableEntry &oldEntry = pageTable[pageIndex];
    uint64_t oldPhysAddr = oldEntry.physAddr;

    // 1. Allocate a new physical page for the private copy.
    uint64_t newPhysAddr = m_physAlloc->Allocate(PAGE_SIZE);
    if (newPhysAddr == ALLOC_FAILED) {
      spdlog::error("Failed to allocate new physical page for COW. virt=0x{:x}",
                    virtAddr);
      // Could trigger swapping or memory compression here if available
      return false;
    }
    m_stats.usedPages++;
    m_stats.freePages--;

    // 2. Copy the content from the shared old page to the new private page.
    // Bounds check before memcpy
    if (newPhysAddr + PAGE_SIZE <= m_physicalMemory.size() &&
        oldPhysAddr + PAGE_SIZE <= m_physicalMemory.size()) {
      std::memcpy(m_physicalMemory.data() + newPhysAddr,
                  m_physicalMemory.data() + oldPhysAddr, PAGE_SIZE);
    } else {
      spdlog::error("HandleCopyOnWrite: Invalid physical addresses for memcpy - newPhysAddr=0x{:x}, oldPhysAddr=0x{:x}, memSize={}",
                   newPhysAddr, oldPhysAddr, m_physicalMemory.size());
      m_physAlloc->Free(newPhysAddr);
      return false;
    }

    // 3. Update the page table entry for the faulting process to point to the
    // new page.
    PageTableEntry &newEntry = pageTable[pageIndex];
    newEntry.physAddr = newPhysAddr;
    newEntry.copyOnWrite = false; // It's a private writable copy now.
    newEntry.writable = true;     // Grant write access.
    newEntry.protection |= PROT_WRITE;

    // 4. Invalidate the TLB entry for this address to force a new translation
    // lookup.
    m_tlb->Invalidate(virtAddr, processId);

    // 5. Check if the old shared page is still referenced by other processes.
    // A real OS uses a reference count on the physical frame for efficiency.
    bool stillReferenced = false;
    for (const auto &ptPair : m_pageTables) {
      if (ptPair.first == processId)
        continue; // Skip self
      const auto &otherTable = ptPair.second;
      // This is a simplified check. A full check would span the relevant index
      // range.
      if (pageIndex < otherTable.size() && otherTable[pageIndex].present &&
          otherTable[pageIndex].physAddr == oldPhysAddr) {
        stillReferenced = true;
        break;
      }
    }

    // If no other process references the old page, it can be freed.
    if (!stillReferenced) {
      m_physAlloc->Free(oldPhysAddr, PAGE_SIZE);
      m_stats.freePages++;
      m_stats.usedPages--;
      spdlog::debug(
          "Freed old shared physical page 0x{:x} as it's no longer referenced.",
          oldPhysAddr);
    }

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("COW handled successfully: virt=0x{:x}, new_phys=0x{:x}",
                 virtAddr, newPhysAddr);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("HandleCopyOnWrite failed for virt=0x{:x}: {}", virtAddr,
                  e.what());
    return false;
  }
}

uint64_t PS4MMU::CreateX86PageTable(uint64_t processId) {
  uint64_t physPage = m_physAlloc->Allocate(PAGE_SIZE, 0);
  X86_64PageTable table;
  table.cr3 = physPage;
  m_x86PageTables[processId] = std::move(table);
#ifdef DEBUG_MMU
  spdlog::info("CreateX86PageTable: pid={}, cr3=0x{:x}", processId, physPage);
#endif
  return physPage;
}

void PS4MMU::MapPageX86(uint64_t virtAddr, uint64_t physAddr,
                        uint64_t processId, int protection, bool user,
                        bool executeDisable) {
#ifdef DEBUG_MMU
  spdlog::info("MapPageX86: virt=0x{:x}, phys=0x{:x}, pid={}, prot={}",
               virtAddr, physAddr, processId, protection);
#endif // Placeholder: map via software tables
  MapMemory(virtAddr, physAddr, PAGE_SIZE, protection, processId);
}

// TODO #11: CRITICAL FIX: Multi-Level Page Table Support - Enhanced x86-64 page
// table implementation
/**
 * @brief Initializes x86-64 multi-level page tables for enhanced memory
 * management
 * @param processId Process ID
 * @return True on success, false otherwise
 */
bool PS4MMU::InitializeX86_64PageTables(uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");

  try {
    spdlog::info("Initializing x86-64 multi-level page tables for process {}",
                 processId);

    // Initialize the x86-64 page table structure
    X86_64PageTable pageTable;

    // Allocate physical memory for PML4 table (512 entries * 8 bytes = 4KB)
    uint64_t pml4PhysAddr =
        m_physAlloc->Allocate(PAGE_SIZE, 5); // High priority
    if (pml4PhysAddr == ALLOC_FAILED) {
      spdlog::error("Failed to allocate physical memory for PML4 table");
      return false;
    }

    pageTable.cr3 = pml4PhysAddr;
    pageTable.pml4.resize(512); // 512 PML4 entries

    // Initialize PML4 entries as empty
    for (auto &entry : pageTable.pml4) {
      entry.raw = 0;
    }

    // Initialize PDPT structure (page directory pointer table)
    pageTable.pdpt.resize(512);
    for (auto &pdptLevel : pageTable.pdpt) {
      pdptLevel.resize(512);
      for (auto &entry : pdptLevel) {
        entry.raw = 0;
      }
    }

    // Initialize PD structure (page directory)
    pageTable.pd.resize(512);
    for (auto &pdLevel : pageTable.pd) {
      pdLevel.resize(512);
      for (auto &entry : pdLevel) {
        entry.raw = 0;
      }
    }

    // Initialize PT structure (page table)
    pageTable.pt.resize(512);
    for (auto &ptLevel : pageTable.pt) {
      ptLevel.resize(512);
      for (auto &entry : ptLevel) {
        entry.raw = 0;
      }
    }

    // Store the page table structure
    m_x86PageTables[processId] = std::move(pageTable);

    auto end = std::chrono::steady_clock::now();
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Successfully initialized x86-64 page tables for process {} "
                 "with CR3=0x{:x}",
                 processId, pml4PhysAddr);
    return true;

  } catch (const std::exception &e) {
    spdlog::error("Failed to initialize x86-64 page tables for process {}: {}",
                  processId, e.what());
    return false;
  }
}

/**
 * @brief Maps virtual memory using x86-64 multi-level page tables
 * @param virtAddr Virtual address to map
 * @param physAddr Physical address to map to
 * @param size Size to map
 * @param protection Memory protection flags
 * @param processId Process ID
 * @return True on success, false otherwise
 */
bool PS4MMU::MapX86_64Memory(uint64_t virtAddr, uint64_t physAddr, size_t size,
                             int protection, uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");

  try {
    spdlog::info("Mapping x86-64 memory: virt=0x{:x}, phys=0x{:x}, size={:#x}, "
                 "prot=0x{:x}, process={}",
                 virtAddr, physAddr, size, protection, processId);

    // Ensure page table exists for this process
    if (m_x86PageTables.find(processId) == m_x86PageTables.end()) {
      if (!InitializeX86_64PageTables(processId)) {
        return false;
      }
    }

    auto &pageTable = m_x86PageTables[processId];

    // Map each page in the range
    for (uint64_t addr = virtAddr; addr < virtAddr + size; addr += PAGE_SIZE) {
      uint64_t currentPhysAddr = physAddr + (addr - virtAddr);

      // Extract page table indices from virtual address
      uint64_t pml4Index = (addr >> 39) & 0x1FF; // Bits 47-39
      uint64_t pdptIndex = (addr >> 30) & 0x1FF; // Bits 38-30
      uint64_t pdIndex = (addr >> 21) & 0x1FF;   // Bits 29-21
      uint64_t ptIndex = (addr >> 12) & 0x1FF;   // Bits 20-12

      // Validate indices
      if (pml4Index >= 512 || pdptIndex >= 512 || pdIndex >= 512 ||
          ptIndex >= 512) {
        spdlog::error("Invalid page table indices for address 0x{:x}", addr);
        return false;
      }

      // Ensure PML4 entry exists
      if (!pageTable.pml4[pml4Index].bits.present) {
        uint64_t pdptPhysAddr = m_physAlloc->Allocate(PAGE_SIZE, 4);
        if (pdptPhysAddr == ALLOC_FAILED) {
          spdlog::error("Failed to allocate PDPT for PML4 index {}", pml4Index);
          return false;
        }
        pageTable.pml4[pml4Index].SetPhysicalAddress(pdptPhysAddr);
        pageTable.pml4[pml4Index].bits.present = 1;
        pageTable.pml4[pml4Index].bits.writable = 1;
        pageTable.pml4[pml4Index].bits.user = 1;
      }

      // Ensure PDPT entry exists
      if (!pageTable.pdpt[pml4Index][pdptIndex].bits.present) {
        uint64_t pdPhysAddr = m_physAlloc->Allocate(PAGE_SIZE, 4);
        if (pdPhysAddr == ALLOC_FAILED) {
          spdlog::error("Failed to allocate PD for PDPT index {}", pdptIndex);
          return false;
        }
        pageTable.pdpt[pml4Index][pdptIndex].SetPhysicalAddress(pdPhysAddr);
        pageTable.pdpt[pml4Index][pdptIndex].bits.present = 1;
        pageTable.pdpt[pml4Index][pdptIndex].bits.writable = 1;
        pageTable.pdpt[pml4Index][pdptIndex].bits.user = 1;
      }

      // Ensure PD entry exists
      if (!pageTable.pd[pml4Index][pdIndex].bits.present) {
        uint64_t ptPhysAddr = m_physAlloc->Allocate(PAGE_SIZE, 4);
        if (ptPhysAddr == ALLOC_FAILED) {
          spdlog::error("Failed to allocate PT for PD index {}", pdIndex);
          return false;
        }
        pageTable.pd[pml4Index][pdIndex].SetPhysicalAddress(ptPhysAddr);
        pageTable.pd[pml4Index][pdIndex].bits.present = 1;
        pageTable.pd[pml4Index][pdIndex].bits.writable = 1;
        pageTable.pd[pml4Index][pdIndex].bits.user = 1;
      }

      // Set PT entry
      pageTable.pt[pml4Index][ptIndex].SetPhysicalAddress(currentPhysAddr);
      pageTable.pt[pml4Index][ptIndex].bits.present = 1;
      pageTable.pt[pml4Index][ptIndex].bits.writable =
          (protection & PROT_WRITE) ? 1 : 0;
      pageTable.pt[pml4Index][ptIndex].bits.user = 1;
      pageTable.pt[pml4Index][ptIndex].bits.executeDisable =
          (protection & PROT_EXEC) ? 0 : 1;
      pageTable.pt[pml4Index][ptIndex].bits.accessed = 0;
      pageTable.pt[pml4Index][ptIndex].bits.dirty = 0;

      // Update TLB
      m_tlb->Insert(addr, currentPhysAddr, PAGE_SIZE, processId);
    }

    auto end = std::chrono::steady_clock::now();
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

    spdlog::info("Successfully mapped x86-64 memory: virt=0x{:x}, phys=0x{:x}, "
                 "size={:#x}",
                 virtAddr, physAddr, size);
    return true;

  } catch (const std::exception &e) {
    spdlog::error("x86-64 memory mapping failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Translates virtual address using x86-64 page tables
 * @param virtAddr Virtual address to translate
 * @param processId Process ID
 * @param write Whether this is for a write operation
 * @return Physical address or ALLOC_FAILED
 */
uint64_t PS4MMU::TranslateX86_64Address(uint64_t virtAddr, uint64_t processId,
                                        bool write) const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");

  auto it = m_x86PageTables.find(processId);
  if (it == m_x86PageTables.end()) {
    return ALLOC_FAILED;
  }

  const auto &pageTable = it->second;

  // Extract page table indices
  uint64_t pml4Index = (virtAddr >> 39) & 0x1FF;
  uint64_t pdptIndex = (virtAddr >> 30) & 0x1FF;
  uint64_t pdIndex = (virtAddr >> 21) & 0x1FF;
  uint64_t ptIndex = (virtAddr >> 12) & 0x1FF;
  uint64_t offset = virtAddr & 0xFFF;

  // Walk page table hierarchy
  if (pml4Index >= pageTable.pml4.size() ||
      !pageTable.pml4[pml4Index].bits.present) {
    return ALLOC_FAILED;
  }

  if (pdptIndex >= pageTable.pdpt[pml4Index].size() ||
      pml4Index >= pageTable.pdpt[pml4Index].size()) {
    spdlog::debug("PDPT index out of bounds: pml4={}, pdpt={}", pml4Index,
                  pdptIndex);
    return ALLOC_FAILED;
  }

  const X86_64PageTableEntry &pdpte = pageTable.pdpt[pml4Index][pdptIndex];
  if (!pdpte.bits.present) {
    spdlog::debug("PDPT entry not present at index {}", pdptIndex);
    return ALLOC_FAILED;
  }

  if (pdpte.bits.pageSize) {
    uint64_t physAddr = pdpte.GetPhysicalAddress() + (virtAddr & 0x3FFFFFFF);
    spdlog::trace("1GB page translation: virt=0x{:x} -> phys=0x{:x}", virtAddr,
                  physAddr);
    return physAddr;
  }

  if (pdIndex >= pageTable.pd.size() || pml4Index >= pageTable.pd.size()) {
    spdlog::debug("PD index out of bounds");
    return ALLOC_FAILED;
  }

  const X86_64PageTableEntry &pde = pageTable.pd[pml4Index][pdIndex];
  if (!pde.bits.present) {
    spdlog::debug("PD entry not present at index {}", pdIndex);
    return ALLOC_FAILED;
  }

  if (pde.bits.pageSize) {
    uint64_t physAddr = pde.GetPhysicalAddress() + (virtAddr & 0x1FFFFF);
    spdlog::trace("2MB page translation: virt=0x{:x} -> phys=0x{:x}", virtAddr,
                  physAddr);
    return physAddr;
  }

  if (ptIndex >= pageTable.pt.size() || pml4Index >= pageTable.pt.size()) {
    spdlog::debug("PT index out of bounds");
    return ALLOC_FAILED;
  }

  const X86_64PageTableEntry &pte = pageTable.pt[pml4Index][ptIndex];
  if (!pte.bits.present) {
    spdlog::debug("PT entry not present at index {}", ptIndex);
    return ALLOC_FAILED;
  }

  if (pte.bits.executeDisable && !write) {
  }

  uint64_t physAddr = pte.GetPhysicalAddress() + offset;
  spdlog::trace("4KB page translation: virt=0x{:x} -> phys=0x{:x}", virtAddr,
                physAddr);
  return physAddr;
}

// TODO #12: CRITICAL FIX: Memory Protection - Enhanced protection and type
// checking
/**
 * @brief Enhanced memory protection with comprehensive validation
 * @param virtAddr Virtual address
 * @param size Size to protect
 * @param protection Protection flags
 * @param processId Process ID
 * @param memoryType Memory type for enhanced validation
 * @return True on success, false otherwise
 */
bool PS4MMU::EnhancedProtectMemory(uint64_t virtAddr, size_t size,
                                   int protection, uint64_t processId,
                                   MemoryType memoryType) {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");

  try {
    spdlog::info("Enhanced memory protection: virt=0x{:x}, size={:#x}, "
                 "prot=0x{:x}, type={}, process={}",
                 virtAddr, size, protection, static_cast<int>(memoryType),
                 processId);

    // Validate input parameters
    if (size == 0) {
      spdlog::error("Invalid protection size: 0");
      return false;
    }

    if (virtAddr % PAGE_SIZE != 0) {
      spdlog::error("Virtual address not page-aligned: 0x{:x}", virtAddr);
      return false;
    }

    if (size % PAGE_SIZE != 0) {
      spdlog::warn("Size not page-aligned, rounding up: {:#x}", size);
      size = (size + PAGE_SIZE - 1) & ~(PAGE_SIZE - 1);
    }

    // Validate protection flags
    if (!ValidateProtectionFlags(protection)) {
      spdlog::error("Invalid protection flags: 0x{:x}", protection);
      return false;
    }

    // Check for integer overflow
    if (virtAddr > UINT64_MAX - size) {
      spdlog::error("Virtual address range overflow: start=0x{:x}, size={:#x}",
                    virtAddr, size);
      return false;
    }

    // Validate memory type compatibility
    if (!ValidateMemoryTypeProtection(memoryType, protection)) {
      spdlog::error("Incompatible memory type {} with protection 0x{:x}",
                    static_cast<int>(memoryType), protection);
      return false;
    }

    // Use x86-64 page tables if available, otherwise fall back to simple page
    // tables
    if (m_x86PageTables.find(processId) != m_x86PageTables.end()) {
      return EnhancedProtectX86_64Memory(virtAddr, size, protection, processId,
                                         memoryType);
    } else {
      return EnhancedProtectSimpleMemory(virtAddr, size, protection, processId,
                                         memoryType);
    }

  } catch (const std::exception &e) {
    spdlog::error("Enhanced memory protection failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Validates protection flags for consistency
 * @param protection Protection flags to validate
 * @return True if valid, false otherwise
 */
bool PS4MMU::ValidateProtectionFlags(int protection) const {
  // Check for invalid flag combinations
  if (protection < 0 || protection > (PROT_READ | PROT_WRITE | PROT_EXEC)) {
    return false;
  }

  // Execute-only memory is generally not allowed on x86-64
  if ((protection & PROT_EXEC) && !(protection & PROT_READ)) {
    spdlog::warn("Execute-only memory protection is unusual on x86-64");
  }

  return true;
}

/**
 * @brief Validates memory type compatibility with protection flags
 * @param memoryType Memory type
 * @param protection Protection flags
 * @return True if compatible, false otherwise
 */
bool PS4MMU::ValidateMemoryTypeProtection(MemoryType memoryType,
                                          int protection) const {
  switch (memoryType) {
  case MemoryType::VIDEO:
    // GPU memory typically requires read/write access
    if (!(protection & (PROT_READ | PROT_WRITE))) {
      return false;
    }
    break;
  case MemoryType::IO:
    // Audio buffers need read/write access
    if (!(protection & PROT_READ)) {
      return false;
    }
    break;
  case MemoryType::Executable:
    // Executable memory needs read/execute access
    if (!(protection & (PROT_READ | PROT_EXEC))) {
      return false;
    }
    break;

  case MemoryType::Stack:
    // Stack memory needs read/write, usually not executable
    if (!(protection & (PROT_READ | PROT_WRITE))) {
      return false;
    }
    if (protection & PROT_EXEC) {
      spdlog::warn("Executable stack detected - potential security risk");
    }
    break;

  case MemoryType::Default:
  default:
    // Default memory allows any valid protection
    break;
  }

  return true;
}

/**
 * @brief Enhanced protection for x86-64 page tables
 * @param virtAddr Virtual address
 * @param size Size to protect
 * @param protection Protection flags
 * @param processId Process ID
 * @param memoryType Memory type
 * @return True on success, false otherwise
 */
bool PS4MMU::EnhancedProtectX86_64Memory(uint64_t virtAddr, size_t size,
                                         int protection, uint64_t processId,
                                         MemoryType memoryType) {
  auto &pageTable = m_x86PageTables[processId];
  uint64_t pageCount = 0;

  for (uint64_t addr = virtAddr; addr < virtAddr + size; addr += PAGE_SIZE) {
    // Extract page table indices
    uint64_t pml4Index = (addr >> 39) & 0x1FF;
    uint64_t pdptIndex = (addr >> 30) & 0x1FF;
    uint64_t pdIndex = (addr >> 21) & 0x1FF;
    uint64_t ptIndex = (addr >> 12) & 0x1FF;

    // Validate that the page exists
    if (!pageTable.pml4[pml4Index].bits.present ||
        !pageTable.pdpt[pml4Index][pdptIndex].bits.present ||
        !pageTable.pd[pml4Index][pdIndex].bits.present ||
        !pageTable.pt[pml4Index][ptIndex].bits.present) {
      spdlog::error("Cannot protect non-existent page at 0x{:x}", addr);
      return false;
    }

    // Update protection in page table entry
    auto &ptEntry = pageTable.pt[pml4Index][ptIndex];
    ptEntry.bits.writable = (protection & PROT_WRITE) ? 1 : 0;
    ptEntry.bits.executeDisable = (protection & PROT_EXEC) ? 0 : 1;

    // Invalidate TLB entry
    m_tlb->Invalidate(addr, processId);
    pageCount++;
  }

  spdlog::info(
      "Enhanced x86-64 protection applied to {} pages starting at 0x{:x}",
      pageCount, virtAddr);
  return true;
}

/**
 * @brief Enhanced protection for simple page tables
 * @param virtAddr Virtual address
 * @param size Size to protect
 * @param protection Protection flags
 * @param processId Process ID
 * @param memoryType Memory type
 * @return True on success, false otherwise
 */
bool PS4MMU::EnhancedProtectSimpleMemory(uint64_t virtAddr, size_t size,
                                         int protection, uint64_t processId,
                                         MemoryType memoryType) {
  auto &pageTable = m_pageTables[processId];
  uint64_t pageCount = 0;

  for (uint64_t addr = virtAddr; addr < virtAddr + size; addr += PAGE_SIZE) {
    uint64_t pageIndex = addr / PAGE_SIZE;

    if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present) {
      spdlog::error("Cannot protect non-existent page at 0x{:x}", addr);
      return false;
    }

    // Update protection and memory type
    pageTable[pageIndex].protection = protection;
    pageTable[pageIndex].type = memoryType;
    pageTable[pageIndex].writable = (protection & PROT_WRITE) ? true : false;
    pageTable[pageIndex].executeDisable =
        (protection & PROT_EXEC) ? false : true;

    // Invalidate TLB entry
    m_tlb->Invalidate(addr, processId);
    pageCount++;
  }

  spdlog::info(
      "Enhanced simple protection applied to {} pages starting at 0x{:x}",
      pageCount, virtAddr);
  return true;
}

// ExportMemoryStats implementation
bool PS4MMU::ExportMemoryStats(const std::string &filePath) const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  try {
    nlohmann::json stats_json;
    stats_json["memoryStats"] = {
        {"totalPages", m_stats.totalPages.load()},
        {"usedPages", m_stats.usedPages.load()},
        {"freePages", m_stats.freePages.load()},
        {"compressedPages", m_stats.compressedPages.load()},
        {"swappedPages", m_stats.swappedPages.load()},
        {"compressionRatio", m_stats.compressionRatio.load()},
        {"swapUsageRatio", m_stats.swapUsageRatio.load()},
        {"pageFaults", m_stats.pageFaults.load()},
        {"totalLatencyUs", m_stats.totalLatencyUs.load()},
        {"tlbHits", m_stats.hits.load()},
        {"tlbMisses", m_stats.misses.load()}};

    if (m_physAlloc) {
      auto physAllocStats = m_physAlloc->GetStats();
      stats_json["allocatorStats"] = {
          {"totalAllocations", physAllocStats.totalAllocations},
          {"totalFrees", physAllocStats.totalFrees},
          {"currentAllocations", physAllocStats.currentAllocations},
          {"peakAllocations", physAllocStats.peakAllocations},
          {"fragmentationCount", physAllocStats.fragmentationCount}};
    } else {
      stats_json["allocatorStats"] = "PhysicalMemoryAllocator not available";
    }

    if (m_prefetcher) {
      auto prefetcherStats = m_prefetcher->GetStats();
      stats_json["prefetcherStats"] = {
          {"prefetchHits", prefetcherStats.prefetchHits},
          {"totalAccesses", prefetcherStats.totalAccesses},
          {"prefetchRequests", prefetcherStats.prefetchRequests},
          {"sequentialPatterns", prefetcherStats.sequentialPatterns},
          {"stridePatterns", prefetcherStats.stridePatterns},
          {"hitRate", prefetcherStats.hitRate},
      };
    } else {
      stats_json["prefetcherStats"] = "MemoryPrefetcher not available";
    }

    if (m_compressor) {
      auto compressorStats = m_compressor->GetStats();
      stats_json["compressionStats"] = {
          {"pagesCompressed", compressorStats.pagesCompressed},
          {"pagesDecompressed", compressorStats.pagesDecompressed},
          {"totalBytesOriginal", compressorStats.totalBytesOriginal},
          {"totalBytesCompressed", compressorStats.totalBytesCompressed},
          {"totalBytesSaved", compressorStats.totalBytesSaved},
          {"compressionRatio", compressorStats.compressionRatio},
          {"compressionEfficiency", compressorStats.compressionEfficiency},
          {"totalLatencyUs", compressorStats.totalLatencyUs},
          {"cacheHits", compressorStats.cacheHits},
          {"cacheMisses", compressorStats.cacheMisses}};
    } else {
      stats_json["compressionStats"] = "MemoryCompressor not available";
    }

    std::ofstream out_stream(filePath);
    if (!out_stream.is_open()) {
      spdlog::error("Failed to open file for stats export: {}", filePath);
      return false;
    }
    out_stream << stats_json.dump(2);
    spdlog::info("Exported memory stats to {}", filePath);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ExportMemoryStats failed: {}", e.what());
    return false;
  }
}
bool PS4MMU::ReadPhysical(uint64_t physAddr, void *data, size_t size) const {
  auto start = std::chrono::steady_clock::now();
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  spdlog::info("Reading physical memory: phys=0x{:x}, size={:#x}", physAddr,
               size);
  try {
    if (physAddr + size > m_size) {
      spdlog::error("Invalid physical read: phys=0x{:x}, size={:#x}", physAddr,
                    size);
      return false;
    }
    std::memcpy(data, m_physicalMemory.data() + physAddr, size);
    m_readBytes.fetch_add(size, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    // LOGIC FIX: Use helper to avoid consteval issues
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Read physical memory: phys=0x{:x}, size={:#x}", physAddr,
                 size);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Physical read failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Writes to physical memory.
 * @param physAddr Physical address.
 * @param data Input buffer.
 * @param size Size to write.
 * @return True on success, false otherwise.
 */
bool PS4MMU::WritePhysical(uint64_t physAddr, const void *data, size_t size) {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");
  spdlog::info("Writing physical memory: phys=0x{:x}, size={:#x}", physAddr,
               size);
  try {
    if (physAddr + size > m_size) {
      spdlog::error("Invalid physical write: phys=0x{:x}, size={:#x}", physAddr,
                    size);
      return false;
    }
    std::memcpy(m_physicalMemory.data() + physAddr, data, size);
    m_writeBytes.fetch_add(size, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    // LOGIC FIX: Use helper to avoid consteval issues
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Wrote physical memory: phys=0x{:x}, size={:#x}", physAddr,
                 size);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Physical write failed: {}", e.what());
    return false;
  }
}

// HandleCopyOnWrite implementation moved to end of file

/* Duplicate PS4MMU::Initialize removed.*/

/* Duplicate shutdown function removed */

/**
 * @brief Frees virtual memory.
 * @param virtAddr Virtual address to free.
 * @param processId Process ID.
 */
void PS4MMU::FreeVirtual(uint64_t virtAddr, uint64_t processId) {
  auto start_time = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");
  spdlog::info("Freeing virtual memory: addr=0x{:x}, process={}", virtAddr,
               processId);
  try {
    auto it_pt = m_pageTables.find(processId);
    if (it_pt == m_pageTables.end()) {
      spdlog::warn("No page table for process {}. Cannot free 0x{:x}",
                   processId, virtAddr);
      return;
    }
    auto &pageTable = it_pt->second;

    uint64_t pageIndex = virtAddr / PAGE_SIZE;
    if (virtAddr % PAGE_SIZE != 0) {
      spdlog::warn("Attempt to free non-page-aligned virtual address: 0x{:x}",
                   virtAddr);
    }

    if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present) {
      spdlog::warn("Invalid virtual address to free (not present or out of "
                   "bounds): 0x{:x}, process={}",
                   virtAddr, processId);
      return;
    }

    if (!pageTable[pageIndex].swapped &&
        pageTable[pageIndex].physAddr != ALLOC_FAILED) {
      m_physAlloc->Free(pageTable[pageIndex].physAddr, PAGE_SIZE);
      m_stats.freePages++;
      m_stats.usedPages--;
    } else if (pageTable[pageIndex].swapped) {
      // Note: SwapManager doesn't have FreeSwappedPage method
      // The swapped page will be cleaned up when the swap entry is
      // overwritten
      spdlog::debug("Page was swapped, swap entry will be cleaned up "
                    "automatically: 0x{:x}",
                    virtAddr);
    }

    if (pageTable[pageIndex].compressedId != 0) {
      m_compressor->FreeCompressedPage(pageTable[pageIndex].compressedId);
    }
    pageTable[pageIndex] = {};
    m_tlb->Invalidate(virtAddr, processId);

    auto end_time = std::chrono::steady_clock::now();
    // LOGIC FIX: Use helper to avoid consteval issues
    auto latency = CalculateLatencyMicroseconds(start_time, end_time);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Freed virtual memory: addr=0x{:x}, process={}", virtAddr,
                 processId);
  } catch (const std::exception &e) {
    spdlog::error("Virtual free failed: {}", e.what());
  }
}

/**
 * @brief Maps virtual to physical memory.
 * @param virtAddr Virtual address.
 * @param physAddr Physical address.
 * @param size Size to map.
 * @param protection Protection flags. (uint32_t version)
 * @param processId Process ID.
 * @return True on success, false otherwise.
 */
bool PS4MMU::MapMemory(uint64_t virtAddr, uint64_t physAddr, size_t size,
                       uint32_t protection, uint64_t processId) {
  return MapMemory(virtAddr, physAddr, size, static_cast<int>(protection),
                   processId);
}

/**
 * @brief Unmaps virtual memory.
 * @param virtAddr Virtual address.
 * @param size Size to unmap.
 * @param processId Process ID.
 * @return True on success, false otherwise.
 */
bool PS4MMU::UnmapMemory(uint64_t virtAddr, size_t size, uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");
  spdlog::info("Unmapping memory: virt=0x{:x}, size={:#x}", virtAddr, size);
  try {
    auto it_pt = m_pageTables.find(processId);
    if (it_pt == m_pageTables.end()) {
      spdlog::warn("No page table for process {}. Cannot unmap region starting "
                   "at 0x{:x}",
                   processId, virtAddr);
      return false;
    }
    auto &pageTable = it_pt->second;

    for (uint64_t addr = virtAddr; addr < virtAddr + size; addr += PAGE_SIZE) {
      uint64_t pageIndex = addr / PAGE_SIZE;
      if (pageIndex < pageTable.size() && pageTable[pageIndex].present) {
        if (!pageTable[pageIndex].swapped &&
            pageTable[pageIndex].physAddr != ALLOC_FAILED) {
          m_physAlloc->Free(pageTable[pageIndex].physAddr, PAGE_SIZE);
          m_stats.freePages++;
          m_stats.usedPages--;
        } else if (pageTable[pageIndex].swapped) {
          // Note: SwapManager doesn't have FreeSwappedPage method
          // The swapped page will be cleaned up when the swap entry is
          // overwritten
          spdlog::debug("Page was swapped, swap entry will be cleaned up "
                        "automatically: 0x{:x}",
                        addr);
        }

        if (pageTable[pageIndex].compressedId != 0) {
          m_compressor->FreeCompressedPage(pageTable[pageIndex].compressedId);
        }
        pageTable[pageIndex] = {};
        m_tlb->Invalidate(addr, processId);
      }
    }
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Unmapped memory: virt=0x{:x}, size={:#x}", virtAddr, size);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Memory unmapping failed: {}", e.what());
    return false;
  }
}
// Removed duplicate ReadPhysical function

/**
 * @brief Initializes the MMU.
 * @return True on success, false otherwise.
 */
bool PS4MMU::Initialize() {
  auto start_time = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");
  spdlog::info("Initializing PS4MMU...");
  try {
    m_size = TOTAL_SIZE;
    spdlog::info(
        "PS4MMU: Allocating physical memory buffer of size {:#x} bytes",
        m_size);
    try {
      m_physicalMemory.resize(m_size);
    } catch (const std::bad_alloc &e) {
      spdlog::critical(
          "PS4MMU: Failed to allocate physical memory buffer ({} bytes): {}",
          m_size, e.what());
      m_size = 512ULL * 1024 * 1024;
      spdlog::warn("PS4MMU: Falling back to {}MB for physical memory buffer",
                   m_size / (1024 * 1024));
      try {
        m_physicalMemory.resize(m_size);
      } catch (const std::bad_alloc &e_fallback) {
        spdlog::critical("PS4MMU: Failed to allocate fallback physical memory "
                         "buffer ({} bytes): {}",
                         m_size, e_fallback.what());
        throw;
      }
    }
    spdlog::info("PS4MMU: Physical memory buffer allocated successfully");

    m_physAlloc = std::make_unique<PhysicalMemoryAllocator>();
    spdlog::info("PS4MMU: Initializing PhysicalMemoryAllocator with size {:#x}",
                 m_size);
    m_physAlloc->Initialize(m_size);
    spdlog::info("PS4MMU: PhysicalMemoryAllocator initialized successfully");

    // Test the allocator immediately after initialization
    spdlog::info(
        "PS4MMU: Testing PhysicalMemoryAllocator with a small allocation...");
    uint64_t testAddr = m_physAlloc->Allocate(PAGE_SIZE);
    if (testAddr == ALLOC_FAILED) {
      spdlog::error("PS4MMU: PhysicalMemoryAllocator test failed: returned "
                    "failure code");
      throw std::runtime_error("PhysicalMemoryAllocator test failed");
    }
    spdlog::info("PS4MMU: Test allocation successful, got address 0x{:x}",
                 testAddr);
    m_physAlloc->Free(testAddr, PAGE_SIZE);
    spdlog::info("PS4MMU: Test free successful");

    m_prefetcher = std::make_unique<MemoryPrefetcher>();
    if (!m_prefetcher->Initialize()) {
      throw std::runtime_error("Memory prefetcher initialization failed");
    }

    m_compressor = std::make_unique<MemoryCompressor>();
    if (!m_compressor->Initialize()) {
      throw std::runtime_error("Memory compressor initialization failed");
    }

    m_swapManager =
        std::make_unique<SwapManager>("ps4_swap.bin", TOTAL_SIZE / 2);
    if (!m_swapManager->Initialize()) {
      throw std::runtime_error("Swap manager initialization failed");
    }

    m_tlb = std::make_unique<ps4::TLB>();

    // Initialize memory statistics properly
    m_stats = {};
    auto totalPages = m_size / PAGE_SIZE;
    m_stats.totalPages.store(totalPages);
    m_stats.freePages.store(totalPages);
    m_stats.usedPages.store(0);
    spdlog::info(
        "PS4MMU: Initialized memory stats - total pages: {}, free pages: {}",
        m_stats.totalPages.load(), m_stats.freePages.load());

    auto end_time = std::chrono::steady_clock::now();
    // LOGIC FIX: Use helper to avoid consteval issues
    auto latency = CalculateLatencyMicroseconds(start_time, end_time);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("PS4MMU initialized with size: {:#x} bytes", m_size);
    return true;
  } catch (const std::exception &e) {
    spdlog::critical("PS4MMU initialization failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Shuts down the MMU, releasing resources.
 */
void PS4MMU::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");
  spdlog::info("Shutting down PS4MMU...");
  try {
    m_physicalMemory.clear();
    m_physicalMemory.shrink_to_fit();
    m_pageTables.clear();
    m_memoryRegions.clear();
    if (m_tlb)
      m_tlb->Clear();
    m_physAlloc.reset();
    m_prefetcher.reset();
    m_compressor.reset();
    m_swapManager.reset();
    m_size = 0;
    m_stats =
        {}; // CRITICAL FIX: Ensure all statistics are properly initialized
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("PS4MMU shutdown completed");
  } catch (const std::exception &e) {
    spdlog::error("PS4MMU shutdown failed: {}", e.what());
  }
}

/**
 * @brief Allocates virtual memory.
 * @param processId Process ID.
 * @param size Size to allocate.
 * @param alignment Alignment requirement.
 * @param protection Protection flags.
 * @param shared True if shared memory.
 * @param type Memory type.
 * @return Virtual address allocated.
 */
uint64_t PS4MMU::AllocateVirtual(uint64_t processId, uint64_t size,
                                 uint64_t alignment, int protection,
                                 bool shared, MemoryType type) {
  auto start_time = std::chrono::steady_clock::now();
  const auto timeout =
      std::chrono::seconds(10); // Reduced timeout to prevent long hangs
  MEMORY_LOCK(m_mutex, "MMUMutex");
  spdlog::info("Allocating virtual memory: process={}, size={:#x}", processId,
               size);
  try {
    if (size == 0) {
      spdlog::error("Invalid allocation size: 0 for AllocateVirtual");
      throw std::invalid_argument("Invalid allocation size: 0");
    }
    size = (size + PAGE_SIZE - 1) & ~(PAGE_SIZE - 1);

    if (alignment == 0 || alignment < PAGE_SIZE) {
      alignment = PAGE_SIZE;
    }
    if ((alignment & (alignment - 1)) != 0 && alignment != 0) {
      spdlog::warn("Alignment 0x{:x} is not a power of 2. Using PAGE_SIZE "
                   "(0x{:x}) instead.",
                   alignment, PAGE_SIZE);
      alignment = PAGE_SIZE;
    }

    // Check timeout before proceeding
    if (std::chrono::steady_clock::now() - start_time > timeout) {
      spdlog::error(
          "AllocateVirtual: Timeout during initial setup for size={:#x}", size);
      throw std::runtime_error("Virtual allocation timeout during setup");
    }

    // PERFORMANCE FIX: Improved virtual address allocation algorithm
    auto pt_iter = m_pageTables.find(processId);
    if (pt_iter == m_pageTables.end()) {
      m_pageTables[processId] = {};
    }
    auto &pageTable = m_pageTables[processId];

    spdlog::info(
        "Searching for free virtual range: size={:#x}, alignment={:#x}", size,
        alignment);

    // Check physical memory availability for large allocations
    if (size > 1024 * 1024) { // Check for allocations > 1MB
      auto stats = m_physAlloc->GetStats();
      spdlog::info(
          "Large allocation requested ({}MB), checking memory availability. "
          "Current stats: allocations={}, fragmentation={}",
          size / (1024 * 1024), stats.allocationCount,
          stats.fragmentationCount);

      // If fragmentation is very high, warn about potential performance issues
      if (stats.fragmentationCount > 10000) {
        spdlog::warn(
            "High memory fragmentation detected ({}), allocation may be slow",
            stats.fragmentationCount);
      }
    }

    // Try to find a free range using a more efficient algorithm
    uint64_t virtAddr = FindFreeVirtualRange(processId, size, alignment);

    if (virtAddr == ALLOC_FAILED) {
      spdlog::error(
          "No free virtual range found of size {:#x} with alignment {:#x}",
          size, alignment);
      throw std::runtime_error("No free virtual range found");
    }

    // Check timeout before page allocation loop
    if (std::chrono::steady_clock::now() - start_time > timeout) {
      spdlog::error(
          "AllocateVirtual: Timeout before page allocation for size={:#x}",
          size);
      throw std::runtime_error(
          "Virtual allocation timeout before page allocation");
    }

    uint64_t numPages = size / PAGE_SIZE;
    spdlog::info("AllocateVirtual: Allocating {} pages starting at 0x{:x}",
                 numPages, virtAddr);

    // Circuit breaker: track consecutive failures to avoid infinite retry loops
    int consecutiveFailures = 0;
    const int maxConsecutiveFailures = 10;

    spdlog::info("AllocateVirtual: Starting page allocation loop for {} pages",
                 numPages);

    for (uint64_t offset = 0; offset < size; offset += PAGE_SIZE) {
      uint64_t currentPage = offset / PAGE_SIZE;

      // Check timeout more frequently for large allocations
      if (currentPage % 10 == 0) { // Check every 10 pages for faster debugging
        if (std::chrono::steady_clock::now() - start_time > timeout) {
          spdlog::error(
              "AllocateVirtual: Timeout during page allocation at page {}/{}",
              currentPage, numPages);
          throw std::runtime_error(
              "Virtual allocation timeout during page allocation");
        }

        // Log progress for all allocations to debug the hang
        spdlog::info(
            "AllocateVirtual: Progress {}/{} pages allocated ({:.1f}%)",
            currentPage, numPages, (currentPage * 100.0) / numPages);
      }

      uint64_t current_alloc_addr = virtAddr + offset;
      spdlog::debug(
          "AllocateVirtual: About to allocate page {} at address 0x{:x}",
          currentPage, current_alloc_addr);

      try {
        AllocatePage(current_alloc_addr, processId, protection, shared, type);
        consecutiveFailures = 0; // Reset failure counter on success
        spdlog::debug(
            "AllocateVirtual: Successfully allocated page {} at address 0x{:x}",
            currentPage, current_alloc_addr);
      } catch (const std::exception &e) {
        consecutiveFailures++;
        spdlog::error(
            "AllocateVirtual: Page allocation failed at page {}/{}: {}",
            currentPage, numPages, e.what());

        // Circuit breaker: fail fast if too many consecutive failures
        if (consecutiveFailures >= maxConsecutiveFailures) {
          spdlog::error("AllocateVirtual: Too many consecutive failures ({}), "
                        "aborting allocation",
                        consecutiveFailures);
          throw std::runtime_error(
              "Virtual allocation failed: too many consecutive page failures");
        }

        // Re-throw the exception to maintain existing error handling
        throw;
      }
    }

    spdlog::info("AllocateVirtual: Completed page allocation loop for {} pages",
                 numPages);

    m_memoryRegions.push_back({virtAddr, size, type, false});
    auto end_time = std::chrono::steady_clock::now();
    auto latency = std::chrono::duration_cast<std::chrono::microseconds>(
                       end_time - start_time)
                       .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info(
        "Allocated virtual memory: addr=0x{:x}, size={:#x}, process={}",
        virtAddr, size, processId);
    return virtAddr;
  } catch (const std::exception &e) {
    spdlog::error("Virtual allocation failed: {}", e.what());
    throw;
  }
}
// GetMemoryType implementation
MemoryType PS4MMU::GetMemoryType(uint64_t virtAddr, uint64_t processId) const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  try {
    auto it = m_pageTables.find(processId);
    if (it == m_pageTables.end()) {
      spdlog::warn("No page table for process {}", processId);
      return MemoryType::Default;
    }

    uint64_t pageIndex = virtAddr / PAGE_SIZE;
    const auto &pageTable = it->second;
    if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present) {
      spdlog::warn("Page not present: 0x{:x}", virtAddr);
      return MemoryType::Default;
    }

    return pageTable[pageIndex].type;
  } catch (const std::exception &e) {
    spdlog::error("GetMemoryType failed: {}", e.what());
    return MemoryType::Default;
  }
}

// SetMemoryType implementation
bool PS4MMU::SetMemoryType(uint64_t virtAddr, uint64_t size, uint64_t processId,
                           MemoryType type) {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");
  spdlog::info("Setting memory type: virt=0x{:x}, size={:#x}, type={}",
               virtAddr, size, static_cast<int>(type));
  try {
    auto it_pt = m_pageTables.find(processId);
    if (it_pt == m_pageTables.end()) {
      spdlog::error("No page table for process {}. Cannot set memory type for "
                    "region at 0x{:x}",
                    processId, virtAddr);
      return false;
    }
    auto &pageTable = it_pt->second;

    for (uint64_t addr = virtAddr; addr < virtAddr + size; addr += PAGE_SIZE) {
      uint64_t pageIndex = addr / PAGE_SIZE;
      if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present) {
        spdlog::error("Cannot set type for invalid page: 0x{:x}", addr);
        return false;
      }
      pageTable[pageIndex].type = type;
      m_tlb->Invalidate(addr, processId);
    }

    bool region_updated = false;
    for (auto &region : m_memoryRegions) {
      if (region.start == virtAddr && region.size == size) {
        region.type = type;
        region_updated = true;
        break;
      }
    }
    if (!region_updated) {
      spdlog::debug("SetMemoryType: No exact matching MemoryRegion found for "
                    "0x{:x}-0x{:x}. PTEs updated.",
                    virtAddr, virtAddr + size);
    }

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Set memory type: virt=0x{:x}, size={:#x}", virtAddr, size);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SetMemoryType failed: {}", e.what());
    return false;
  }
}

// GetPageProtection implementation
int PS4MMU::GetPageProtection(uint64_t virtAddr, uint64_t processId) const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  try {
    auto it = m_pageTables.find(processId);
    if (it == m_pageTables.end()) {
      spdlog::warn("No page table for process {}", processId);
      return PROT_NONE;
    }

    uint64_t pageIndex = virtAddr / PAGE_SIZE;
    const auto &pageTable = it->second;
    if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present) {
      spdlog::warn("Page not present: 0x{:x}", virtAddr);
      return PROT_NONE;
    }

    return pageTable[pageIndex].protection;
  } catch (const std::exception &e) {
    spdlog::error("GetPageProtection failed: {}", e.what());
    return PROT_NONE;
  }
}

// GetReadCount implementation
uint64_t PS4MMU::GetReadCount() const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  return m_readBytes.load();
}

// GetWriteCount implementation
uint64_t PS4MMU::GetWriteCount() const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  return m_writeBytes.load();
}

// GetPageFaultCount implementation
uint64_t PS4MMU::GetPageFaultCount() const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  return m_stats.pageFaults.load();
}

// GetCompressedPageCount implementation
uint64_t PS4MMU::GetCompressedPageCount() const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  return m_stats.compressedPages.load();
}

// GetCompressionRatio implementation
float PS4MMU::GetCompressionRatio() const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  if (m_compressor) {
    return m_stats.compressionRatio.load();
  }
  return 1.0f;
}

// GetCompressionCycles implementation
uint64_t PS4MMU::GetCompressionCycles() const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  if (m_compressor) {
    // CompressionStats doesn't have totalLatencyUs, return 0 for now
    return 0;
  }
  return 0;
}

// GetDecompressionCycles implementation
uint64_t PS4MMU::GetDecompressionCycles() const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  if (m_compressor) {
    // CompressionStats doesn't have totalLatencyUs, return 0 for now
    return 0;
  }
  return 0;
}

// GetBandwidthUsage implementation
float PS4MMU::GetBandwidthUsage() const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  auto elapsedSeconds = m_stats.totalLatencyUs.load() / 1e6;
  if (elapsedSeconds == 0)
    return 0.0f;

  double totalBytes =
      static_cast<double>(m_readBytes.load() + m_writeBytes.load());
  double totalMegaBytes = totalBytes / (1024.0 * 1024.0);

  return static_cast<float>(totalMegaBytes / elapsedSeconds);
}

// ResetBandwidthCounters implementation
void PS4MMU::ResetBandwidthCounters() {
  MEMORY_LOCK(m_mutex, "MMUMutex");
  m_readBytes = 0;
  m_writeBytes = 0;
  spdlog::info("Bandwidth byte counters reset. Latency "
               "(m_stats.totalLatencyUs) not reset by this function.");
}

// SaveState implementation
void PS4MMU::SaveState(std::ostream &out) const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  try {
    nlohmann::json state;
    state["size"] = m_size;
    // RACE CONDITION FIX: Load atomic values for serialization
    state["readBytes"] = m_readBytes.load();
    state["writeBytes"] = m_writeBytes.load();
    state["stats"] = {{"totalPages", m_stats.totalPages.load()},
                      {"usedPages", m_stats.usedPages.load()},
                      {"freePages", m_stats.freePages.load()},
                      {"compressedPages", m_stats.compressedPages.load()},
                      {"swappedPages", m_stats.swappedPages.load()},
                      {"compressionRatio", m_stats.compressionRatio.load()},
                      {"swapUsageRatio", m_stats.swapUsageRatio.load()},
                      {"pageFaults", m_stats.pageFaults.load()},
                      {"totalLatencyUs", m_stats.totalLatencyUs.load()},
                      {"hits", m_stats.hits.load()},
                      {"misses", m_stats.misses.load()}};
    state["memoryRegions"] = nlohmann::json::array();
    for (const auto &region : m_memoryRegions) {
      state["memoryRegions"].push_back({{"start", region.start},
                                        {"size", region.size},
                                        {"type", static_cast<int>(region.type)},
                                        {"dynamic", region.dynamic}});
    }

    out << state.dump(2);
    if (!out.good()) {
      throw std::runtime_error("Output stream error during MMU state save.");
    }
    spdlog::info("Saved MMU state (partially: core stats and regions)");
  } catch (const std::exception &e) {
    spdlog::error("SaveState failed: {}", e.what());
  }
}

// LoadState implementation
void PS4MMU::LoadState(std::istream &in) {
  MEMORY_LOCK(m_mutex, "MMUMutex");
  try {
    nlohmann::json state;
    in >> state;
    if (in.fail()) {
      throw std::runtime_error(
          "Input stream error or invalid JSON format during MMU state load.");
    }

    m_size = state["size"];
    // RACE CONDITION FIX: Store values into atomic members
    m_readBytes.store(state["readBytes"]);
    m_writeBytes.store(state["writeBytes"]);
    m_stats.totalPages.store(state["stats"]["totalPages"]);
    m_stats.usedPages.store(state["stats"]["usedPages"]);
    m_stats.freePages.store(state["stats"]["freePages"]);
    m_stats.compressedPages.store(state["stats"]["compressedPages"]);
    m_stats.swappedPages.store(state["stats"]["swappedPages"]);
    m_stats.compressionRatio.store(state["stats"]["compressionRatio"]);
    m_stats.swapUsageRatio.store(state["stats"]["swapUsageRatio"]);
    m_stats.pageFaults.store(state["stats"]["pageFaults"]);
    m_stats.totalLatencyUs.store(state["stats"]["totalLatencyUs"]);
    m_stats.hits.store(state["stats"].value("hits", 0));
    m_stats.misses.store(state["stats"].value("misses", 0));

    m_memoryRegions.clear();
    for (const auto &region_json : state["memoryRegions"]) {
      m_memoryRegions.push_back(
          {region_json["start"], region_json["size"],
           static_cast<MemoryType>(region_json["type"].get<int>()),
           region_json["dynamic"]});
    }
    if (m_tlb)
      m_tlb->Clear();

    spdlog::info("Loaded MMU state (partially: core stats and regions)");
  } catch (const std::exception &e) {
    spdlog::error("LoadState failed: {}", e.what());
    throw;
  }
}

// SetPrefetchHint implementation
void PS4MMU::SetPrefetchHint(PrefetchHint hint) {
  MEMORY_LOCK(m_mutex, "MMUMutex");
  m_prefetchHint = hint;
  spdlog::info("Set prefetch hint to {}", static_cast<int>(hint));
}

// GetPrefetcherStats implementation
ps4::MemoryPrefetcher::Stats PS4MMU::GetPrefetcherStats() const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  if (m_prefetcher) {
    return m_prefetcher->GetStats();
  }
  return ps4::MemoryPrefetcher::Stats{};
}

// GetCompressionStats implementation
ps4::CompressionStats PS4MMU::GetCompressionStats() const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  if (m_compressor) {
    return m_compressor->GetStats();
  }
  return {};
}

// SetCompressionPolicy implementation
void PS4MMU::SetCompressionPolicy(MemoryCompressor::CompressionPolicy policy) {
  MEMORY_LOCK(m_mutex, "MMUMutex");
  if (m_compressor) {
    m_compressor->SetCompressionPolicy(policy);
  }
  spdlog::info("Set compression policy to {}", static_cast<int>(policy));
}

// SetCompressionAlgorithm implementation
void PS4MMU::SetCompressionAlgorithm(
    MemoryCompressor::CompressionAlgorithm algorithm) {
  MEMORY_LOCK(m_mutex, "MMUMutex");
  if (m_compressor) {
    m_compressor->SetCompressionAlgorithm(algorithm);
  }
  spdlog::info("Set compression algorithm to {}", static_cast<int>(algorithm));
}

// EnableCompression implementation
void PS4MMU::EnableCompression(bool enabled) {
  MEMORY_LOCK(m_mutex, "MMUMutex");
  m_compressionEnabled.store(enabled, std::memory_order_relaxed);
  spdlog::info("Compression {}", enabled ? "enabled" : "disabled");
}

// GetMemoryStats implementation
MemoryStats PS4MMU::GetMemoryStats() const {
  MEMORY_SHARED_LOCK(m_mutex, "MMUMutex");
  return m_stats;
}

// GetStats implementation (alias for GetMemoryStats)
MemoryStats PS4MMU::GetStats() const { return GetMemoryStats(); }

// CompressEligiblePages implementation
uint64_t PS4MMU::CompressEligiblePages() {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");
  uint64_t compressedCount = 0;
  spdlog::info("Compressing eligible pages...");
  if (!m_compressionEnabled.load(std::memory_order_relaxed)) {
    spdlog::info("Compression is disabled, skipping CompressEligiblePages.");
    return 0;
  }

  try {
    for (auto &ptPair : m_pageTables) {
      auto &pageTable = ptPair.second;
      for (size_t i = 0; i < pageTable.size(); ++i) {
        if (pageTable[i].present && pageTable[i].compressedId == 0 &&
            !pageTable[i].swapped) {
          uint64_t virtAddr = i * PAGE_SIZE;
          if (TryCompressPage(virtAddr, ptPair.first)) {
            compressedCount++;
          }
        }
      }
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Compressed {} pages", compressedCount);
    return compressedCount;
  } catch (const std::exception &e) {
    spdlog::error("CompressEligiblePages failed: {}", e.what());
    return 0;
  }
}

// DecompressAllPages implementation
uint64_t PS4MMU::DecompressAllPages(uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");
  uint64_t decompressedCount = 0;
  spdlog::info("Decompressing all pages for process {}", processId);
  try {
    auto it = m_pageTables.find(processId);
    if (it == m_pageTables.end()) {
      spdlog::warn("No page table for process {}", processId);
      return 0;
    }
    auto &pageTable = it->second;
    for (size_t i = 0; i < pageTable.size(); ++i) {
      if (pageTable[i].present && pageTable[i].compressedId != 0) {
        // Page is present and compressed.
        uint64_t virtAddr = i * PAGE_SIZE;
        if (DecompressPageIfNeeded(virtAddr, processId)) {
          decompressedCount++;
        } else {
          spdlog::error("Failed to decompress page 0x{:x} for process {}",
                        virtAddr, processId);
        }
      }
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Decompressed {} pages for process {}", decompressedCount,
                 processId);
    return decompressedCount;
  } catch (const std::exception &e) {
    spdlog::error("DecompressAllPages failed: {}", e.what());
    return 0;
  }
}

// ReconfigureMemoryRegion implementation
bool PS4MMU::ReconfigureMemoryRegion(uint64_t start_addr, uint64_t size,
                                     MemoryType type, bool dynamic) {
  auto startTime = std::chrono::steady_clock::now();
  MEMORY_LOCK(m_mutex, "MMUMutex");
  spdlog::info("Reconfiguring memory region: start=0x{:x}, size={:#x}, type={}",
               start_addr, size, static_cast<int>(type));
  try {
    // Validate the region
    if (start_addr % PAGE_SIZE != 0 || size % PAGE_SIZE != 0) {
      spdlog::error(
          "Invalid alignment for memory region: start=0x{:x}, size={:#x}",
          start_addr, size);
      return false;
    }

    auto it_mr =
        std::find_if(m_memoryRegions.begin(), m_memoryRegions.end(),
                     [start_addr, size](const MemoryRegion &region) {
                       return region.start == start_addr && region.size == size;
                     });
    if (it_mr == m_memoryRegions.end()) {
      spdlog::error("Memory region not found: start=0x{:x}, size={:#x}",
                    start_addr, size);
      return false;
    }
    if (!it_mr->dynamic && dynamic == false) {
      spdlog::error(
          "Memory region is not dynamic, cannot reconfigure: start=0x{:x}",
          start_addr);
      return false;
    }

    // Update the region in m_memoryRegions list
    it_mr->type = type;
    it_mr->dynamic = dynamic;

    // Update page table entries for all processes mapping this region
    for (auto &ptPair : m_pageTables) {
      auto &pageTable = ptPair.second;
      for (uint64_t addr = start_addr; addr < start_addr + size;
           addr += PAGE_SIZE) {
        uint64_t pageIndex = addr / PAGE_SIZE;
        if (pageIndex < pageTable.size() && pageTable[pageIndex].present) {
          pageTable[pageIndex].type = type;
          m_tlb->Invalidate(addr, ptPair.first);
        }
      }
    }

    auto end_time = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end_time -
                                                              startTime)
            .count();
    spdlog::info("Reconfigured memory region: start=0x{:x}, size={:#x}",
                 start_addr, size);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ReconfigureMemoryRegion failed: {}", e.what());
    return false;
  }
}

// Private helper: FindFreePhysicalBlock implementation
uint64_t PS4MMU::FindFreePhysicalBlock(uint64_t size) {
  if (!m_physAlloc) {
    spdlog::error(
        "FindFreePhysicalBlock: PhysicalMemoryAllocator is not initialized.");
    return ALLOC_FAILED;
  }
  return m_physAlloc->Allocate(size);
}

// Private helper: MarkPageDirty implementation
void PS4MMU::MarkPageDirty(uint64_t virtAddr, uint64_t processId) {
  auto it_pt = m_pageTables.find(processId);
  if (it_pt == m_pageTables.end()) {
    spdlog::warn("MarkPageDirty: No page table for process {}. Cannot mark "
                 "page 0x{:x}",
                 processId, virtAddr);
    return;
  }
  auto &pageTable = it_pt->second;
  uint64_t pageIndex = virtAddr / PAGE_SIZE;
  if (pageIndex < pageTable.size() && pageTable[pageIndex].present) {
    pageTable[pageIndex].dirty = true;
    spdlog::debug("Marked page dirty: virt=0x{:x}", virtAddr);
  } else {
    spdlog::warn(
        "MarkPageDirty: Page not present or out of bounds for virt=0x{:x}",
        virtAddr);
  }
}

// Private helper: CheckProtection implementation
void PS4MMU::CheckProtection(const PageTableEntry &page, bool write,
                             uint64_t virtAddr) const {
  if (!page.present) {
    throw std::runtime_error(
        fmt::format("Page not present (CheckProtection): 0x{:x}", virtAddr));
  }
  if (write && !(page.protection & PROT_WRITE)) {
    throw std::runtime_error(
        fmt::format("Write access denied: 0x{:x}", virtAddr));
  }
  if (!write && !(page.protection & PROT_READ)) {
    throw std::runtime_error(
        fmt::format("Read access denied: 0x{:x}", virtAddr));
  }
}

// Private helper: CheckMemoryTypeAccess implementation
void PS4MMU::CheckMemoryTypeAccess(MemoryType type, bool write,
                                   uint64_t virtAddr, int protection) const {
  switch (type) {
  case MemoryType::SYSTEM:
    if (write) {
      throw std::runtime_error(
          fmt::format("Write to SYSTEM memory denied: 0x{:x}", virtAddr));
    }
    break;
  case MemoryType::IO:
    if (write && !(protection & PROT_WRITE)) {
      throw std::runtime_error(fmt::format(
          "Write to IO memory denied by protection: 0x{:x}", virtAddr));
    }
    if (!write && !(protection & PROT_READ)) {
      throw std::runtime_error(fmt::format(
          "Read from IO memory denied by protection: 0x{:x}", virtAddr));
    }
    break;
  case MemoryType::VIDEO:
    if (write && !(protection & PROT_WRITE)) {
      throw std::runtime_error(
          fmt::format("Write to VIDEO memory denied: 0x{:x}", virtAddr));
    }
    if (!write && !(protection & PROT_READ)) {
      throw std::runtime_error(
          fmt::format("Read from VIDEO memory denied: 0x{:x}", virtAddr));
    }
    break;
  case MemoryType::Default:
  default:
    break;
  }
}

// Private helper: CheckAndCompressPage implementation
void PS4MMU::CheckAndCompressPage(uint64_t virtAddr, uint64_t processId) {
  if (!m_compressionEnabled.load(std::memory_order_relaxed))
    return;

  MEMORY_LOCK(m_mutex, "MMUMutex");
  auto &pageTable = m_pageTables[processId];
  uint64_t pageIndex = virtAddr / PAGE_SIZE;
  if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present ||
      pageTable[pageIndex].compressedId != 0) {
    return;
  }

  auto &accessInfo = m_pageAccessInfo[processId][virtAddr];
  auto now = std::chrono::steady_clock::now();
  auto timeSinceLastAccess =
      std::chrono::duration_cast<std::chrono::milliseconds>(
          now - pageTable[pageIndex].lastAccessTime)
          .count();

  if (m_compressor->ShouldCompressPage(accessInfo.accessCount,
                                       pageTable[pageIndex].lastAccessTime)) {
    if (m_stats.usedPages > (m_stats.totalPages * 0.8)) {
      std::vector<std::pair<uint64_t, uint64_t>> candidates;

      for (const auto &entry : pageTable) {
        if (entry.present && !entry.swapped && entry.compressedId == 0) {
          auto entryAccessTime =
              std::chrono::duration_cast<std::chrono::milliseconds>(
                  entry.lastAccessTime.time_since_epoch())
                  .count();
          candidates.emplace_back(virtAddr, entryAccessTime);
        }
      }

      // LOGIC FIX: Use function object to avoid consteval issues
      struct CompareBySecond {
        bool operator()(const std::pair<uint64_t, uint64_t> &a,
                        const std::pair<uint64_t, uint64_t> &b) const {
          return a.second < b.second;
        }
      };
      std::sort(candidates.begin(), candidates.end(), CompareBySecond{});

      size_t maxCandidates =
          std::min(candidates.size(), static_cast<size_t>(10));
      for (size_t i = 0; i < maxCandidates; ++i) {
        uint64_t candidateAddr = candidates[i].first;
        if (candidateAddr != virtAddr) {
          if (!TryCompressPage(candidateAddr, processId)) {
            SwapPageOut(candidateAddr, processId);
          }
        }
      }
    }

    TryCompressPage(virtAddr, processId);
  }
}

// Private helper: DecompressPageIfNeeded implementation
bool PS4MMU::DecompressPageIfNeeded(uint64_t virtAddr, uint64_t processId) {
  MEMORY_LOCK(m_mutex, "MMUMutex");
  auto &pageTable = m_pageTables[processId];
  uint64_t pageIndex = virtAddr / PAGE_SIZE;
  if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present ||
      pageTable[pageIndex].compressedId == 0) {
    return false;
  }

  std::vector<uint8_t> data(PAGE_SIZE);
  if (!m_compressor->DecompressPage(pageTable[pageIndex].compressedId,
                                    data.data(), PAGE_SIZE)) {
    spdlog::error("Failed to decompress page: virt=0x{:x}", virtAddr);
    return false;
  }

  uint64_t physAddr = m_physAlloc->Allocate(PAGE_SIZE);
  if (physAddr == ALLOC_FAILED) {
    spdlog::error("Failed to allocate physical page for decompression");
    return false;
  }

  std::memcpy(m_physicalMemory.data() + physAddr, data.data(), PAGE_SIZE);
  m_compressor->FreeCompressedPage(pageTable[pageIndex].compressedId);
  pageTable[pageIndex].physAddr = physAddr;
  pageTable[pageIndex].compressedId = 0;
  m_tlb->Insert(virtAddr, physAddr, PAGE_SIZE, processId);
  m_stats.compressedPages--;
  spdlog::info("Decompressed page: virt=0x{:x}", virtAddr);
  return true;
}

// Private helper: TryCompressPage implementation
bool PS4MMU::TryCompressPage(uint64_t virtAddr, uint64_t processId) {
  MEMORY_LOCK(m_mutex, "MMUMutex");
  auto &pageTable = m_pageTables[processId];
  uint64_t pageIndex = virtAddr / PAGE_SIZE;
  if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present ||
      pageTable[pageIndex].compressedId != 0 || pageTable[pageIndex].swapped) {
    return false;
  }

  std::vector<uint8_t> data(PAGE_SIZE);
  std::memcpy(data.data(),
              m_physicalMemory.data() + pageTable[pageIndex].physAddr,
              PAGE_SIZE);
  uint64_t compressedId;
  if (!m_compressor->CompressPage(data.data(), data.size(), compressedId)) {
    spdlog::debug("Page not compressed: virt=0x{:x}", virtAddr);
    return false;
  }
  if (compressedId == ALLOC_FAILED) {
    spdlog::debug("Page not compressed: virt=0x{:x}", virtAddr);
    return false;
  }

  m_physAlloc->Free(pageTable[pageIndex].physAddr, PAGE_SIZE);
  pageTable[pageIndex].physAddr = ALLOC_FAILED;
  pageTable[pageIndex].compressedId = compressedId;
  m_stats.compressedPages.fetch_add(1, std::memory_order_relaxed);
  m_tlb->Invalidate(virtAddr, processId);
  spdlog::info("Compressed page: virt=0x{:x}", virtAddr);
  return true;
}

// Private helper: UpdatePageAccess implementation
void PS4MMU::UpdatePageAccess(uint64_t virtAddr, uint64_t processId) {
  MEMORY_LOCK(m_accessMutex, "AccessMutex");
  auto &accessInfo = m_pageAccessInfo[processId][virtAddr];
  accessInfo.accessCount++;
}

// Private helper: HandleAccess implementation
void PS4MMU::HandleAccess(uint64_t virtAddr, size_t size, bool isWrite,
                          uint64_t processId) {
  UpdatePageAccess(virtAddr, processId);
  if (m_prefetchHint != PrefetchHint::PREFETCH_NONE) {
  }
  if (m_compressionEnabled.load(std::memory_order_relaxed)) {
    CheckAndCompressPage(virtAddr, processId);
  }
}

// Private helper: SwapPageOut implementation
bool PS4MMU::SwapPageOut(uint64_t virtAddr, uint64_t processId) {
  MEMORY_LOCK(m_mutex, "MMUMutex");
  auto &pageTable = m_pageTables[processId];
  uint64_t pageIndex = virtAddr / PAGE_SIZE;
  if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present ||
      pageTable[pageIndex].swapped || pageTable[pageIndex].compressedId != 0) {
    return false;
  }

  std::vector<uint8_t> data(PAGE_SIZE);
  std::memcpy(data.data(),
              m_physicalMemory.data() + pageTable[pageIndex].physAddr,
              PAGE_SIZE);
  if (!m_swapManager->SwapOut(virtAddr, processId, data.data())) {
    spdlog::error("Failed to swap out page: virt=0x{:x}", virtAddr);
    return false;
  }

  m_physAlloc->Free(pageTable[pageIndex].physAddr, PAGE_SIZE);
  pageTable[pageIndex].physAddr = ALLOC_FAILED;
  pageTable[pageIndex].swapped = true;
  m_stats.swappedPages.fetch_add(1, std::memory_order_relaxed);
  m_tlb->Invalidate(virtAddr, processId);
  spdlog::info("Swapped out page: virt=0x{:x}", virtAddr);
  return true;
}

// Private helper: SwapPageIn implementation
bool PS4MMU::SwapPageIn(uint64_t virtAddr, uint64_t processId) {
  MEMORY_LOCK(m_mutex, "MMUMutex");
  auto &pageTable = m_pageTables[processId];
  uint64_t pageIndex = virtAddr / PAGE_SIZE;
  if (pageIndex >= pageTable.size() || !pageTable[pageIndex].present ||
      !pageTable[pageIndex].swapped) {
    return false;
  }

  std::vector<uint8_t> data(PAGE_SIZE);
  if (!m_swapManager->SwapIn(virtAddr, processId, data.data())) {
    spdlog::error("Failed to swap in page: virt=0x{:x}", virtAddr);
    return false;
  }

  uint64_t physAddr = m_physAlloc->Allocate(PAGE_SIZE);
  if (physAddr == ALLOC_FAILED) {
    spdlog::error("Failed to allocate physical page for swap-in");
    return false;
  }

  std::memcpy(m_physicalMemory.data() + physAddr, data.data(), PAGE_SIZE);
  pageTable[pageIndex].physAddr = physAddr;
  pageTable[pageIndex].swapped = false;
  m_stats.swappedPages--;
  m_tlb->Insert(virtAddr, physAddr, PAGE_SIZE, processId);
  spdlog::info("Swapped in page: virt=0x{:x}", virtAddr);
  return true;
}

// Override AllocatePageTableLevel for proper page table allocation
uint64_t PS4MMU::AllocatePageTableLevel(int level) {
  auto start = std::chrono::steady_clock::now();
  try {
    uint64_t physAddr = m_physAlloc->Allocate(PAGE_SIZE);
    if (physAddr == ALLOC_FAILED) {
      throw std::runtime_error("Failed to allocate page table level");
    }
    std::memset(m_physicalMemory.data() + physAddr, 0, PAGE_SIZE);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::debug("Allocated page table level {} at phys=0x{:x}", level,
                  physAddr);
    return physAddr;
  } catch (const std::exception &e) {
    spdlog::error("AllocatePageTableLevel failed: {}", e.what());
    throw;
  }
}

/**
 * @brief Performs full x86-64 multi-level page table walk.
 * This function emulates the 4-level paging of the x86-64 architecture to
 * translate a virtual address to a physical address.
 * @param virtAddr Virtual address to translate.
 * @param processId Process ID.
 * @param write True if the memory access is a write operation.
 * @param cr3 The physical address of the Level 4 Page Map Table (PML4).
 * @return Physical address, or ALLOC_FAILED if translation fails or is
 * prohibited.
 */
uint64_t PS4MMU::WalkPageTable(uint64_t virtAddr, uint64_t processId,
                               bool write, uint64_t cr3) const {
  try {
    // Extract indices for each page table level from the virtual address.
    // x86-64 uses a 48-bit virtual address, split into:
    // - 9 bits for PML4 index
    // - 9 bits for PDPT index
    // - 9 bits for PD index
    // - 9 bits for PT index
    // - 12 bits for page offset
    uint64_t pml4Index = (virtAddr >> 39) & 0x1FF;
    uint64_t pdptIndex = (virtAddr >> 30) & 0x1FF;
    uint64_t pdIndex = (virtAddr >> 21) & 0x1FF;
    uint64_t ptIndex = (virtAddr >> 12) & 0x1FF;
    uint64_t offset = virtAddr & 0xFFF;

    spdlog::trace(
        "x86-64 Page Walk: virt=0x{:x}, pml4={}, pdpt={}, pd={}, pt={}",
        virtAddr, pml4Index, pdptIndex, pdIndex, ptIndex);

    // --- Level 4: Page-Map Level 4 (PML4) ---
    X86_64PageTableEntry pml4e;
    uint64_t pml4e_addr = cr3 + pml4Index * sizeof(X86_64PageTableEntry);
    if (!ReadPhysical(pml4e_addr, &pml4e, sizeof(pml4e))) {
      spdlog::error("WalkPageTable: Failed to read PML4E at phys=0x{:x}",
                    pml4e_addr);
      return ALLOC_FAILED;
    }

    if (!pml4e.bits.present) {
      spdlog::debug("WalkPageTable: PML4E not present for virt=0x{:x}",
                    virtAddr);
      return ALLOC_FAILED; // Page Fault
    }

    // --- Level 3: Page-Directory-Pointer Table (PDPT) ---
    uint64_t pdpt_base = pml4e.GetPhysicalAddress();
    X86_64PageTableEntry pdpte;
    uint64_t pdpte_addr = pdpt_base + pdptIndex * sizeof(X86_64PageTableEntry);
    if (!ReadPhysical(pdpte_addr, &pdpte, sizeof(pdpte))) {
      spdlog::error("WalkPageTable: Failed to read PDPTE at phys=0x{:x}",
                    pdpte_addr);
      return ALLOC_FAILED;
    }

    if (!pdpte.bits.present) {
      spdlog::debug("WalkPageTable: PDPTE not present for virt=0x{:x}",
                    virtAddr);
      return ALLOC_FAILED; // Page Fault
    }

    // Check for 1GB huge pages
    if (pdpte.bits.pageSize) {
      if (write && !pdpte.bits.writable) {
        spdlog::warn(
            "WalkPageTable: Write protection fault on 1GB page for virt=0x{:x}",
            virtAddr);
        return ALLOC_FAILED;
      }
      uint64_t physAddr = pdpte.GetPhysicalAddress() + (virtAddr & 0x3FFFFFFF);
      spdlog::trace("1GB huge page translation: virt=0x{:x} -> phys=0x{:x}",
                    virtAddr, physAddr);
      return physAddr;
    }

    // --- Level 2: Page Directory (PD) ---
    uint64_t pd_base = pdpte.GetPhysicalAddress();
    X86_64PageTableEntry pde;
    uint64_t pde_addr = pd_base + pdIndex * sizeof(X86_64PageTableEntry);
    if (!ReadPhysical(pde_addr, &pde, sizeof(pde))) {
      spdlog::error("WalkPageTable: Failed to read PDE at phys=0x{:x}",
                    pde_addr);
      return ALLOC_FAILED;
    }

    if (!pde.bits.present) {
      spdlog::debug("WalkPageTable: PDE not present for virt=0x{:x}", virtAddr);
      return ALLOC_FAILED; // Page Fault
    }

    // Check for 2MB huge pages
    if (pde.bits.pageSize) {
      if (write && !pde.bits.writable) {
        spdlog::warn(
            "WalkPageTable: Write protection fault on 2MB page for virt=0x{:x}",
            virtAddr);
        return ALLOC_FAILED;
      }
      uint64_t physAddr = pde.GetPhysicalAddress() + (virtAddr & 0x1FFFFF);
      spdlog::trace("2MB huge page translation: virt=0x{:x} -> phys=0x{:x}",
                    virtAddr, physAddr);
      return physAddr;
    }

    // --- Level 1: Page Table (PT) ---
    uint64_t pt_base = pde.GetPhysicalAddress();
    X86_64PageTableEntry pte;
    uint64_t pte_addr = pt_base + ptIndex * sizeof(X86_64PageTableEntry);
    if (!ReadPhysical(pte_addr, &pte, sizeof(pte))) {
      spdlog::error("WalkPageTable: Failed to read PTE at phys=0x{:x}",
                    pte_addr);
      return ALLOC_FAILED;
    }

    if (!pte.bits.present) {
      spdlog::debug("WalkPageTable: PTE not present for virt=0x{:x}", virtAddr);
      return ALLOC_FAILED; // Page Fault
    }

    // Final protection checks for a 4KB page
    if (write && !pte.bits.writable) {
      spdlog::warn(
          "WalkPageTable: Write protection fault on 4KB page for virt=0x{:x}",
          virtAddr);
      return ALLOC_FAILED; // Page Fault
    }

    // At this point, translation is successful.
    uint64_t finalPhysAddr = pte.GetPhysicalAddress() + offset;

    // The hardware would set the Accessed bit here. We can emulate that.
    // A mutable ReadPhysical/WritePhysical would be needed to modify the entry
    // in-place. For now, we'll skip modifying the Accessed/Dirty bits to keep
    // the const qualifier.

    spdlog::trace("4KB page translation: virt=0x{:x} -> phys=0x{:x}", virtAddr,
                  finalPhysAddr);
    return finalPhysAddr;

  } catch (const std::exception &e) {
    spdlog::error("x86-64 page walk failed with exception: {}", e.what());
    return ALLOC_FAILED;
  }
}

/**
 * @brief Handles page faults with comprehensive fault type analysis and
 * recovery.
 * @param virtAddr Virtual address that caused the fault.
 * @param processId Process ID.
 * @param write True if this was a write fault.
 * @return True if fault was handled successfully, false otherwise.
 */
bool PS4MMU::HandlePageFault(uint64_t virtAddr, uint64_t processId,
                             bool write) {
  auto start = std::chrono::steady_clock::now();
  const auto timeout =
      std::chrono::seconds(5); // 5 second timeout for page fault handling
  MEMORY_LOCK(m_mutex, "MMUMutex");

  spdlog::info("Handling page fault: virt=0x{:x}, process={}, write={}",
               virtAddr, processId, write);

  try {
    // Check timeout before starting
    if (std::chrono::steady_clock::now() - start > timeout) {
      spdlog::error(
          "HandlePageFault: Timeout before processing for virt=0x{:x}",
          virtAddr);
      return false;
    }

    // Update page fault statistics
    m_stats.pageFaults.fetch_add(1, std::memory_order_relaxed);

    // Analyze the type of fault
    FaultType faultType = AnalyzeFaultType(virtAddr, processId, write);

    bool handled = false;

    switch (faultType) {
    case FaultType::DEMAND_PAGING:
      spdlog::debug("Handling demand paging fault for virt=0x{:x}", virtAddr);
      handled = HandleDemandPaging(virtAddr, processId);
      break;

    case FaultType::COPY_ON_WRITE:
      spdlog::debug("Handling copy-on-write fault for virt=0x{:x}", virtAddr);
      handled = HandleCopyOnWrite(virtAddr, processId);
      break;

    case FaultType::GUARD_PAGE:
      spdlog::debug("Handling guard page fault for virt=0x{:x}", virtAddr);
      handled = HandleGuardPageFault(virtAddr, processId);
      break;

    case FaultType::PROTECTION_FAULT:
      spdlog::error(
          "Protection fault cannot be automatically resolved: virt=0x{:x}",
          virtAddr);
      handled = false;
      break;

    case FaultType::UNKNOWN:
    default:
      spdlog::error("Unknown page fault type for virt=0x{:x}", virtAddr);
      handled = false;
      break;
    }

    // Check timeout after fault type handling
    if (std::chrono::steady_clock::now() - start > timeout) {
      spdlog::error(
          "HandlePageFault: Timeout after fault type handling for virt=0x{:x}",
          virtAddr);
      return false;
    }

    // If page was swapped out, try to swap it back in
    if (!handled) {
      // Check timeout before swap operation
      if (std::chrono::steady_clock::now() - start > timeout) {
        spdlog::error(
            "HandlePageFault: Timeout before swap operation for virt=0x{:x}",
            virtAddr);
        return false;
      }

      auto it = m_pageTables.find(processId);
      if (it != m_pageTables.end()) {
        uint64_t pageIndex = virtAddr / PAGE_SIZE;
        if (pageIndex < it->second.size() && it->second[pageIndex].swapped) {
          spdlog::debug("Attempting to swap in page for virt=0x{:x}", virtAddr);
          handled = SwapPageIn(virtAddr, processId);
        }
      }
    }

    // If page is compressed, decompress it
    if (!handled) {
      // Check timeout before decompression
      if (std::chrono::steady_clock::now() - start > timeout) {
        spdlog::error(
            "HandlePageFault: Timeout before decompression for virt=0x{:x}",
            virtAddr);
        return false;
      }

      auto it = m_pageTables.find(processId);
      if (it != m_pageTables.end()) {
        uint64_t pageIndex = virtAddr / PAGE_SIZE;
        if (pageIndex < it->second.size() && it->second[pageIndex].present &&
            it->second[pageIndex].compressedId != 0) {
          spdlog::debug("Attempting to decompress page for virt=0x{:x}",
                        virtAddr);
          handled = DecompressPageIfNeeded(virtAddr, processId);
        }
      }
    }

    auto end = std::chrono::steady_clock::now();
    auto latency = CalculateLatencyMicroseconds(start, end);
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

    if (handled) {
      spdlog::info(
          "Page fault handled successfully: virt=0x{:x}, type={} in {}us",
          virtAddr, static_cast<int>(faultType), latency);
    } else {
      spdlog::error(
          "Failed to handle page fault: virt=0x{:x}, type={} after {}us",
          virtAddr, static_cast<int>(faultType), latency);
      ExportMemoryStats("memory_stats_pagefault_failed.json");
    }

    return handled;

  } catch (const std::exception &e) {
    spdlog::error(
        "Page fault handling failed with exception: virt=0x{:x}, error={}",
        virtAddr, e.what());
    ExportMemoryStats("memory_stats_pagefault_exception.json");
    return false;
  }
}
