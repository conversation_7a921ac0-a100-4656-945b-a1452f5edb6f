// Copyright 2025 < Copyright Owner >

#include <algorithm>
#include <array>
#include <chrono>
#include <cmath>
#include <cstring>
#include <fstream>
#include <immintrin.h>
#include <istream>
#include <memory>
#include <mutex>
#include <ostream>
#include <queue>
#include <random>
#include <stdexcept>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

#ifdef _WIN32
#include <intrin.h>
#else
#pragma once
#include <atomic>
#endif

#include <spdlog/spdlog.h>

namespace cpu::diag
{
// Can be toggled from a CLI flag, an ImGui checkbox, an INI entry, etc.
inline std::atomic_bool g_enable_vector_access_log{false};

inline void set_vector_access_log(bool on) noexcept
{
    g_enable_vector_access_log.store(on, std::memory_order_relaxed);
}

// Drop-in replacement for the old hard-wired macro
#define CPU_DIAG_VECTOR_ACCESS(fmt_, ...)                                        \
    do {                                                                         \
        if (cpu::diag::g_enable_vector_access_log.load(std::memory_order_relaxed))\
            SPDLOG_DEBUG("VECTOR_ACCESS: " fmt_, __VA_ARGS__);                   \
    } while (false)

} // namespace cpu::diag

// AVX-512 helper functions for platforms that may not support them
#ifndef _mm512_castps512_ps256
#define _mm512_castps512_ps256(a) _mm256_castsi256_ps(_mm512_castsi512_si256(_mm512_castps_si512(a)))
#endif

#include "cache/cache.h"
#include "common/lock_ordering.h"
#include "cpu/decoded_instruction.h"
#include "cpu/instruction_decoder.h"
#include "cpu/x86_64_cpu.h"
#include "cpu/x86_64_pipeline.h"
#include "debug/vector_debug.h"
#include "emulator/apic.h"
#include "emulator/interrupt_handler.h"
#include "jit/x86_64_jit_compiler.h"
#include "memory/tlb.h"
#include "ps4/ps4_emulator.h"

namespace x86_64 {

// CPU Feature Detection
namespace {
struct CPUFeatures {
  bool avx512f = false;
  bool avx2 = false;
  bool avx = false;
  bool sse42 = false;
  bool sse41 = false;
  bool ssse3 = false;
  bool sse3 = false;
  bool sse2 = false;
  bool sse = false;
};

CPUFeatures DetectCPUFeatures() {
  CPUFeatures features;

#ifdef _WIN32
  int cpuInfo[4];

  // Check for basic CPUID support
  __cpuid(cpuInfo, 0);
  int maxLeaf = cpuInfo[0];

  if (maxLeaf >= 1) {
    __cpuid(cpuInfo, 1);
    features.sse = (cpuInfo[3] & (1 << 25)) != 0;
    features.sse2 = (cpuInfo[3] & (1 << 26)) != 0;
    features.sse3 = (cpuInfo[2] & (1 << 0)) != 0;
    features.ssse3 = (cpuInfo[2] & (1 << 9)) != 0;
    features.sse41 = (cpuInfo[2] & (1 << 19)) != 0;
    features.sse42 = (cpuInfo[2] & (1 << 20)) != 0;
    features.avx = (cpuInfo[2] & (1 << 28)) != 0;
  }

  if (maxLeaf >= 7) {
    __cpuidex(cpuInfo, 7, 0);
    features.avx2 = (cpuInfo[1] & (1 << 5)) != 0;
    features.avx512f = (cpuInfo[1] & (1 << 16)) != 0;
  }
#else
  // GCC/Clang intrinsics
  unsigned int eax, ebx, ecx, edx;

  if (__get_cpuid(0, &eax, &ebx, &ecx, &edx)) {
    unsigned int maxLeaf = eax;

    if (maxLeaf >= 1 && __get_cpuid(1, &eax, &ebx, &ecx, &edx)) {
      features.sse = (edx & (1 << 25)) != 0;
      features.sse2 = (edx & (1 << 26)) != 0;
      features.sse3 = (ecx & (1 << 0)) != 0;
      features.ssse3 = (ecx & (1 << 9)) != 0;
      features.sse41 = (ecx & (1 << 19)) != 0;
      features.sse42 = (ecx & (1 << 20)) != 0;
      features.avx = (ecx & (1 << 28)) != 0;
    }

    if (maxLeaf >= 7 && __get_cpuid_count(7, 0, &eax, &ebx, &ecx, &edx)) {
      features.avx2 = (ebx & (1 << 5)) != 0;
      features.avx512f = (ebx & (1 << 16)) != 0;
    }
  }
#endif

  spdlog::info("CPU Features detected: SSE={}, SSE2={}, SSE3={}, SSSE3={}, "
               "SSE4.1={}, SSE4.2={}, AVX={}, AVX2={}, AVX-512F={}",
               features.sse, features.sse2, features.sse3, features.ssse3,
               features.sse41, features.sse42, features.avx, features.avx2,
               features.avx512f);

  return features;
}

static const CPUFeatures g_cpuFeatures = DetectCPUFeatures();
} // namespace

// PIC Implementation
PIC::PIC()
    : icw1(0), icw4(0), imr(0xFF), isr(0), irr(0), baseVector(0x08),
      currentCommand(0), initSequence(0) {
  spdlog::info("PIC constructed");
}

void PIC::Reset() {
  icw1 = 0;
  icw4 = 0;
  imr = 0xFF; // Mask all interrupts
  isr = 0;
  irr = 0;
  baseVector = 0x08;
  currentCommand = 0;
  initSequence = 0;
  spdlog::info("PIC reset");
}

void PIC::Write(uint64_t address, uint64_t value, uint8_t size) {
  if (size != 1)
    return;

  uint8_t data = static_cast<uint8_t>(value);
  switch (address & 1) {
  case 0: // Command/ICW1
    if (data & 0x10) {
      // ICW1
      icw1 = data;
      initSequence = 1;
      imr = 0xFF; // Mask all during init
    } else {
      // OCW2/OCW3
      currentCommand = data;
    }
    break;
  case 1: // Data/ICW2-4/IMR
    if (initSequence == 1) {
      baseVector = data;
      initSequence = 2;
    } else if (initSequence == 2 && (icw1 & 0x02) == 0) {
      // ICW3 (cascade mode)
      initSequence = 3;
    } else if (initSequence >= 2) {
      // ICW4 or IMR
      if (icw1 & 0x01) {
        icw4 = data;
        initSequence = 0;
      } else {
        imr = data;
      }
    } else {
      imr = data;
    }
    break;
  }
  spdlog::trace("PIC write: addr=0x{:x}, value=0x{:x}", address, data);
}

uint64_t PIC::Read(uint64_t address, uint8_t size) {
  if (size != 1)
    return 0;

  switch (address & 1) {
  case 0: // Status
    return (currentCommand & 0x04) ? isr : irr;
  case 1: // IMR
    return imr;
  }
  return 0;
}

std::string PIC::GetName() const { return "PIC"; }

void PIC::SaveState(std::ostream &out) const {
  out.write(reinterpret_cast<const char *>(&icw1), sizeof(icw1));
  out.write(reinterpret_cast<const char *>(&icw4), sizeof(icw4));
  out.write(reinterpret_cast<const char *>(&imr), sizeof(imr));
  out.write(reinterpret_cast<const char *>(&isr), sizeof(isr));
  out.write(reinterpret_cast<const char *>(&irr), sizeof(irr));
  out.write(reinterpret_cast<const char *>(&baseVector), sizeof(baseVector));
}

void PIC::LoadState(std::istream &in) {
  in.read(reinterpret_cast<char *>(&icw1), sizeof(icw1));
  in.read(reinterpret_cast<char *>(&icw4), sizeof(icw4));
  in.read(reinterpret_cast<char *>(&imr), sizeof(imr));
  in.read(reinterpret_cast<char *>(&isr), sizeof(isr));
  in.read(reinterpret_cast<char *>(&irr), sizeof(irr));
  in.read(reinterpret_cast<char *>(&baseVector), sizeof(baseVector));
}

void PIC::SignalInterrupt(uint8_t irq) {
  // CRITICAL: Enhanced bounds check for IRQ
  if (irq >= 8) {
    spdlog::error("PIC::SignalInterrupt: invalid IRQ {}", irq);
    return;
  }
  irr |= (1 << irq);
  UpdateInterrupts();
}

void PIC::UpdateInterrupts() {
  uint8_t pending = irr & ~imr;
  if (pending && !cpu.expired()) {
    if (auto cpuPtr = cpu.lock()) {
      for (int i = 0; i < 8; ++i) {
        if (pending & (1 << i)) {
          cpuPtr->QueueInterrupt(baseVector + i, 0);
          isr |= (1 << i);
          irr &= ~(1 << i);
          break;
        }
      }
    }
  }
}

// PIT Implementation
PIT::PIT() : controlWord(0), currentChannel(0), lowByte(false) {
  try {
    counters[0] = 0;
    counters[1] = 0;
    counters[2] = 0;
    spdlog::info("PIT constructed");
  } catch (const std::exception &e) {
    spdlog::error("PIT constructor failed: {}", e.what());
    throw;
  } catch (...) {
    spdlog::error("PIT constructor failed with unknown exception");
    throw;
  }
}

void PIT::Reset() {
  controlWord = 0;
  currentChannel = 0;
  lowByte = false;
  counters[0] = 0;
  counters[1] = 0;
  counters[2] = 0;
  spdlog::info("PIT reset");
}

void PIT::Write(uint64_t address, uint64_t value, uint8_t size) {
  if (size != 1)
    return;

  uint8_t data = static_cast<uint8_t>(value);
  switch (address & 3) {
  case 0:
  case 1:
  case 2: {
    uint8_t channel = address & 3;
    // CRITICAL: Bounds check for counters array access
    if (channel >= 3) {
      spdlog::error("PIT Write: invalid channel {}", channel);
      return;
    }
    if (lowByte) {
      counters[channel] = (counters[channel] & 0xFF00) | data;
      lowByte = false;
    } else {
      counters[channel] = (counters[channel] & 0x00FF) | (data << 8);
      lowByte = true;
    }
    break;
  }
  case 3: // Control word
    controlWord = data;
    currentChannel = (data >> 6) & 3;
    if (currentChannel < 3) {
      lowByte = true; // Start with low byte
    }
    break;
  }
  spdlog::trace("PIT write: addr=0x{:x}, value=0x{:x}", address, data);
}

uint64_t PIT::Read(uint64_t address, uint8_t size) {
  if (size != 1)
    return 0;

  switch (address & 3) {
  case 0:
  case 1:
  case 2: {
    uint8_t channel = address & 3;
    // CRITICAL: Bounds check for counters array access
    if (channel >= 3) {
      spdlog::error("PIT Read: invalid channel {}", channel);
      return 0;
    }
    if (lowByte) {
      lowByte = false;
      return counters[channel] & 0xFF;
    } else {
      lowByte = true;
      return (counters[channel] >> 8) & 0xFF;
    }
  }
  case 3:
    return 0; // Control word is write-only
  }
  return 0;
}

std::string PIT::GetName() const { return "PIT"; }

void PIT::SaveState(std::ostream &out) const {
  out.write(reinterpret_cast<const char *>(counters), sizeof(counters));
  out.write(reinterpret_cast<const char *>(&controlWord), sizeof(controlWord));
  out.write(reinterpret_cast<const char *>(&currentChannel),
            sizeof(currentChannel));
  out.write(reinterpret_cast<const char *>(&lowByte), sizeof(lowByte));
}

void PIT::LoadState(std::istream &in) {
  in.read(reinterpret_cast<char *>(counters), sizeof(counters));
  in.read(reinterpret_cast<char *>(&controlWord), sizeof(controlWord));
  in.read(reinterpret_cast<char *>(&currentChannel), sizeof(currentChannel));
  in.read(reinterpret_cast<char *>(&lowByte), sizeof(lowByte));
}

void PIT::Tick() {
  // Decrement active counters
  for (int i = 0; i < 3; ++i) {
    if (counters[i] > 0) {
      counters[i]--;
      if (counters[i] == 0 && i == 0) {
        // Channel 0 generates timer interrupt
        if (!cpu.expired()) {
          if (auto cpuPtr = cpu.lock()) {
            cpuPtr->QueueInterrupt(0x20, 0); // Timer interrupt
          }
        }
      }
    }
  }
}

// DeviceManager Implementation
void DeviceManager::RegisterDevice(uint64_t base, uint64_t size,
                                   std::weak_ptr<x86_64::Device> device) {
  try {
    // Validate parameters
    if (size == 0) {
      spdlog::error(
          "DeviceManager: Cannot register device with zero size at 0x{:x}",
          base);
      throw std::invalid_argument("Device size cannot be zero");
    }

    if (base + size < base) {
      spdlog::error("DeviceManager: Address overflow for device at 0x{:x} with "
                    "size 0x{:x}",
                    base, size);
      throw std::invalid_argument("Device address range overflow");
    }

    // Check if device pointer is valid
    if (device.expired()) {
      spdlog::error("DeviceManager: Cannot register expired device at 0x{:x}",
                    base);
      throw std::invalid_argument("Device pointer is expired");
    }

    // Check for overlapping devices
    for (const auto &existing : devices) {
      uint64_t existing_end = existing.base + existing.size;
      uint64_t new_end = base + size;

      if ((base < existing_end && new_end > existing.base)) {
        spdlog::error("DeviceManager: Device overlap detected - new device "
                      "0x{:x}-0x{:x} overlaps with existing 0x{:x}-0x{:x}",
                      base, new_end - 1, existing.base, existing_end - 1);
        throw std::invalid_argument(
            "Device address range overlaps with existing device");
      }
    }

    DeviceMapping mapping;
    mapping.base = base;
    mapping.size = size;
    mapping.device = device;
    devices.push_back(mapping);

    spdlog::info("DeviceManager: Successfully registered device at "
                 "0x{:x}-0x{:x} (size: 0x{:x})",
                 base, base + size - 1, size);
  } catch (const std::exception &e) {
    spdlog::error("DeviceManager: Failed to register device at 0x{:x}: {}",
                  base, e.what());
    throw;
  }
}

std::weak_ptr<x86_64::Device> DeviceManager::FindDevice(uint64_t address) {
  try {
    for (const auto &mapping : devices) {
      // Validate mapping integrity
      if (mapping.size == 0) {
        spdlog::warn(
            "DeviceManager: Found device with zero size at 0x{:x}, skipping",
            mapping.base);
        continue;
      }

      if (address >= mapping.base && address < mapping.base + mapping.size) {
        if (mapping.device.expired()) {
          spdlog::warn("DeviceManager: Found expired device at 0x{:x} for "
                       "address 0x{:x}",
                       mapping.base, address);
          continue;
        }
        return mapping.device;
      }
    }
  } catch (const std::exception &e) {
    spdlog::error("DeviceManager: Error finding device for address 0x{:x}: {}",
                  address, e.what());
  }
  return std::weak_ptr<x86_64::Device>();
}

X86_64CPU::X86_64CPU(ps4::PS4Emulator &emulator, ps4::PS4MMU &mmu,
                     uint32_t cpuId)
    : m_emulator(emulator), mmu(mmu), m_cpuId(cpuId), registers{},
      xmmRegisters{}, rflags(DEFAULT_RFLAGS), running(false), halted(false),
      jit(std::make_unique<X86_64JITCompiler>(this)),
      decoder(std::make_unique<InstructionDecoder>()),
      jitCache(std::make_unique<jit::JitCache>()), jitTranslator(std::make_unique<jit::JitTranslator>()), pipeline(std::make_unique<Pipeline>(*this, mmu, *jitCache, *jitTranslator)), pic(nullptr), apic(nullptr),
      pit(nullptr), utilization(0.0f), tlb() { // Initialize TLB

  try {
    spdlog::info("X86_64CPU[{}]: Starting register initialization", m_cpuId);
    registers.fill(0);

#ifdef __AVX__
    xmmRegisters.fill(_mm256_setzero_si256());
#else
    for (auto &reg : xmmRegisters) {
      reg = _mm256_castsi128_si256(_mm_setzero_si128());
    }
#endif

    // AVX-512 registers (only if supported)
    spdlog::debug("X86_64CPU[{}]: Initializing SIMD registers", m_cpuId);
    if (g_cpuFeatures.avx512f) {
#ifdef __AVX512F__
      zmmRegisters.fill(_mm512_setzero_si512());
#else
      // Fallback: zero-initialize manually if AVX-512 intrinsics not available
      for (auto &reg : zmmRegisters) {
        memset(&reg, 0, sizeof(reg));
      }
#endif
      spdlog::debug("X86_64CPU[{}]: AVX-512 registers initialized", m_cpuId);
    } else {
      // Zero-initialize without using AVX-512 intrinsics
      for (auto &reg : zmmRegisters) {
        memset(&reg, 0, sizeof(reg));
      }
    }

    // Initialize mask registers (K0-K7)
    kRegisters.fill(0);
    kRegisters[0] = 0xFFFF; // K0 is always all 1s
    spdlog::debug("X86_64CPU[{}]: Mask registers initialized", m_cpuId);
    spdlog::debug(
        "X86_64CPU[{}]: AVX-512 not supported, registers zero-initialized",
        m_cpuId);

    // Create devices safely with individual error handling
    spdlog::info("X86_64CPU[{}]: Creating devices", m_cpuId);

    try {
      spdlog::debug("X86_64CPU[{}]: Creating PIC", m_cpuId);
      pic = std::make_shared<PIC>();
      if (!pic) {
        throw std::runtime_error("Failed to create PIC");
      }
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: PIC creation failed: {}", m_cpuId,
                    e.what());
      throw;
    }

    try {
      spdlog::debug("X86_64CPU[{}]: Creating PIT", m_cpuId);
      pit = std::make_shared<PIT>();
      if (!pit) {
        throw std::runtime_error("Failed to create PIT");
      }
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: PIT creation failed: {}", m_cpuId,
                    e.what());
      throw;
    }

    try {
      spdlog::debug("X86_64CPU[{}]: Creating APIC", m_cpuId);
      apic = std::make_shared<x86_64::APIC>(m_cpuId);
      if (!apic) {
        throw std::runtime_error("Failed to create APIC");
      }
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: APIC creation failed: {}", m_cpuId,
                    e.what());
      throw;
    }

    // Register devices with error handling
    spdlog::info("X86_64CPU[{}]: Registering devices", m_cpuId);

    spdlog::debug("X86_64CPU[{}]: Registering PIC at 0x20", m_cpuId);
    deviceManager.RegisterDevice(0x20, 2, pic); // PIC

    spdlog::debug("X86_64CPU[{}]: Registering PIT at 0x40", m_cpuId);
    deviceManager.RegisterDevice(0x40, 4, pit); // PIT

    spdlog::debug("X86_64CPU[{}]: Registering APIC at 0xFEE00000", m_cpuId);
    deviceManager.RegisterDevice(0xFEE00000, 0x1000, apic); // APIC

    spdlog::info("X86_64CPU[{}] created with {} XMM registers", m_cpuId,
                 XMM_REGISTER_COUNT);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}] constructor failed: {}", m_cpuId, e.what());
    throw;
  } catch (...) {
    spdlog::error("X86_64CPU[{}] constructor failed with unknown exception",
                  m_cpuId);
    throw;
  }
}

X86_64CPU::~X86_64CPU() {
  Shutdown();
  spdlog::info("X86_64CPU[{}] destroyed", m_cpuId);
}

bool X86_64CPU::Initialize() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  try {
    running = true;
    halted = false;
    tlb.Clear();
    // Clear and re-initialize interrupt queue
    while (!interruptQueue.empty()) {
      interruptQueue.pop();
    }

    // Ensure registers array is properly sized before filling
    if (registers.size() != GENERAL_REGISTER_COUNT) {
      spdlog::error("X86_64CPU[{}]: Register array size mismatch: {} != {}", 
                    m_cpuId, registers.size(), GENERAL_REGISTER_COUNT);
      return false;
    }
    
    // Reset all registers with bounds checking
    registers.fill(0);
    xmmRegisters.fill(_mm256_setzero_si256());

    // Reset AVX-512 registers conditionally
    if (g_cpuFeatures.avx512f) {
#ifdef __AVX512F__
      zmmRegisters.fill(_mm512_setzero_si512());
#else
      for (auto &reg : zmmRegisters) {
        memset(&reg, 0, sizeof(reg));
      }
#endif
    } else {
      for (auto &reg : zmmRegisters) {
        memset(&reg, 0, sizeof(reg));
      }
    }
    kRegisters.fill(0);
    segmentRegisters.fill(0);
    controlRegisters.fill(0);
    debugRegisters.fill(0);
    msrRegisters.clear();

    // Initialize default register values with bounds checking
    size_t rsp_idx = static_cast<size_t>(Register::RSP);
    size_t rip_idx = static_cast<size_t>(Register::RIP);
    
    if (rsp_idx < registers.size()) {
      registers[rsp_idx] = DEFAULT_STACK_POINTER;
    }
    if (rip_idx < registers.size()) {
      registers[rip_idx] = DEFAULT_ENTRY_POINT;
    }
    rflags = DEFAULT_RFLAGS;

    // Initialize segment registers with default values
    segmentRegisters[static_cast<size_t>(Register::CS) -
                     static_cast<size_t>(Register::ES)] =
        KERNEL_CS; // Code segment
    segmentRegisters[static_cast<size_t>(Register::DS) -
                     static_cast<size_t>(Register::ES)] =
        KERNEL_DS; // Data segment
    segmentRegisters[static_cast<size_t>(Register::ES) -
                     static_cast<size_t>(Register::ES)] = KERNEL_DS;
    segmentRegisters[static_cast<size_t>(Register::SS) -
                     static_cast<size_t>(Register::ES)] =
        KERNEL_DS; // Stack segment
    segmentRegisters[static_cast<size_t>(Register::FS) -
                     static_cast<size_t>(Register::ES)] =
        0; // FS and GS usually 0 initially
    segmentRegisters[static_cast<size_t>(Register::GS) -
                     static_cast<size_t>(Register::ES)] = 0;

    // Initialize control registers
    controlRegisters[0] = 0x80000011; // CR0: PE=1, PG=1, WP=1 (Protected
                                      // Mode, Paging, Write Protect)
    controlRegisters[3] = 0x0; // CR3: Page directory base (usually set by OS)
    controlRegisters[4] =
        0x000006F0; // CR4: Various extensions enabled (PAE, PGE, OSFXSR,
                    // OSXMMEXCPT, VMXE, SMXE, PCIDE, OSXSAVE, SMEP, SMAP)

    // Initialize essential MSRs
    msrRegisters[static_cast<uint32_t>(MSR::EFER)] =
        0x501; // LME=1, LMA=1, SCE=1 (Long Mode Enable, Long Mode Active,
               // SYSCALL Enable)
    msrRegisters[static_cast<uint32_t>(MSR::STAR)] =
        0x230008ULL << 32; // SYSCALL/SYSRET target CS/SS
    msrRegisters[static_cast<uint32_t>(MSR::LSTAR)] = 0; // SYSCALL target RIP
    msrRegisters[static_cast<uint32_t>(MSR::CSTAR)] =
        0; // Compat-mode SYSCALL target RIP
    msrRegisters[static_cast<uint32_t>(MSR::SFMASK)] = 0; // SYSCALL RFLAGS mask
    msrRegisters[static_cast<uint32_t>(MSR::FS_BASE)] = 0;
    msrRegisters[static_cast<uint32_t>(MSR::GS_BASE)] = 0;
    msrRegisters[static_cast<uint32_t>(MSR::KERNEL_GS_BASE)] = 0;
    msrRegisters[static_cast<uint32_t>(MSR::TSC)] = 0; // Time Stamp Counter
    msrRegisters[static_cast<uint32_t>(MSR::APIC_BASE)] =
        0xFEE00000 | (1ULL << 11); // APIC Base Address, APIC Global Enable

    // Initialize FPU state (FXSAVE/FXRSTOR format)
    fpuState = {};
    fpuState.controlWord = 0x037F; // Default FPU control word
    fpuState.statusWord = 0x0000;
    fpuState.tagWord = 0xFFFF; // All FPU registers empty
    fpuState.lastInstructionPointer = 0;
    fpuState.lastDataPointer = 0;
    fpuState.opcode = 0;
    fpuState.top = 0;      // Stack top pointer
    fpuState.tags.fill(3); // All registers empty (tag = 3)
    fpuState.st.fill(0.0); // Clear all stack registers

    // Initialize MMX state (part of FPU state)
    mmxState = {};
    mmxState.mmxMode = false; // Not strictly needed, but good for clarity

    // Initialize GDTR, IDTR, LDTR, TR
    gdtrBase = 0;
    gdtrLimit = 0;
    idtrBase = 0;
    idtrLimit = 0;
    ldtr = 0;
    tr = 0;
    tssBase = 0;
    tssLimit = 0;

    // Initialize process ID
    processId = 0;

    pipeline->ResetStats();
    utilization = 0.0f;

    spdlog::info(
        "X86_64CPU[{}] initialized with full register support. RIP=0x{:x}, registers.size()={}",
        m_cpuId, DEFAULT_ENTRY_POINT, registers.size());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}] initialization failed: {}", m_cpuId, e.what());
    running = false;
    return false;
  }
}

void X86_64CPU::Shutdown() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  running = false;
  halted = false;
  tlb.Clear();
  while (!interruptQueue.empty()) {
    interruptQueue.pop();
  }
  utilization = 0.0f;

  pipeline->Flush();
  spdlog::info("X86_64CPU[{}] shut down", m_cpuId);
}

void X86_64CPU::ResetState() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  utilization = 0.0f;
  while (!interruptQueue.empty()) {
    interruptQueue.pop();
  }
  halted = false;

  pipeline->Flush();

  // Reset all registers to zero
  registers.fill(0);
  xmmRegisters.fill(_mm256_setzero_si256());

  // Reset AVX-512 registers conditionally
  if (g_cpuFeatures.avx512f) {
#ifdef __AVX512F__
    zmmRegisters.fill(_mm512_setzero_si512());
#else
    for (auto &reg : zmmRegisters) {
      memset(&reg, 0, sizeof(reg));
    }
#endif
  } else {
    for (auto &reg : zmmRegisters) {
      memset(&reg, 0, sizeof(reg));
    }
  }
  kRegisters.fill(0);
  segmentRegisters.fill(0);
  controlRegisters.fill(0);
  debugRegisters.fill(0);
  msrRegisters.clear();

  // Set default values using constants
  registers[static_cast<size_t>(Register::RSP)] = DEFAULT_STACK_POINTER;
  registers[static_cast<size_t>(Register::RIP)] = DEFAULT_ENTRY_POINT;
  rflags = DEFAULT_RFLAGS;

  // Reset segment registers
  segmentRegisters[static_cast<size_t>(Register::CS) -
                   static_cast<size_t>(Register::ES)] = KERNEL_CS;
  segmentRegisters[static_cast<size_t>(Register::DS) -
                   static_cast<size_t>(Register::ES)] = KERNEL_DS;
  segmentRegisters[static_cast<size_t>(Register::ES) -
                   static_cast<size_t>(Register::ES)] = KERNEL_DS;
  segmentRegisters[static_cast<size_t>(Register::SS) -
                   static_cast<size_t>(Register::ES)] = KERNEL_DS;

  // Reset control registers
  controlRegisters[0] = 0x80000011;
  controlRegisters[3] = 0x0;
  controlRegisters[4] = 0x000006F0;

  // Reset MSRs
  msrRegisters[static_cast<uint32_t>(MSR::EFER)] = 0x501;
  msrRegisters[static_cast<uint32_t>(MSR::STAR)] = 0x230008ULL << 32;
  msrRegisters[static_cast<uint32_t>(MSR::LSTAR)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::CSTAR)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::SFMASK)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::FS_BASE)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::GS_BASE)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::KERNEL_GS_BASE)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::TSC)] = 0;
  msrRegisters[static_cast<uint32_t>(MSR::APIC_BASE)] =
      0xFEE00000 | (1ULL << 11);

  // Reset FPU state
  fpuState = {};
  fpuState.controlWord = 0x037F;
  fpuState.tagWord = 0xFFFF;
  fpuState.top = 0;
  fpuState.tags.fill(3);
  fpuState.st.fill(0.0);

  // Reset GDTR, IDTR, LDTR, TR
  gdtrBase = 0;
  gdtrLimit = 0;
  idtrBase = 0;
  idtrLimit = 0;
  ldtr = 0;
  tr = 0;
  tssBase = 0;
  tssLimit = 0;

  processId = 0;

  spdlog::info("X86_64CPU[{}] state reset to defaults: RIP=0x{:x}", m_cpuId,
               DEFAULT_ENTRY_POINT);
}

void X86_64CPU::QueueInterrupt(uint8_t vector, uint8_t priority) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  interruptQueue.push({vector, priority});
  spdlog::debug("X86_64CPU[{}]: Queued interrupt vector=0x{:x}, priority={}",
                m_cpuId, vector, priority);
  halted = false; // Wake up from HLT
}

void X86_64CPU::TriggerInterrupt(uint8_t vector, uint64_t errorCode,
                                 bool isSoftwareInterrupt) {
  // Release CPU mutex before calling InterruptHandler to prevent deadlock
  // The InterruptHandler will call back into CPU methods, so we must not hold
  // our mutex
  std::unique_lock<std::recursive_timed_mutex> lock(mutex, std::defer_lock);
  if (lock.owns_lock()) { // Only unlock if we already own it
    lock.unlock();
  }

  InterruptHandler &handler = m_emulator.GetInterruptHandler();
  try {
    handler.HandleInterrupt(vector, errorCode, isSoftwareInterrupt);
    CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: Triggered interrupt vector=0x{:x}", m_cpuId,
                  vector);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Failed to handle interrupt vector=0x{:x}: {}",
                  m_cpuId, vector, e.what());
    // If handling fails, re-queue the interrupt with exponential backoff
    // Re-acquire lock to modify internal state
    if (lock.try_lock_for(std::chrono::milliseconds(10))) {
      interruptQueue.push({vector, 0}); // Re-queue with low priority
      // Don't halt immediately - allow retry
    }
  }
}

void X86_64CPU::DeviceTick() {
  pit->Tick();
  apic->UpdateTimer(1); // Update timer with 1 cycle
}

void X86_64CPU::ExecuteCycle() {
  // Optimized mutex acquisition with reduced contention
  auto lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
      mutex, ps4::LockLevel::CPU, "CPUMutex", std::try_to_lock);
  if (!lock->owns_lock()) {
    // Exponential backoff based on CPU ID to reduce thundering herd
    auto timeout = std::chrono::microseconds(100 + (m_cpuId * 50));
    if (!mutex.try_lock_for(timeout)) {
      CPU_DIAG_VECTOR_ACCESS(
          "X86_64CPU[{}]: Mutex contention - backing off to reduce contention",
          m_cpuId);

      // Staggered backoff to prevent all cores from retrying simultaneously
      auto backoff_us = 50 + (m_cpuId * 25);
      std::this_thread::sleep_for(std::chrono::microseconds(backoff_us));
      return;
    }
    lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
        mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
  }

  if (!running) {
    spdlog::debug("X86_64CPU[{}]: Not running, skipping cycle", m_cpuId);
    return;
  }

  DeviceTick();
  CheckMWAITWakeup();

  if (halted) {
    // If halted, only process interrupts
    if (!interruptQueue.empty() && GetFlag(FLAG_IF)) {
      auto irq = interruptQueue.top();
      interruptQueue.pop();
      CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: Waking from HLT to handle interrupt 0x{:x}",
                    m_cpuId, irq.vector);
      halted = false; // Exit HLT state
      // Release lock before triggering interrupt to prevent deadlock
      lock->unlock();
      TriggerInterrupt(irq.vector, 0, false);
      return; // Cycle ends after handling interrupt
    } else {
      CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: Halted, waiting for interrupt", m_cpuId);
      // Adaptive sleep based on CPU ID to reduce synchronization
      auto sleep_time = std::chrono::microseconds(50 + (m_cpuId * 10));
      std::this_thread::sleep_for(sleep_time);
      return;
    }
  }

  auto start = std::chrono::steady_clock::now();
  try {
    // Handle pending interrupts if enabled
    if (GetFlag(FLAG_IF) && !interruptQueue.empty()) {
      auto irq = interruptQueue.top();
      interruptQueue.pop();

      uint64_t currentRip = _getRegister(Register::RIP);

      // Release our mutex before calling TriggerInterrupt to prevent deadlock
      lock->unlock();

      if (currentRip == 0 || currentRip < 0x1000) {
        CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: Invalid RIP 0x{:x} before interrupt - "
                         "preventing execution",
                         m_cpuId, currentRip);
        // Re-acquire lock to set running to false
        auto timeout = std::chrono::milliseconds(10);
        if (mutex.try_lock_for(timeout)) {
          lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
              mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
          running = false;
        } else {
          CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: Could not acquire mutex to stop CPU", m_cpuId);
        }
        return;
      }

      TriggerInterrupt(irq.vector, 0, false);

      // Reacquire lock for UpdateUtilization
      if (!mutex.try_lock()) {
        CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: Could not reacquire mutex after interrupt",
                     m_cpuId);
        return;
      }
      lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
          mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
      UpdateUtilization(start);
      return;
    }

    uint64_t rip;
    {
      // Ensure thread-safe access to RIP register
      std::unique_lock<std::recursive_timed_mutex> reg_lock(mutex, std::defer_lock);
      if (!reg_lock.try_lock_for(std::chrono::microseconds(100))) {
        CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: Could not acquire register lock for RIP read", m_cpuId);
        return;
      }
      rip = _getRegister(Register::RIP);
    }

    // Enhanced RIP initialization with per-core distribution and validation
    if (rip == 0 || rip == DEFAULT_ENTRY_POINT) {
      uint64_t baseEntry = DEFAULT_ENTRY_POINT;
      uint64_t coreOffset = m_cpuId * 0x1000; // 4KB per core
      uint64_t newRip = baseEntry + coreOffset;

      // Validate the new RIP is in a reasonable range
      if (newRip < 0x1000 || newRip >= 0x800000000000ULL) {
        CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: Invalid calculated RIP 0x{:x}, using default", m_cpuId, newRip);
        newRip = DEFAULT_ENTRY_POINT;
      }

      spdlog::info("X86_64CPU[{}]: Setting distributed entry point: 0x{:x} "
                   "(base + 0x{:x})",
                   m_cpuId, newRip, coreOffset);
      _setRegister(Register::RIP, newRip);
      rip = newRip;

      // Initialize some basic code (e.g., NOP) at this location
      try {
        lock->unlock();                // Release lock for MMU access
        uint8_t nopInstruction = 0x90; // NOP instruction
        
        // Try to write the NOP instruction, handle page faults gracefully
        if (!mmu.WriteVirtual(rip, &nopInstruction, 1, processId)) {
          CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: Failed to write NOP instruction at 0x{:x}, page may not be mapped", m_cpuId, rip);
        }
        
        if (!mutex.try_lock_for(std::chrono::milliseconds(5))) {
          CPU_DIAG_VECTOR_ACCESS(
              "X86_64CPU[{}]: Could not reacquire lock after RIP setup",
              m_cpuId);
          return;
        }
        lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
            mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: Failed to initialize entry point: {}",
                      m_cpuId, e.what());
        running = false;
        return;
      }
    }

    // CRITICAL FIX: Validate RIP before any execution with enhanced recovery
    if (rip < 0x1000 || rip >= 0x800000000000ULL) {
      CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: CRITICAL - Invalid RIP 0x{:x} in "
                       "ExecuteCycle, attempting recovery",
                       m_cpuId, rip);

      // Try to set a safe default RIP with per-core offset
      uint64_t recoveryRip = DEFAULT_ENTRY_POINT + (m_cpuId * 0x1000);
      if (recoveryRip >= 0x800000000000ULL) {
        recoveryRip = DEFAULT_ENTRY_POINT; // Fallback to base entry point
      }
      
      _setRegister(Register::RIP, recoveryRip);
      rip = recoveryRip;

      if (rip < 0x1000) { // If still invalid, stop the CPU
        running = false;
        spdlog::error(
            "X86_64CPU[{}]: Cannot recover from invalid RIP, stopping CPU",
            m_cpuId);
        return;
      }
    }

    // Release lock before JIT/pipeline execution to prevent deadlocks
    lock->unlock();

    bool jitSuccess = false;
    try {
      jitSuccess = jit->ExecuteCompiledCode(rip);
    } catch (const std::exception &e) {
      CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: JIT execution exception at 0x{:x}: {}",
                    m_cpuId, rip, e.what());
      jitSuccess = false;
    }

    if (jitSuccess) {
      if (mutex.try_lock_for(std::chrono::microseconds(100))) {
        lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
            mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
        UpdateUtilization(start);
      }
      return;
    }

    CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: JIT failed at 0x{:x}, using pipeline",
                  m_cpuId, rip);

    try {
      pipeline->Step(); // Pipeline handles all instruction execution
    } catch (const std::exception &e) {
      CPU_DIAG_VECTOR_ACCESS("X86_64CPU[{}]: Pipeline execution exception at 0x{:x}: {}",
                    m_cpuId, rip, e.what());
      // Advance RIP to prevent infinite loop on bad instruction
      if (mutex.try_lock_for(std::chrono::microseconds(100))) {
        lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
            mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
        uint64_t currentRip = _getRegister(Register::RIP);
        _setRegister(Register::RIP, currentRip + 1); // Skip bad instruction
      }
      // Trigger a general protection fault
      TriggerInterrupt(EXC_GP, 0, false);
    }

    if (mutex.try_lock_for(std::chrono::microseconds(100))) {
      lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
          mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
      UpdateUtilization(start);
    }
  } catch (const CPUException &e) {
    // Ensure we have the lock for accessing GetRegister and running
    if (!lock->owns_lock() && mutex.try_lock()) {
      lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
          mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
    }

    if (lock->owns_lock()) {
      uint64_t errorRip = _getRegister(Register::RIP);
      spdlog::critical("X86_64CPU[{}]: Exception at RIP=0x{:x}: {}", m_cpuId,
                       errorRip, e.what());
      running = false;
    } else {
      spdlog::critical("X86_64CPU[{}]: Exception (could not acquire lock): {}",
                       m_cpuId, e.what());
    }
    throw;
  } catch (const std::exception &e) {
    // Catch any other unexpected exceptions
    if (!lock->owns_lock() && mutex.try_lock()) {
      lock = std::make_unique<ps4::OrderedTimedLockGuard<decltype(mutex)>>(
          mutex, ps4::LockLevel::CPU, "CPUMutex", std::adopt_lock);
    }
    if (lock->owns_lock()) {
      uint64_t errorRip = _getRegister(Register::RIP);
      spdlog::critical("X86_64CPU[{}]: Unhandled exception at RIP=0x{:x}: {}",
                       m_cpuId, errorRip, e.what());
      running = false;
    } else {
      spdlog::critical(
          "X86_64CPU[{}]: Unhandled exception (could not acquire lock): {}",
          m_cpuId, e.what());
    }
    TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
  }
}

void X86_64CPU::UpdateUtilization(
    const std::chrono::steady_clock::time_point &start) {
  auto end = std::chrono::steady_clock::now();
  auto duration =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  // Accumulate utilization over time, capping at 1.0 (100%)
  utilization = (std::min)(1.0f, utilization + (float)duration / 1000000.0f);
  spdlog::trace("X86_64CPU[{}] utilization updated: {:.2f}%", m_cpuId,
                utilization * 100.0f);
}

bool X86_64CPU::CalculateParity(uint64_t value) {
  uint8_t byte = value & 0xFF;
  int count = 0;
  for (int i = 0; i < 8; ++i) {
    if ((byte >> i) & 1)
      count++;
  }
  return (count % 2 == 0);
}

void X86_64CPU::UpdateArithmeticFlags(uint64_t op1, uint64_t op2,
                                      uint64_t result, uint8_t sizeInBits,
                                      bool isSubtract) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  if (sizeInBits == 0 || sizeInBits > 64) {
    spdlog::error("X86_64CPU[{}]: Invalid size for flag calculation: {} bits",
                  m_cpuId, sizeInBits);
    return;
  }

  uint64_t mask =
      (sizeInBits == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << sizeInBits) - 1;
  uint64_t signBit = 1ULL << (sizeInBits - 1);

  result &= mask;
  op1 &= mask;
  op2 &= mask;

  // Clear only arithmetic flags, preserve other flags
  rflags &= ~ARITHMETIC_FLAGS_MASK;

  // Zero flag
  if (result == 0)
    rflags |= FLAG_ZF;

  // Sign flag
  if (result & signBit)
    rflags |= FLAG_SF;

  // Parity flag
  if (CalculateParity(result))
    rflags |= FLAG_PF;

  // Carry and Auxiliary Carry flags
  if (isSubtract) {
    // CF is set if a borrow is generated (op1 < op2)
    if (op1 < op2)
      rflags |= FLAG_CF;
    // AF is set if a borrow is generated from bit 4
    if ((op1 & 0xF) < (op2 & 0xF))
      rflags |= FLAG_AF;
  } else { // Addition
    // CF is set if a carry is generated (result overflows mask)
    uint64_t fullResult = op1 + op2;
    if (fullResult > mask)
      rflags |= FLAG_CF;
    // AF is set if a carry is generated from bit 3 to bit 4
    if (((op1 & 0xF) + (op2 & 0xF)) > 0xF)
      rflags |= FLAG_AF;
  }

  // Overflow flag calculation
  bool s1 = (op1 & signBit) != 0;
  bool s2 = (op2 & signBit) != 0;
  bool sr = (result & signBit) != 0;

  if (isSubtract) {
    // Overflow occurs if (positive - negative = negative) or (negative -
    // positive = positive)
    if ((s1 && !s2 && !sr) || (!s1 && s2 && sr))
      rflags |= FLAG_OF;
  } else { // Addition
    // Overflow occurs if (positive + positive = negative) or (negative +
    // negative = positive)
    if (s1 == s2 && s1 != sr)
      rflags |= FLAG_OF;
  }

  spdlog::trace("X86_64CPU[{}] flags updated: RFLAGS=0x{:x}", m_cpuId, rflags);
}

uint64_t
X86_64CPU::ReadOperandValue(const DecodedInstruction::Operand &operand) {
  uint8_t sizeInBits = operand.size;

  switch (operand.type) {
  case DecodedInstruction::Operand::Type::REGISTER:
    return GetRegister(operand.reg) & ((1ULL << sizeInBits) - 1);
  case DecodedInstruction::Operand::Type::IMMEDIATE:
    // Sign-extend immediate values based on their size
    switch (sizeInBits) {
    case 8:
      return static_cast<uint64_t>(static_cast<int8_t>(operand.immediate));
    case 16:
      return static_cast<uint64_t>(static_cast<int16_t>(operand.immediate));
    case 32:
      return static_cast<uint64_t>(static_cast<int32_t>(operand.immediate));
    case 64:
      return operand.immediate;
    default:
      spdlog::error("X86_64CPU[{}]: Invalid immediate size: {} bits", m_cpuId,
                    sizeInBits);
      throw CPUException("Invalid immediate size");
    }
  case DecodedInstruction::Operand::Type::MEMORY: {
    uint64_t addr = CalculateMemoryAddress(operand);
    size_t bytesToRead = sizeInBits / 8;
    uint64_t value = 0;
    try {
      mmu.ReadVirtual(addr, &value, bytesToRead, GetProcessId());
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: Memory read failed at 0x{:x}: {}", m_cpuId,
                    addr, e.what());
      TriggerInterrupt(EXC_PF, addr, false); // Page Fault
      throw CPUException("Memory read error");
    }
    spdlog::trace("Read memory at 0x{:x}, value=0x{:x}, size={}", addr, value,
                  bytesToRead);
    return value & ((1ULL << sizeInBits) - 1);
  }
  case DecodedInstruction::Operand::Type::XMM: {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    __m256i reg = xmmRegisters[static_cast<size_t>(operand.reg) -
                               static_cast<size_t>(Register::XMM0)];
    alignas(32) uint64_t tmp[4];
    _mm256_store_si256((__m256i *)tmp, reg);

    uint64_t result = 0;
    switch (sizeInBits) {
    case 32: // Single 32-bit value (e.g., scalar float)
      result = tmp[0] & 0xFFFFFFFF;
      break;
    case 64: // Single 64-bit value (e.g., scalar double)
      result = tmp[0];
      break;
    case 128: // Full XMM register (128-bit) - return lower 64 bits with
              // warning
      spdlog::warn(
          "ReadOperandValue called for 128-bit XMM operation at XMM{} - "
          "returning lower 64 bits. Use ReadXmmOperandValue() for full SIMD "
          "data.",
          static_cast<int>(operand.reg));
      result = tmp[0];
      break;
    case 256: // Full YMM register (256-bit) - return lower 64 bits with
              // warning
      spdlog::warn(
          "ReadOperandValue called for 256-bit YMM operation at XMM{} - "
          "returning lower 64 bits. Use ReadXmmOperandValue() for full SIMD "
          "data.",
          static_cast<int>(operand.reg));
      result = tmp[0];
      break;
    default:
      result = tmp[0] & ((1ULL << sizeInBits) - 1);
      break;
    }

    spdlog::trace("Read XMM{}: value=0x{:x}, size={} bits",
                  static_cast<int>(operand.reg), result, sizeInBits);
    return result;
  }
  default:
    spdlog::error("X86_64CPU[{}]: Invalid operand type: {}", m_cpuId,
                  static_cast<int>(operand.type));
    throw CPUException("Invalid operand type");
  }
}

void X86_64CPU::WriteOperandValue(const DecodedInstruction::Operand &operand,
                                  uint64_t value) {
  uint8_t sizeInBits = operand.size;

  switch (operand.type) {
  case DecodedInstruction::Operand::Type::REGISTER:
    SetRegister(operand.reg, value & ((1ULL << sizeInBits) - 1));
    spdlog::trace("Write register {}: value=0x{:x}",
                  static_cast<int>(operand.reg), value);
    break;
  case DecodedInstruction::Operand::Type::MEMORY:
    WriteMemoryOperand(operand, value);
    break;
  case DecodedInstruction::Operand::Type::XMM: {
    __m256i newValue;
    switch (sizeInBits) {
    case 32:
      newValue =
          _mm256_set_epi32(0, 0, 0, 0, 0, 0, 0, static_cast<int32_t>(value));
      break;
    case 64:
      newValue = _mm256_set_epi64x(0, 0, 0, static_cast<int64_t>(value));
      break;
    case 128:
      spdlog::warn("WriteOperandValue called for 128-bit XMM operation - use "
                   "WriteXmmOperandValue instead. Setting lower 64 bits.");
      newValue = _mm256_set_epi64x(0, 0, 0, static_cast<int64_t>(value));
      break;
    case 256:
      spdlog::warn("WriteOperandValue called for 256-bit YMM operation - use "
                   "WriteXmmOperandValue instead. Broadcasting value.");
      newValue = _mm256_set1_epi64x(static_cast<int64_t>(value));
      break;
    default:
      newValue = _mm256_set_epi64x(
          0, 0, 0, static_cast<int64_t>(value & ((1ULL << sizeInBits) - 1)));
      break;
    }
    WriteXmmOperand(operand, newValue);
    spdlog::trace("Write XMM{}: value=0x{:x}, size={} bits",
                  static_cast<int>(operand.reg), value, sizeInBits);
    break;
  }
  default:
    spdlog::error("X86_64CPU[{}]: Invalid operand type for write: {}", m_cpuId,
                  static_cast<int>(operand.type));
    throw CPUException("Write to invalid operand type");
  }
}

uint64_t X86_64CPU::CalculateMemoryAddress(
    const DecodedInstruction::Operand &operand) const {
  if (operand.type != DecodedInstruction::Operand::Type::MEMORY) {
    spdlog::error("X86_64CPU[{}]: Invalid operand type for memory address "
                  "calculation: {}",
                  m_cpuId, static_cast<int>(operand.type));
    throw CPUException("Invalid operand type for memory address");
  }

  std::unique_lock<std::recursive_timed_mutex> lock(
      mutex); // Lock for register access
  uint64_t addr = 0;
  if (operand.memory.base == Register::RIP) {
    // RIP-relative addressing: RIP + displacement + instruction_length
    addr = _getRegister(Register::RIP) + operand.memory.displacement +
           operand.instructionLength;
  } else if (operand.memory.base != Register::NONE) {
    addr = _getRegister(operand.memory.base);
  }
  if (operand.memory.index != Register::NONE) {
    addr += _getRegister(operand.memory.index) * operand.memory.scale;
  }
  addr += static_cast<int64_t>(
      operand.memory.displacement); // Displacement is signed

  spdlog::trace("Calculated memory address: 0x{:x}", addr);
  return addr;
}

void X86_64CPU::WriteMemoryOperand(const DecodedInstruction::Operand &operand,
                                   uint64_t value) {
  uint8_t sizeInBits = operand.size;
  uint64_t addr = CalculateMemoryAddress(operand);
  size_t bytesToWrite = sizeInBits / 8;
  try {
    mmu.WriteVirtual(addr, &value, bytesToWrite, GetProcessId());
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Memory write failed at 0x{:x}: {}", m_cpuId,
                  addr, e.what());
    TriggerInterrupt(EXC_PF, addr, false); // Page Fault
    throw CPUException("Memory write error");
  }
  spdlog::trace("Write memory at 0x{:x}, value=0x{:x}, size={}", addr, value,
                bytesToWrite);
}

void X86_64CPU::WriteXmmOperand(const DecodedInstruction::Operand &operand,
                                const __m256i &value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  xmmRegisters[static_cast<size_t>(operand.reg) -
               static_cast<size_t>(Register::XMM0)] = value;
  spdlog::trace("Write XMM{}: value set", static_cast<int>(operand.reg));
}

__m256i
X86_64CPU::ReadXmmOperandValue(const DecodedInstruction::Operand &operand) {
  if (operand.type != DecodedInstruction::Operand::Type::XMM) {
    spdlog::error("X86_64CPU[{}]: Invalid operand type for XMM read: {}",
                  m_cpuId, static_cast<int>(operand.type));
    throw CPUException("Invalid operand type for XMM read");
  }

  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  __m256i reg = xmmRegisters[static_cast<size_t>(operand.reg) -
                             static_cast<size_t>(Register::XMM0)];

  uint8_t sizeInBits = operand.size;
  switch (sizeInBits) {
  case 128: // XMM register (128-bit)
    // Zero upper 128 bits, keep lower 128 bits
    return _mm256_inserti128_si256(_mm256_setzero_si256(),
                                   _mm256_extracti128_si256(reg, 0), 0);
  case 256: // YMM register (256-bit)
    return reg;
  default:
    // For smaller sizes, return the full register and let caller handle
    // extraction
    spdlog::warn("ReadXmmOperandValue called for size {} bits, returning full "
                 "256-bit register. Caller should mask.",
                 sizeInBits);
    return reg;
  }
}

void X86_64CPU::WriteXmmOperandValue(const DecodedInstruction::Operand &operand,
                                     const __m256i &value) {
  if (operand.type != DecodedInstruction::Operand::Type::XMM) {
    spdlog::error("X86_64CPU[{}]: Invalid operand type for XMM write: {}",
                  m_cpuId, static_cast<int>(operand.type));
    throw CPUException("Invalid operand type for XMM write");
  }

  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  size_t regIndex =
      static_cast<size_t>(operand.reg) - static_cast<size_t>(Register::XMM0);

  uint8_t sizeInBits = operand.size;
  switch (sizeInBits) {
  case 128: // XMM register (128-bit)
    // Preserve upper 128 bits, set lower 128 bits
    xmmRegisters[regIndex] = _mm256_inserti128_si256(
        xmmRegisters[regIndex],                 // Keep the full register
        _mm256_extracti128_si256(value, 0), 0); // Set lower 128 bits
    break;
  case 256: // YMM register (256-bit)
    xmmRegisters[regIndex] = value;
    break;
  default:
    // For other sizes, preserve upper bits and modify only the relevant
    // portion This is a simplification; proper handling would involve
    // _mm256_insert_epiXX
    spdlog::warn("WriteXmmOperandValue called for size {} bits, writing full "
                 "256-bit register. This might be incorrect.",
                 sizeInBits);
    xmmRegisters[regIndex] = value;
    break;
  }

  spdlog::trace("Write XMM{}: full register updated, size={} bits",
                static_cast<int>(operand.reg), sizeInBits);
}

void X86_64CPU::FetchDecodeExecute() {
  uint64_t rip = GetRegister(Register::RIP);
  DecodedInstruction instr;

  try {
    // Comprehensive RIP validation
    if (rip == 0 || rip == 0xDEADBEEF || rip == 0xCCCCCCCC ||
        rip == 0xFEEEFEEE || rip < 0x1000 || rip >= 0x800000000000ULL) {
      spdlog::critical("X86_64CPU[{}]: CRITICAL - Invalid RIP detected: 0x{:x}",
                       m_cpuId, rip);
      spdlog::critical("X86_64CPU[{}]: This indicates severe memory corruption "
                       "or deadlock. Attempting recovery.",
                       m_cpuId);

      // Emergency RIP recovery - try to set to a safe default or previous
      // known good value
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      if (registers[static_cast<size_t>(Register::RSP)] != 0) {
        try {
          uint64_t stackTop = registers[static_cast<size_t>(Register::RSP)];
          if (stackTop > 0x1000 && stackTop < 0x800000000000ULL) {
            lock.unlock(); // Release lock for MMU access
            uint64_t returnAddr = 0;
            if (mmu.ReadVirtual(stackTop, &returnAddr, 8, processId) &&
                returnAddr > 0x1000 && returnAddr < 0x800000000000ULL) {
              lock.lock(); // Re-acquire lock
              _setRegister(Register::RIP, returnAddr);
              _setRegister(Register::RSP, stackTop + 8);
              spdlog::warn("X86_64CPU[{}]: Emergency RIP recovery to 0x{:x} "
                           "from stack",
                           m_cpuId, returnAddr);
              return; // Successfully recovered, try again next cycle
            }
          }
        } catch (...) {
          // Recovery failed, continue with error handling
        }
      }

      // If recovery fails, stop execution to prevent cascade
      running = false;
      lock.unlock();
      TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
      return;
    }

    std::vector<uint8_t> instBuffer(INSTRUCTION_BUFFER_SIZE);
    try {
      if (!mmu.ReadVirtual(rip, instBuffer.data(), instBuffer.size(),
                           GetProcessId())) {
        spdlog::error("X86_64CPU[{}]: Memory read failed at RIP=0x{:x}",
                      m_cpuId, rip);
        TriggerInterrupt(EXC_PF, rip, false); // Page Fault
        return;
      }
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: Exception reading memory at RIP=0x{:x}: {}",
                    m_cpuId, rip, e.what());
      TriggerInterrupt(EXC_PF, rip, false); // Page Fault
      return;
    }

    // Decode the instruction
    auto decodeInfo =
        decoder->Decode(rip, instBuffer.data(), instBuffer.size(), instr);

    if (decodeInfo.error != DecoderError::Success ||
        instr.instType == InstructionType::Unknown || instr.length == 0 ||
        instr.length > MAX_INSTRUCTION_LENGTH) {
      spdlog::warn("X86_64CPU[{}]: Decode failed at 0x{:x}: error={}, type={}, "
                   "length={}",
                   m_cpuId, rip, static_cast<int>(decodeInfo.error),
                   static_cast<int>(instr.instType), instr.length);
      TriggerInterrupt(EXC_UD, 0, false); // Undefined Opcode
      return;
    }

    if (!instr.validate()) {
      spdlog::error("X86_64CPU[{}]: Invalid instruction structure at 0x{:x}",
                    m_cpuId, rip);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    uint64_t nextRip = rip + instr.length;
    spdlog::trace("X86_64CPU[{}]: Interpreting at 0x{:x}: type={}, length={}",
                  m_cpuId, rip, static_cast<int>(instr.instType), instr.length);

    // Handle REP prefixes for string operations
    bool repActive = (instr.instType >= InstructionType::Movsb &&
                      instr.instType <= InstructionType::Cmpsq) &&
                     (instr.repPrefix || instr.repePrefix || instr.repnePrefix);

    if (repActive) {
      ExecuteStringOperation(instr, nextRip);
    } else {
      // Delegate instruction execution to the pipeline's InterpretInstruction
      // method Create a minimal ExecuteStage for the pipeline to process
      ExecuteStage exec;
      exec.instr = instr;
      exec.pc = rip;
      exec.valid = true;
      exec.predicted_taken = false;
      exec.predicted_target = 0;
      exec.execute_cycle = 0; // Not used in direct execution
      exec.fetch_cycle = 0;   // Not used in direct execution
      exec.prediction_confidence = 0.0f;
      exec.unit_type = ExecutionUnitType::INTEGER; // Default unit (was ALU)
      exec.execution_latency = 1;              // Default latency

      try {
        pipeline->InterpretInstruction(exec);
        spdlog::trace("X86_64CPU[{}]: Delegated instruction execution to "
                      "pipeline at 0x{:x}",
                      m_cpuId, rip);
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: Pipeline instruction execution failed at "
                      "0x{:x}: {}",
                      m_cpuId, rip, e.what());
        TriggerInterrupt(EXC_UD, 0, false); // Undefined Opcode
        return;
      }
    }

    // Update RIP to next instruction
    SetRegister(Register::RIP, nextRip);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: FetchDecodeExecute failed at 0x{:x}: {}",
                  m_cpuId, rip, e.what());
    TriggerInterrupt(EXC_GP, 0, false);
  } catch (...) {
    spdlog::error(
        "X86_64CPU[{}]: Unknown exception in FetchDecodeExecute at 0x{:x}",
        m_cpuId, rip);
    TriggerInterrupt(EXC_GP, 0, false);
  }
}

uint64_t X86_64CPU::TranslateAddress(uint64_t virtualAddr) {
  // Check CPL for privilege checks on memory access
  uint8_t cpl = GetCPL();

  size_t hitFlag, useCache;
  {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    hitFlag = 0;
    useCache = 0;
    uint64_t physAddr = tlb.Lookup(virtualAddr, hitFlag, useCache);
    if (physAddr != 0) {
      spdlog::trace("TLB hit for virtual 0x{:x}, physical 0x{:x}", virtualAddr,
                    physAddr);
      return physAddr;
    }
  }

  // Paging is enabled if CR0.PG (bit 31) is set
  if (!(GetCR0() & (1ULL << 31))) {
    spdlog::trace("Paging disabled, virtual address 0x{:x} is physical",
                  virtualAddr);
    return virtualAddr; // Identity mapping if paging is off
  }

  uint64_t cr3_val = GetCR3(); // CR3 holds the base address of the PML4 table
  uint64_t pml4_base = cr3_val & ~0xFFF; // CR3 bits 11:0 are reserved/ignored

  // Extract page table indices
  uint64_t pml4_idx = (virtualAddr >> 39) & 0x1FF;
  uint64_t pdpt_idx = (virtualAddr >> 30) & 0x1FF;
  uint64_t pd_idx = (virtualAddr >> 21) & 0x1FF;
  uint64_t pt_idx = (virtualAddr >> 12) & 0x1FF;
  uint64_t offset = virtualAddr & 0xFFF;

  uint64_t pml4e_addr = pml4_base + pml4_idx * 8;
  uint64_t pml4e = 0;
  try {
    mmu.ReadPhysical(pml4e_addr, &pml4e, 8);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Page table walk failed: PML4E read error "
                  "at 0x{:x}: {}",
                  m_cpuId, pml4e_addr, e.what());
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table walk error");
  }

  if (!(pml4e & 1)) { // Present bit (bit 0)
    spdlog::warn(
        "Page entry not present: 0x{:x}", virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    return 0; // Return 0 instead of throwing to prevent cascade failures
  }
  // Check R/W (bit 1) and U/S (bit 2) permissions
  if (!CheckPagePermissions(pml4e, cpl, true,
                            true)) { // Assume read/write for now
    spdlog::error("Page table fault: PML4E permission violation at 0x{:x} "
                  "(virtual 0x{:x})",
                  pml4e_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table fault: PML4E permission violation");
  }

  uint64_t pdpt_base = pml4e & ~0xFFF;
  uint64_t pdpte_addr = pdpt_base + pdpt_idx * 8;
  uint64_t pdpte = 0;
  try {
    mmu.ReadPhysical(pdpte_addr, &pdpte, 8);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Page table walk failed: PDPTE read error "
                  "at 0x{:x}: {}",
                  m_cpuId, pdpte_addr, e.what());
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table walk error");
  }

  if (!(pdpte & 1)) { // Present bit
    spdlog::warn(
        "Page entry not present: 0x{:x}", virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    return 0; // Return 0 instead of throwing to prevent cascade failures
  }
  if (!CheckPagePermissions(pdpte, cpl, true, true)) {
    spdlog::error("Page table fault: PDPTE permission violation at 0x{:x} "
                  "(virtual 0x{:x})",
                  pdpte_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false);
    throw CPUException("Page table fault: PDPTE permission violation");
  }

  // Check for 1GB page (bit 7 of PDPTE)
  if (pdpte & (1ULL << 7)) { // PS bit (Page Size)
    uint64_t physAddr = (pdpte & ~0x3FFFFFULL) |
                        (virtualAddr & 0x3FFFFFFF); // Mask for 1GB page
    spdlog::trace("Translated virtual 0x{:x} to physical 0x{:x} (1GB page)",
                  virtualAddr, physAddr);
    {
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      tlb.Insert(virtualAddr, physAddr);
    }
    return physAddr;
  }

  uint64_t pd_base = pdpte & ~0xFFF;
  uint64_t pde_addr = pd_base + pd_idx * 8;
  uint64_t pde = 0;
  try {
    mmu.ReadPhysical(pde_addr, &pde, 8);
  } catch (const std::exception &e) {
    spdlog::error(
        "X86_64CPU[{}]: Page table walk failed: PDE read error at 0x{:x}: {}",
        m_cpuId, pde_addr, e.what());
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table walk error");
  }

  if (!(pde & 1)) { // Present bit
    spdlog::warn(
        "Page entry not present: 0x{:x}", virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    return 0; // Return 0 instead of throwing to prevent cascade failures
  }
  if (!CheckPagePermissions(pde, cpl, true, true)) {
    spdlog::error("Page table fault: PDE permission violation at 0x{:x} "
                  "(virtual 0x{:x})",
                  pde_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false);
    throw CPUException("Page table fault: PDE permission violation");
  }

  // Check for 2MB page (bit 7 of PDE)
  if (pde & (1ULL << 7)) { // PS bit
    uint64_t physAddr =
        (pde & ~0x1FFFFFULL) | (virtualAddr & 0x1FFFFF); // Mask for 2MB page
    spdlog::trace("Translated virtual 0x{:x} to physical 0x{:x} (2MB page)",
                  virtualAddr, physAddr);
    {
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      tlb.Insert(virtualAddr, physAddr);
    }
    return physAddr;
  }

  uint64_t pt_base = pde & ~0xFFF;
  uint64_t pte_addr = pt_base + pt_idx * 8;
  uint64_t pte = 0;
  try {
    mmu.ReadPhysical(pte_addr, &pte, 8);
  } catch (const std::exception &e) {
    spdlog::error(
        "X86_64CPU[{}]: Page table walk failed: PTE read error at 0x{:x}: {}",
        m_cpuId, pte_addr, e.what());
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    throw CPUException("Page table walk error");
  }

  if (!(pte & 1)) { // Present bit
    spdlog::warn(
        "Page entry not present: 0x{:x}", virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false); // Page Fault
    return 0; // Return 0 instead of throwing to prevent cascade failures
  }
  if (!CheckPagePermissions(pte, cpl, true, true)) {
    spdlog::error("Page table fault: PTE permission violation at 0x{:x} "
                  "(virtual 0x{:x})",
                  pte_addr, virtualAddr);
    TriggerInterrupt(EXC_PF, virtualAddr, false);
    throw CPUException("Page table fault: PTE permission violation");
  }

  uint64_t physAddr = (pte & ~0xFFF) | offset;
  {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    tlb.Insert(virtualAddr, physAddr);
  }
  spdlog::trace("Translated virtual 0x{:x} to physical 0x{:x}", virtualAddr,
                physAddr);

  return physAddr;
}

bool X86_64CPU::CheckPagePermissions(uint64_t pte_entry, uint8_t cpl,
                                     bool is_write, bool is_execute) const {
  bool present = (pte_entry & (1ULL << 0)) != 0;
  bool rw = (pte_entry & (1ULL << 1)) != 0;  // Read/Write
  bool us = (pte_entry & (1ULL << 2)) != 0;  // User/Supervisor
  bool nx = (pte_entry & (1ULL << 63)) != 0; // No-Execute (if EFER.NXE is set)

  if (!present)
    return false; // Not present, always a fault

  // User/Supervisor check
  if (cpl == 3 && !us) { // User mode trying to access supervisor page
    spdlog::debug("Permission denied: User mode access to supervisor page.");
    return false;
  }

  // Read/Write check
  if (is_write && !rw) { // Trying to write to a read-only page
    spdlog::debug("Permission denied: Write access to read-only page.");
    return false;
  }

  // Execute check (if NXE is enabled in EFER)
  if (GetMSR(MSR::EFER) & (1ULL << 11)) { // EFER.NXE (No-Execute Enable)
    if (is_execute && nx) { // Trying to execute a non-executable page
      spdlog::debug(
          "Permission denied: Execute access to non-executable page.");
      return false;
    }
  }

  return true;
}

void X86_64CPU::Push(uint64_t value, uint8_t sizeInBytes) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  uint64_t rsp = _getRegister(Register::RSP);

  if (rsp < (uint64_t)sizeInBytes ||
      rsp - sizeInBytes < 0x1000) { // Basic stack overflow check
    lock.unlock();
    spdlog::critical(
        "X86_64CPU[{}]: Stack overflow during PUSH. RSP=0x{:x}, size={}",
        m_cpuId, rsp, sizeInBytes);
    TriggerInterrupt(EXC_SS, 0, false); // Stack Segment Fault
    throw CPUException("Stack overflow");
  }

  rsp -= sizeInBytes;
  _setRegister(Register::RSP, rsp);
  uint64_t processIdCopy = processId;
  lock.unlock(); // Release lock before MMU access

  try {
    mmu.WriteVirtual(rsp, reinterpret_cast<const void *>(&value), sizeInBytes,
                     processIdCopy);
  } catch (const std::exception &e) {
    spdlog::error(
        "X86_64CPU[{}]: Memory write failed during PUSH at 0x{:x}: {}", m_cpuId,
        rsp, e.what());
    TriggerInterrupt(EXC_PF, rsp, false); // Page Fault
    throw CPUException("Memory write error during PUSH");
  }
  spdlog::trace("Pushed value 0x{:x} to stack at 0x{:x}, size={}", value, rsp,
                sizeInBytes);
}

uint64_t X86_64CPU::Pop(uint8_t sizeInBytes) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  uint64_t rsp = _getRegister(Register::RSP);

  if (rsp + sizeInBytes > DEFAULT_STACK_POINTER ||
      rsp >= 0x800000000000ULL) { // Basic stack underflow check
    lock.unlock();
    spdlog::critical(
        "X86_64CPU[{}]: Stack underflow during POP. RSP=0x{:x}, size={}",
        m_cpuId, rsp, sizeInBytes);
    TriggerInterrupt(EXC_SS, 0, false); // Stack Segment Fault
    throw CPUException("Stack underflow");
  }

  uint64_t processIdCopy = processId;
  lock.unlock(); // Release lock before MMU access

  uint64_t value = 0;
  try {
    mmu.ReadVirtual(rsp, reinterpret_cast<void *>(&value), sizeInBytes,
                    processIdCopy);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Memory read failed during POP at 0x{:x}: {}",
                  m_cpuId, rsp, e.what());
    TriggerInterrupt(EXC_PF, rsp, false); // Page Fault
    throw CPUException("Memory read error during POP");
  }

  {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    _setRegister(Register::RSP, rsp + sizeInBytes);
  }
  spdlog::trace("Popped value 0x{:x} from stack at 0x{:x}, size={}", value, rsp,
                sizeInBytes);
  return value;
}

void X86_64CPU::UpdateFlags(uint64_t result, uint64_t op1, uint64_t op2,
                            uint8_t sizeInBits, bool isSubtract) {
  UpdateArithmeticFlags(op1, op2, result, sizeInBits, isSubtract);
}

bool X86_64CPU::CheckCondition(uint8_t conditionCode) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  switch (conditionCode) {
  // Overflow conditions
  case 0x0: // JO - Jump if overflow
    return GetFlag(FLAG_OF);
  case 0x1: // JNO - Jump if not overflow
    return !GetFlag(FLAG_OF);

  // Carry conditions
  case 0x2: // JB/JNAE/JC - Jump if below/carry
    return GetFlag(FLAG_CF);
  case 0x3: // JNB/JAE/JNC - Jump if not below/not carry
    return !GetFlag(FLAG_CF);

  // Zero conditions
  case 0x4: // JZ/JE - Jump if zero/equal
    return GetFlag(FLAG_ZF);
  case 0x5: // JNZ/JNE - Jump if not zero/not equal
    return !GetFlag(FLAG_ZF);

  // Below or equal / Above
  case 0x6: // JBE/JNA - Jump if below or equal
    return GetFlag(FLAG_CF) || GetFlag(FLAG_ZF);
  case 0x7: // JNBE/JA - Jump if not below or equal/above
    return !GetFlag(FLAG_CF) && !GetFlag(FLAG_ZF);

  // Sign conditions
  case 0x8: // JS - Jump if sign
    return GetFlag(FLAG_SF);
  case 0x9: // JNS - Jump if not sign
    return !GetFlag(FLAG_SF);

  // Parity conditions
  case 0xA: // JP/JPE - Jump if parity/parity even
    return GetFlag(FLAG_PF);
  case 0xB: // JNP/JPO - Jump if not parity/parity odd
    return !GetFlag(FLAG_PF);

  // Less than (signed)
  case 0xC: // JL/JNGE - Jump if less/not greater or equal
    return GetFlag(FLAG_SF) != GetFlag(FLAG_OF);
  case 0xD: // JNL/JGE - Jump if not less/greater or equal
    return GetFlag(FLAG_SF) == GetFlag(FLAG_OF);

  // Less than or equal / Greater than (signed)
  case 0xE: // JLE/JNG - Jump if less or equal/not greater
    return GetFlag(FLAG_ZF) || (GetFlag(FLAG_SF) != GetFlag(FLAG_OF));
  case 0xF: // JNLE/JG - Jump if not less or equal/greater
    return !GetFlag(FLAG_ZF) && (GetFlag(FLAG_SF) == GetFlag(FLAG_OF));

  default:
    spdlog::warn("X86_64CPU[{}]: Invalid condition code 0x{:x}", m_cpuId,
                 conditionCode);
    return false;
  }
}

void X86_64CPU::SetGDTR(uint64_t base, uint16_t limit) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  gdtrBase = base;
  gdtrLimit = limit;
  spdlog::info("X86_64CPU[{}]: Set GDTR: base=0x{:x}, limit=0x{:x}", m_cpuId,
               base, limit);
}

void X86_64CPU::SetIDTR(uint64_t base, uint16_t limit) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  idtrBase = base;
  idtrLimit = limit;
  spdlog::info("X86_64CPU[{}]: Set IDTR: base=0x{:x}, limit=0x{:x}", m_cpuId,
               base, limit);
}

void X86_64CPU::SetLDTR(uint16_t selector) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  ldtr = selector;
  spdlog::info("X86_64CPU[{}]: Set LDTR: selector=0x{:x}", m_cpuId, selector);
}

void X86_64CPU::SetTR(uint16_t selector) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  // Save old TR value
  uint16_t oldTR = tr;

  // Update TR
  tr = selector;
  spdlog::info("X86_64CPU[{}]: Set TR: selector=0x{:x}", m_cpuId, selector);

  // Load TSS descriptor
  LoadTSSDescriptor();

  // Load I/O permission bitmap
  if (tssBase != 0) {
    LoadIOPermissionBitmap();

    // Log I/O permission bitmap status
    if (!ioPermissionBitmap.empty()) {
      spdlog::info("X86_64CPU[{}]: I/O permission bitmap loaded: {} bytes",
                   m_cpuId, ioPermissionBitmap.size());
    } else {
      spdlog::info("X86_64CPU[{}]: No I/O permission bitmap available",
                   m_cpuId);
    }
  } else {
    spdlog::warn("X86_64CPU[{}]: Failed to load TSS descriptor for TR=0x{:x}",
                 m_cpuId, selector);

    // Restore old TR if TSS loading failed
    tr = oldTR;
  }
}

uint64_t X86_64CPU::GetGDTRBase() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return gdtrBase;
}

uint16_t X86_64CPU::GetGDTRLimit() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return gdtrLimit;
}

uint64_t X86_64CPU::GetIDTRBase() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return idtrBase;
}

uint16_t X86_64CPU::GetIDTRLimit() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return idtrLimit;
}

uint16_t X86_64CPU::GetLDTR() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return ldtr;
}

uint16_t X86_64CPU::GetTR() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return tr;
}

void X86_64CPU::SaveState(std::ostream &out) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  out.write(reinterpret_cast<const char *>(registers.data()),
            registers.size() * sizeof(uint64_t));
  out.write(reinterpret_cast<const char *>(xmmRegisters.data()),
            xmmRegisters.size() * sizeof(__m256i));
  out.write(reinterpret_cast<const char *>(&rflags), sizeof(rflags));
  out.write(reinterpret_cast<const char *>(segmentRegisters.data()),
            segmentRegisters.size() * sizeof(uint16_t));
  out.write(reinterpret_cast<const char *>(controlRegisters.data()),
            controlRegisters.size() * sizeof(uint64_t));
  out.write(reinterpret_cast<const char *>(debugRegisters.data()),
            debugRegisters.size() * sizeof(uint64_t));
  // Save MSRs (more complex due to unordered_map)
  size_t msr_count = msrRegisters.size();
  out.write(reinterpret_cast<const char *>(&msr_count), sizeof(msr_count));
  for (const auto &pair : msrRegisters) {
    out.write(reinterpret_cast<const char *>(&pair.first), sizeof(pair.first));
    out.write(reinterpret_cast<const char *>(&pair.second),
              sizeof(pair.second));
  }
  out.write(reinterpret_cast<const char *>(&gdtrBase), sizeof(gdtrBase));
  out.write(reinterpret_cast<const char *>(&gdtrLimit), sizeof(gdtrLimit));
  out.write(reinterpret_cast<const char *>(&idtrBase), sizeof(idtrBase));
  out.write(reinterpret_cast<const char *>(&idtrLimit), sizeof(idtrLimit));
  out.write(reinterpret_cast<const char *>(&ldtr), sizeof(ldtr));
  out.write(reinterpret_cast<const char *>(&tr), sizeof(tr));
  out.write(reinterpret_cast<const char *>(&tssBase), sizeof(tssBase));
  out.write(reinterpret_cast<const char *>(&tssLimit), sizeof(tssLimit));
  out.write(reinterpret_cast<const char *>(&processId), sizeof(processId));
  out.write(reinterpret_cast<const char *>(&halted), sizeof(halted));
  // FPU state (assuming fpuState is a POD struct)
  out.write(reinterpret_cast<const char *>(&fpuState), sizeof(fpuState));

  spdlog::info("X86_64CPU[{}]: Saved state", m_cpuId);
}

void X86_64CPU::LoadState(std::istream &in) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  in.read(reinterpret_cast<char *>(registers.data()),
          registers.size() * sizeof(uint64_t));
  in.read(reinterpret_cast<char *>(xmmRegisters.data()),
          xmmRegisters.size() * sizeof(__m256i));
  in.read(reinterpret_cast<char *>(&rflags), sizeof(rflags));
  in.read(reinterpret_cast<char *>(segmentRegisters.data()),
          segmentRegisters.size() * sizeof(uint16_t));
  in.read(reinterpret_cast<char *>(controlRegisters.data()),
          controlRegisters.size() * sizeof(uint64_t));
  in.read(reinterpret_cast<char *>(debugRegisters.data()),
          debugRegisters.size() * sizeof(uint64_t));
  // Load MSRs
  size_t msr_count;
  in.read(reinterpret_cast<char *>(&msr_count), sizeof(msr_count));
  msrRegisters.clear();
  for (size_t i = 0; i < msr_count; ++i) {
    uint32_t key;
    uint64_t value;
    in.read(reinterpret_cast<char *>(&key), sizeof(key));
    in.read(reinterpret_cast<char *>(&value), sizeof(value));
    msrRegisters[key] = value;
  }
  in.read(reinterpret_cast<char *>(&gdtrBase), sizeof(gdtrBase));
  in.read(reinterpret_cast<char *>(&gdtrLimit), sizeof(gdtrLimit));
  in.read(reinterpret_cast<char *>(&idtrBase), sizeof(idtrBase));
  in.read(reinterpret_cast<char *>(&idtrLimit), sizeof(idtrLimit));
  in.read(reinterpret_cast<char *>(&ldtr), sizeof(ldtr));
  in.read(reinterpret_cast<char *>(&tr), sizeof(tr));
  in.read(reinterpret_cast<char *>(&tssBase), sizeof(tssBase));
  in.read(reinterpret_cast<char *>(&tssLimit), sizeof(tssLimit));
  in.read(reinterpret_cast<char *>(&processId), sizeof(processId));
  in.read(reinterpret_cast<char *>(&halted), sizeof(halted));
  // FPU state
  in.read(reinterpret_cast<char *>(&fpuState), sizeof(fpuState));

  spdlog::info("X86_64CPU[{}]: Loaded state", m_cpuId);
}

uint64_t X86_64CPU::GetRegister(Register r) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return _getRegister(r);
}

bool X86_64CPU::SetRegister(Register r, uint64_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  _setRegister(r, v);
  return true; // signal success
}

ps4::PS4MMU &X86_64CPU::GetMemory() { return m_emulator.GetMemory(); }

uint32_t X86_64CPU::GetCPUId() const { return m_cpuId; }

void X86_64CPU::InvalidateTLB(uint64_t virtAddr) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  tlb.Invalidate(virtAddr);
}

uint64_t X86_64CPU::_getRegister(Register r) const {
  // Map sub-registers to their parent 64-bit registers
  Register parentReg;
  uint8_t regSize;
  uint8_t regOffset;

  if (!MapToParentRegister(r, parentReg, regSize, regOffset)) {
    CPU_DIAG_VECTOR_ACCESS(
        "X86_64CPU[{}]: Invalid register access - register {} (index {}) "
        "cannot be mapped to a general purpose register",
        m_cpuId, static_cast<int>(r), static_cast<size_t>(r));
    return 0;
  }

  // Get the parent register index (should be 0-16 for RAX-R15, RIP)
  size_t regIndex = static_cast<size_t>(parentReg);

  // CRITICAL: Enhanced bounds check to prevent vector out of range errors
  if (regIndex >= registers.size() || regIndex >= GENERAL_REGISTER_COUNT) {
    CPU_DIAG_VECTOR_ACCESS(
        "X86_64CPU[{}]: CRITICAL - Invalid register access - parent register {} (index {}) "
        "out of bounds (size {}, max {}). Preventing crash.",
        m_cpuId, static_cast<int>(parentReg), regIndex, registers.size(), GENERAL_REGISTER_COUNT - 1);
    // Return a safe default value instead of crashing
    return 0;
  }

  // Safe bounds-checked access
  uint64_t fullValue = registers[regIndex];
  // Extract the appropriate portion based on register size and offset
  return ExtractRegisterValue(fullValue, regSize, regOffset);
}

void X86_64CPU::_setRegister(Register r, uint64_t v) {
  // Map sub-registers to their parent 64-bit registers
  Register parentReg;
  uint8_t regSize;
  uint8_t regOffset;

  if (!MapToParentRegister(r, parentReg, regSize, regOffset)) {
    CPU_DIAG_VECTOR_ACCESS(
        "X86_64CPU[{}]: Invalid register write - register {} (index {}) "
        "cannot be mapped to a general purpose register",
        m_cpuId, static_cast<int>(r), static_cast<size_t>(r));
    return;
  }

  // Get the parent register index (should be 0-16 for RAX-R15, RIP)
  size_t regIndex = static_cast<size_t>(parentReg);

  // CRITICAL: Enhanced bounds check to prevent vector out of range errors
  if (regIndex >= registers.size() || regIndex >= GENERAL_REGISTER_COUNT) {
    CPU_DIAG_VECTOR_ACCESS(
        "X86_64CPU[{}]: CRITICAL - Invalid register write - parent register {} (index {}) "
        "out of bounds (size {}, max {}). Preventing crash.",
        m_cpuId, static_cast<int>(parentReg), regIndex, registers.size(), GENERAL_REGISTER_COUNT - 1);
    return;
  }

  // Safe bounds-checked access
  uint64_t fullValue = registers[regIndex];
  // Update the appropriate portion based on register size and offset
  registers[regIndex] = UpdateRegisterValue(fullValue, v, regSize, regOffset);
}

bool X86_64CPU::MapToParentRegister(Register r, Register& parentReg, uint8_t& regSize, uint8_t& regOffset) const {
  size_t regIndex = static_cast<size_t>(r);

  // 64-bit general purpose registers (RAX-R15, RIP) - direct mapping
  if (regIndex <= static_cast<size_t>(Register::RIP)) {
    parentReg = r;
    regSize = 64;
    regOffset = 0;
    return true;
  }

  // 32-bit general purpose registers (EAX-R15D)
  if (regIndex >= static_cast<size_t>(Register::EAX) && regIndex <= static_cast<size_t>(Register::R15D)) {
    size_t offset = regIndex - static_cast<size_t>(Register::EAX);
    parentReg = static_cast<Register>(static_cast<size_t>(Register::RAX) + offset);
    regSize = 32;
    regOffset = 0;
    return true;
  }

  // 16-bit general purpose registers (AX-R15W)
  if (regIndex >= static_cast<size_t>(Register::AX) && regIndex <= static_cast<size_t>(Register::R15W)) {
    size_t offset = regIndex - static_cast<size_t>(Register::AX);
    parentReg = static_cast<Register>(static_cast<size_t>(Register::RAX) + offset);
    regSize = 16;
    regOffset = 0;
    return true;
  }

  // 8-bit general purpose registers (AL-R15B)
  if (regIndex >= static_cast<size_t>(Register::AL) && regIndex <= static_cast<size_t>(Register::R15B)) {
    size_t offset = regIndex - static_cast<size_t>(Register::AL);

    // Handle special case for AH, CH, DH, BH (high byte of 16-bit registers)
    if (offset >= 4 && offset <= 7) { // AH, CH, DH, BH
      parentReg = static_cast<Register>(static_cast<size_t>(Register::RAX) + (offset - 4));
      regSize = 8;
      regOffset = 8; // High byte
      return true;
    } else {
      // Low byte registers (AL, CL, DL, BL, SPL, BPL, SIL, DIL, R8B-R15B)
      if (offset <= 3) { // AL, CL, DL, BL
        parentReg = static_cast<Register>(static_cast<size_t>(Register::RAX) + offset);
      } else { // SPL, BPL, SIL, DIL, R8B-R15B
        parentReg = static_cast<Register>(static_cast<size_t>(Register::RAX) + (offset - 4));
      }
      regSize = 8;
      regOffset = 0; // Low byte
      return true;
    }
  }

  // Non-general purpose registers are not supported by this method
  return false;
}

uint64_t X86_64CPU::ExtractRegisterValue(uint64_t fullValue, uint8_t regSize, uint8_t regOffset) const {
  switch (regSize) {
    case 64:
      return fullValue;
    case 32:
      return fullValue & 0xFFFFFFFFULL;
    case 16:
      return fullValue & 0xFFFFULL;
    case 8:
      if (regOffset == 8) {
        return (fullValue >> 8) & 0xFFULL; // High byte
      } else {
        return fullValue & 0xFFULL; // Low byte
      }
    default:
      spdlog::error("X86_64CPU[{}]: Invalid register size {} in ExtractRegisterValue", m_cpuId, regSize);
      return 0;
  }
}

uint64_t X86_64CPU::UpdateRegisterValue(uint64_t fullValue, uint64_t newValue, uint8_t regSize, uint8_t regOffset) const {
  switch (regSize) {
    case 64:
      return newValue;
    case 32:
      // 32-bit writes clear the upper 32 bits (x86-64 behavior)
      return newValue & 0xFFFFFFFFULL;
    case 16:
      // 16-bit writes preserve upper 48 bits
      return (fullValue & 0xFFFFFFFFFFFF0000ULL) | (newValue & 0xFFFFULL);
    case 8:
      if (regOffset == 8) {
        // High byte write preserves all other bits
        return (fullValue & 0xFFFFFFFFFFFF00FFULL) | ((newValue & 0xFFULL) << 8);
      } else {
        // Low byte write preserves upper 56 bits
        return (fullValue & 0xFFFFFFFFFFFFFF00ULL) | (newValue & 0xFFULL);
      }
    default:
      spdlog::error("X86_64CPU[{}]: Invalid register size {} in UpdateRegisterValue", m_cpuId, regSize);
      return fullValue;
  }
}

void X86_64CPU::SetRflags(uint64_t f) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  rflags = f;
  spdlog::trace("X86_64CPU[{}] RFLAGS set: 0x{:x}", m_cpuId, f);
}

uint64_t X86_64CPU::GetRflags() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return rflags;
}

bool X86_64CPU::GetFlag(uint64_t m) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return (rflags & m) != 0;
}

void X86_64CPU::SetFlag(uint64_t flag, bool value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  if (value) {
    rflags |= flag;
  } else {
    rflags &= ~flag;
  }
}

void X86_64CPU::UpdateLogicalFlags(uint64_t result, uint8_t sizeInBits) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  if (sizeInBits == 0 || sizeInBits > 64) {
    spdlog::error(
        "X86_64CPU[{}]: Invalid size for logical flag calculation: {} bits",
        m_cpuId, sizeInBits);
    return;
  }

  uint64_t mask =
      (sizeInBits == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << sizeInBits) - 1;
  uint64_t signBit = 1ULL << (sizeInBits - 1);

  result &= mask;

  // Clear logical flags AND carry/overflow flags for TEST/AND/OR/XOR
  // CF and OF are always cleared for logical operations
  rflags &= ~(FLAG_ZF | FLAG_SF | FLAG_PF | FLAG_CF | FLAG_OF);

  // Set flags based on result
  if (result == 0)
    rflags |= FLAG_ZF;
  if (result & signBit)
    rflags |= FLAG_SF;
  if (CalculateParity(result))
    rflags |= FLAG_PF;

  // AF is undefined for logical operations, so we leave it unchanged

  spdlog::trace("X86_64CPU[{}]: Logical flags updated: RFLAGS=0x{:x}", m_cpuId,
                rflags);
}

uint64_t X86_64CPU::GetControlRegister(Register r) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  size_t index = static_cast<size_t>(r) - static_cast<size_t>(Register::CR0);
  if (index >= controlRegisters.size()) {
    spdlog::error(
        "X86_64CPU[{}]: Attempted to read invalid control register: {}",
        m_cpuId, static_cast<int>(r));
    throw CPUException("Invalid control register access");
  }
  return controlRegisters[index];
}

void X86_64CPU::SetControlRegister(Register r, uint64_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  size_t index = static_cast<size_t>(r) - static_cast<size_t>(Register::CR0);
  if (index >= controlRegisters.size()) {
    spdlog::error(
        "X86_64CPU[{}]: Attempted to write invalid control register: {}",
        m_cpuId, static_cast<int>(r));
    TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
    return;
  }
  controlRegisters[index] = v;
  // Special handling for CR3 (TLB flush)
  if (r == Register::CR3) {
    tlb.Clear(); // CR3 writes invalidate TLB
    spdlog::info("X86_64CPU[{}]: CR3 set to 0x{:x}, TLB flushed.", m_cpuId, v);
  }
  spdlog::trace("X86_64CPU[{}]: CR{} set to 0x{:x}", m_cpuId, index, v);
}



uint64_t X86_64CPU::GetDebugRegister(Register r) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  size_t index = static_cast<size_t>(r) - static_cast<size_t>(Register::DR0);
  if (index >= debugRegisters.size()) {
    spdlog::error(
        "X86_64CPU[{}]: Attempted to read invalid debug register: {}", m_cpuId,
        static_cast<int>(r));
    throw CPUException("Invalid debug register access");
  }
  return debugRegisters[index];
}

void X86_64CPU::SetDebugRegister(Register r, uint64_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  size_t index = static_cast<size_t>(r) - static_cast<size_t>(Register::DR0);
  if (index >= debugRegisters.size()) {
    spdlog::error(
        "X86_64CPU[{}]: Attempted to write invalid debug register: {}", m_cpuId,
        static_cast<int>(r));
    TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
    return;
  }
  debugRegisters[index] = v;
  spdlog::trace("X86_64CPU[{}]: DR{} set to 0x{:x}", m_cpuId, index, v);
}

uint64_t X86_64CPU::GetMSR(MSR msr_index) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  auto it = msrRegisters.find(static_cast<uint32_t>(msr_index));
  if (it != msrRegisters.end()) {
    return it->second;
  }
  spdlog::warn("X86_64CPU[{}]: Attempted to read uninitialized MSR: 0x{:x}",
               m_cpuId, static_cast<uint32_t>(msr_index));
  return 0; // Return 0 for uninitialized MSRs
}

void X86_64CPU::SetMSR(MSR msr_index, uint64_t value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  msrRegisters[static_cast<uint32_t>(msr_index)] = value;
  spdlog::trace("X86_64CPU[{}]: MSR 0x{:x} set to 0x{:x}", m_cpuId,
                static_cast<uint32_t>(msr_index), value);
}

uint64_t X86_64CPU::GetCR0() const { return GetControlRegister(Register::CR0); }
uint64_t X86_64CPU::GetCR2() const { return GetControlRegister(Register::CR2); }
uint64_t X86_64CPU::GetCR3() const { return GetControlRegister(Register::CR3); }
uint64_t X86_64CPU::GetCR4() const { return GetControlRegister(Register::CR4); }

void X86_64CPU::SetCR0(uint64_t v) { SetControlRegister(Register::CR0, v); }
void X86_64CPU::SetCR2(uint64_t v) { SetControlRegister(Register::CR2, v); }
void X86_64CPU::SetCR3(uint64_t v) { SetControlRegister(Register::CR3, v); }
void X86_64CPU::SetCR4(uint64_t v) { SetControlRegister(Register::CR4, v); }

uint16_t X86_64CPU::GetCS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::CS) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetCS(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::CS) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: CS set to 0x{:x}", m_cpuId, v);
}

uint16_t X86_64CPU::GetSS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::SS) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetSS(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::SS) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: SS set to 0x{:x}", m_cpuId, v);
}

uint16_t X86_64CPU::GetDS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::DS) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetDS(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::DS) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: DS set to 0x{:x}", m_cpuId, v);
}

uint16_t X86_64CPU::GetES() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::ES) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetES(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::ES) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: ES set to 0x{:x}", m_cpuId, v);
}

uint16_t X86_64CPU::GetFS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::FS) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetFS(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::FS) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: FS set to 0x{:x}", m_cpuId, v);
}

uint16_t X86_64CPU::GetGS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return segmentRegisters[static_cast<size_t>(Register::GS) -
                          static_cast<size_t>(Register::ES)];
}

void X86_64CPU::SetGS(uint16_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  segmentRegisters[static_cast<size_t>(Register::GS) -
                   static_cast<size_t>(Register::ES)] = v;
  spdlog::trace("X86_64CPU[{}]: GS set to 0x{:x}", m_cpuId, v);
}

uint64_t X86_64CPU::GetFSBase() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return GetMSR(MSR::FS_BASE);
}

uint64_t X86_64CPU::GetGSBase() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return GetMSR(MSR::GS_BASE);
}

uint64_t X86_64CPU::GetTRBase() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return tssBase;
}

uint64_t X86_64CPU::GetTSSBase() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return tssBase;
}

uint16_t X86_64CPU::GetTSSLimit() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return tssLimit;
}

uint16_t X86_64CPU::GetKernelSS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return kernelSS;
}

uint8_t X86_64CPU::GetCPL() const {
  // Current Privilege Level is bits 0 and 1 of CS selector
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return GetCS() & 0x3;
}

uint8_t X86_64CPU::GetIOPL() const {
  // I/O Privilege Level is bits 12 and 13 of RFLAGS
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return (rflags >> 12) & 0x3;
}

X86_64JITCompiler &X86_64CPU::GetJITCompiler() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return *jit;
}

Pipeline &X86_64CPU::GetPipeline() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return *pipeline;
}

uint64_t X86_64CPU::GetProcessId() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return processId;
}

void X86_64CPU::SetProcessId(uint64_t pid) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  processId = pid;
  spdlog::info("X86_64CPU[{}]: Process ID set to 0x{:x}", m_cpuId, pid);
}

void X86_64CPU::Execute() { FetchDecodeExecute(); }

void X86_64CPU::SetContext(const CPUContext &ctx) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  registers = ctx.registers;
  rflags = ctx.rflags;
  xmmRegisters = ctx.xmmRegisters;
  _setRegister(Register::RIP, ctx.rip);
  _setRegister(Register::RSP, ctx.rsp);
  // Restore segment registers
  segmentRegisters[static_cast<size_t>(Register::CS) -
                   static_cast<size_t>(Register::ES)] = ctx.cs;
  segmentRegisters[static_cast<size_t>(Register::DS) -
                   static_cast<size_t>(Register::ES)] = ctx.ds;
  segmentRegisters[static_cast<size_t>(Register::ES) -
                   static_cast<size_t>(Register::ES)] = ctx.es;
  segmentRegisters[static_cast<size_t>(Register::FS) -
                   static_cast<size_t>(Register::ES)] = ctx.fs;
  segmentRegisters[static_cast<size_t>(Register::GS) -
                   static_cast<size_t>(Register::ES)] = ctx.gs;
  segmentRegisters[static_cast<size_t>(Register::SS) -
                   static_cast<size_t>(Register::ES)] = ctx.ss;
  // Restore control registers
  controlRegisters[0] = ctx.cr0;
  controlRegisters[2] = ctx.cr2;
  controlRegisters[3] = ctx.cr3;
  controlRegisters[4] = ctx.cr4;
  // Restore FPU state (simplified, assumes direct copy)
  std::memcpy(&fpuState, ctx.fpu_state_bytes, sizeof(FPUState));

  spdlog::info("X86_64CPU[{}]: Context set: RIP=0x{:x}, RSP=0x{:x}", m_cpuId,
               ctx.rip, ctx.rsp);
}

std::unordered_map<std::string, uint64_t> X86_64CPU::GetDiagnostics() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  std::unordered_map<std::string, uint64_t> diagnostics;
  diagnostics["cycles"] = pipeline->GetStats().cycles;
  diagnostics["instructionsExecuted"] =
      pipeline->GetStats().instructionsExecuted;
  diagnostics["stalls"] = pipeline->GetStats().stalls;
  diagnostics["data_hazard_stalls"] = pipeline->GetStats().data_hazard_stalls;
  diagnostics["memory_stalls"] = pipeline->GetStats().memory_stalls;
  diagnostics["branch_hits"] = pipeline->GetStats().branch_hits;
  diagnostics["branch_mispredictions"] =
      pipeline->GetStats().branch_mispredictions;
  diagnostics["interruptQueueSize"] = interruptQueue.size();
  diagnostics["tlbHits"] = tlb.GetHits();
  diagnostics["tlbMisses"] = tlb.GetMisses();
  spdlog::trace("X86_64CPU[{}]: Diagnostics retrieved", m_cpuId);
  return diagnostics;
}

bool X86_64CPU::SwitchToFiber(uint64_t fiberId) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  try {
    // Save current CPU state
    CPUContext currentContext;
    currentContext.registers = registers;
    currentContext.rflags = rflags;
    currentContext.xmmRegisters = xmmRegisters;
    currentContext.rip = GetRegister(Register::RIP);
    currentContext.rsp = GetRegister(Register::RSP);
    // Save segment registers
    currentContext.cs = GetCS();
    currentContext.ds = GetDS();
    currentContext.es = GetES();
    currentContext.fs = GetFS();
    currentContext.gs = GetGS();
    currentContext.ss = GetSS();
    // Save control registers
    currentContext.cr0 = GetCR0();
    currentContext.cr2 = GetCR2();
    currentContext.cr3 = GetCR3();
    currentContext.cr4 = GetCR4();
    // Save FPU state
    std::memcpy(currentContext.fpu_state_bytes, &fpuState, sizeof(FPUState));

    // Get fiber manager from emulator
    auto &fiberManager = m_emulator.GetFiberManager();

    // Switch to the specified fiber
    bool success = fiberManager.SwitchToFiber(fiberId);
    if (!success) {
      spdlog::error("X86_64CPU[{}]: Failed to switch to fiber {}", m_cpuId,
                    fiberId);
      return false;
    }

    // The fiber manager should have updated the CPU context
    // In a full implementation, we would restore the fiber's saved context
    // here
    spdlog::info("X86_64CPU[{}]: Successfully switched to fiber {}", m_cpuId,
                 fiberId);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Exception during fiber switch to {}: {}",
                  m_cpuId, fiberId, e.what());
    return false;
  }
}

bool X86_64CPU::IsRunning() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return running;
}

bool X86_64CPU::ValidateIOPortAccess(uint16_t port, uint8_t size) {
  // Check I/O privilege level (IOPL) in RFLAGS
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  uint8_t iopl = GetIOPL();
  uint8_t cpl = GetCPL();

  // Validate port range and size first
  if (port + size > 0x10000) {
    spdlog::error("X86_64CPU[{}]: I/O port access beyond valid range: "
                  "port=0x{:x}, size={}",
                  m_cpuId, port, size);
    TriggerInterrupt(EXC_GP, 0, false);
    return false;
  }

  // If CPL <= IOPL, access is allowed without checking I/O permission bitmap
  if (cpl <= iopl) {
    spdlog::trace("X86_64CPU[{}]: I/O port access allowed by IOPL: "
                  "port=0x{:x}, CPL={}, IOPL={}",
                  m_cpuId, port, cpl, iopl);
    return true;
  }

  // CPL > IOPL, need to check I/O permission bitmap
  spdlog::debug("X86_64CPU[{}]: Checking I/O permission bitmap for port "
                "0x{:x}, CPL={}, IOPL={}",
                m_cpuId, port, cpl, iopl);

  // Load TSS and I/O permission bitmap if not already loaded
  LoadIOPermissionBitmap();

  // Check I/O permission bitmap
  if (!CheckIOPermissionBitmap(port, size)) {
    spdlog::warn("X86_64CPU[{}]: I/O port access denied by permission bitmap: "
                 "port=0x{:x}, size={}",
                 m_cpuId, port, size);
    TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
    return false;
  }

  spdlog::trace("X86_64CPU[{}]: I/O port access allowed by permission bitmap: "
                "port=0x{:x}",
                m_cpuId, port);
  return true;
}

void X86_64CPU::LoadTSSDescriptor() {
  if (tr == 0) {
    spdlog::debug("X86_64CPU[{}]: TR is 0, no TSS loaded", m_cpuId);
    return;
  }

  // Calculate TSS descriptor address in GDT
  uint64_t descriptorAddr = gdtrBase + (tr & 0xFFF8); // Clear RPL and TI bits

  if (descriptorAddr + sizeof(TSSDescriptor) > gdtrBase + gdtrLimit) {
    spdlog::error("X86_64CPU[{}]: TSS descriptor beyond GDT limit", m_cpuId);
    TriggerInterrupt(EXC_GP, tr, false);
    return;
  }

  // Read TSS descriptor from memory
  TSSDescriptor tssDesc;
  mmu.ReadVirtual(descriptorAddr, &tssDesc, sizeof(TSSDescriptor),
                  GetProcessId());

  // Calculate TSS base address (64-bit)
  tssBase = static_cast<uint64_t>(tssDesc.base_low) |
            (static_cast<uint64_t>(tssDesc.base_mid) << 16) |
            (static_cast<uint64_t>(tssDesc.base_high) << 24) |
            (static_cast<uint64_t>(tssDesc.base_upper) << 32);

  // Calculate TSS limit
  tssLimit = tssDesc.limit_low;
  if (tssDesc.granularity & 0x80) {      // G bit set
    tssLimit = (tssLimit << 12) | 0xFFF; // Page granularity
  }

  spdlog::debug(
      "X86_64CPU[{}]: Loaded TSS descriptor: base=0x{:x}, limit=0x{:x}",
      m_cpuId, tssBase, tssLimit);
}

void X86_64CPU::LoadIOPermissionBitmap() {
  // Load TSS descriptor if not already loaded
  if (tssBase == 0) {
    LoadTSSDescriptor();
  }

  if (tssBase == 0) {
    spdlog::debug(
        "X86_64CPU[{}]: No valid TSS, I/O permission bitmap unavailable",
        m_cpuId);
    ioPermissionBitmap.clear();
    return;
  }

  // Read TSS to get I/O permission bitmap offset
  TSS64 tss;
  if (tssLimit < sizeof(TSS64)) {
    spdlog::warn("X86_64CPU[{}]: TSS too small for 64-bit TSS structure",
                 m_cpuId);
    ioPermissionBitmap.clear();
    return;
  }

  mmu.ReadVirtual(tssBase, &tss, sizeof(TSS64), GetProcessId());
  ioPermissionBitmapOffset = tss.io_map_base;

  // Check if I/O permission bitmap offset is valid
  if (ioPermissionBitmapOffset >= tssLimit) {
    spdlog::debug(
        "X86_64CPU[{}]: I/O permission bitmap offset beyond TSS limit",
        m_cpuId);
    ioPermissionBitmap.clear();
    return;
  }

  // Calculate I/O permission bitmap size (from offset to end of TSS)
  uint32_t bitmapSize = tssLimit - ioPermissionBitmapOffset + 1;

  // Limit bitmap size to reasonable maximum (8KB covers all 65536 ports)
  if (bitmapSize > 8192) {
    bitmapSize = 8192;
  }

  // Read I/O permission bitmap from TSS
  ioPermissionBitmap.resize(bitmapSize);
  mmu.ReadVirtual(tssBase + ioPermissionBitmapOffset, ioPermissionBitmap.data(),
                  bitmapSize, GetProcessId());

  spdlog::debug("X86_64CPU[{}]: Loaded I/O permission bitmap: offset=0x{:x}, "
                "size={} bytes",
                m_cpuId, ioPermissionBitmapOffset, bitmapSize);
}

bool X86_64CPU::CheckIOPermissionBitmap(uint16_t port, uint8_t size) {
  if (ioPermissionBitmap.empty()) {
    // No I/O permission bitmap available, deny access
    return false;
  }

  // Check each port in the range
  for (uint8_t i = 0; i < size; i++) {
    uint16_t currentPort = port + i;
    uint32_t byteIndex = currentPort / 8;
    uint8_t bitIndex = currentPort % 8;

    // Check if byte index is within bitmap
    if (byteIndex >= ioPermissionBitmap.size()) {
      // Port beyond bitmap, deny access
      return false;
    }

    // Check if bit is set (1 = deny, 0 = allow)
    if (ioPermissionBitmap[byteIndex] & (1 << bitIndex)) {
      // Access denied for this port
      return false;
    }
  }

  // All ports in range are allowed
  return true;
}

uint64_t X86_64CPU::ReadIOPort(uint16_t port, uint8_t size) {
  auto weakDev = deviceManager.FindDevice(port);
  if (auto dev = weakDev.lock()) {
    return dev->Read(port, size);
  }
  if (!ValidateIOPortAccess(port, size)) {
    // ValidateIOPortAccess already triggers GP fault
    return 0;
  }

  // In a real emulator, this would interface with hardware devices
  // For now, return dummy values based on common ports
  uint64_t value = 0;

  switch (port) {
  case 0x20:      // PIC1 command
  case 0x21:      // PIC1 data
  case 0xA0:      // PIC2 command
  case 0xA1:      // PIC2 data
    value = 0x00; // PIC not active
    break;
  case 0x40:      // Timer channel 0
  case 0x41:      // Timer channel 1
  case 0x42:      // Timer channel 2
  case 0x43:      // Timer command
    value = 0x00; // Timer inactive
    break;
  case 0x60:      // Keyboard data
    value = 0x00; // No key pressed
    break;
  case 0x64:      // Keyboard status
    value = 0x14; // Ready for input (output buffer empty, input buffer empty,
                  // system flag clear)
    break;
  case 0x70:      // CMOS address
  case 0x71:      // CMOS data
    value = 0x00; // CMOS default
    break;
  case 0x80:      // NMI Status and Control Register (NMI_SC)
    value = 0x00; // Dummy value
    break;
  case 0x92:      // Fast A20 Gate
    value = 0x02; // A20 enabled (bit 1)
    break;
  case 0x3F8:     // COM1 data
  case 0x3F9:     // COM1 interrupt enable
  case 0x3FA:     // COM1 interrupt ID
  case 0x3FB:     // COM1 line control
  case 0x3FC:     // COM1 modem control
  case 0x3FD:     // COM1 line status
  case 0x3FE:     // COM1 modem status
  case 0x3FF:     // COM1 scratch
    value = 0x60; // Serial port ready (LSR: THRE, DR)
    break;
  case 0xCF8:     // PCI Configuration Address Register
    value = 0x00; // No PCI device selected
    break;
  case 0xCFC:           // PCI Configuration Data Register
    value = 0xFFFFFFFF; // Dummy value for unconfigured PCI
    break;
  default:
    spdlog::warn("X86_64CPU[{}]: Read from unhandled I/O port 0x{:x}", m_cpuId,
                 port);
    value = 0xFFFFFFFFFFFFFFFFULL; // Unconnected port
    break;
  }

  // Mask to requested size
  switch (size) {
  case 1:
    value &= 0xFF;
    break;
  case 2:
    value &= 0xFFFF;
    break;
  case 4:
    value &= 0xFFFFFFFF;
    break;
  // 8-byte I/O not supported in x86-64
  default:
    spdlog::error("X86_64CPU[{}]: Invalid I/O read size: {}", m_cpuId, size);
    TriggerInterrupt(EXC_GP, 0, false);
    return 0;
  }

  spdlog::trace("X86_64CPU[{}]: Read I/O port 0x{:x} size={} value=0x{:x}",
                m_cpuId, port, size, value);
  return value;
}

void X86_64CPU::WriteIOPort(uint16_t port, uint64_t value, uint8_t size) {
  auto weakDev = deviceManager.FindDevice(port);
  if (auto dev = weakDev.lock()) {
    dev->Write(port, value, size);
    return;
  }
  if (!ValidateIOPortAccess(port, size)) {
    // ValidateIOPortAccess already triggers GP fault
    return;
  }

  // In a real emulator, this would interface with hardware devices
  spdlog::trace("X86_64CPU[{}]: Write I/O port 0x{:x} size={} value=0x{:x}",
                m_cpuId, port, size, value);

  // Handle some common ports for logging and basic state changes
  switch (port) {
  case 0x20: // PIC1 command
  case 0xA0: // PIC2 command
    spdlog::debug("X86_64CPU[{}]: PIC command written: 0x{:x}", m_cpuId, value);
    break;
  case 0x21: // PIC1 data
  case 0xA1: // PIC2 data
    spdlog::debug("X86_64CPU[{}]: PIC mask written: 0x{:x}", m_cpuId, value);
    break;
  case 0x43: // Timer command
    spdlog::debug("X86_64CPU[{}]: Timer command written: 0x{:x}", m_cpuId,
                  value);
    break;
  case 0x64:             // Keyboard command
    if (value == 0xFE) { // Pulse CPU reset
      spdlog::warn("X86_64CPU[{}]: Keyboard controller requested CPU reset!",
                   m_cpuId);
      // This would typically trigger a full emulator reset
      // For now, just log
    }
    break;
  case 0x70: // CMOS address
    spdlog::debug("X86_64CPU[{}]: CMOS address written: 0x{:x}", m_cpuId,
                  value);
    break;
  case 0x71: // CMOS data
    spdlog::debug("X86_64CPU[{}]: CMOS data written: 0x{:x}", m_cpuId, value);
    break;
  case 0x92: // Fast A20 Gate
    spdlog::debug("X86_64CPU[{}]: Fast A20 Gate written: 0x{:x}", m_cpuId,
                  value);
    // Bit 1 controls A20 gate
    break;
  case 0x3F8: // COM1 data
    spdlog::debug("X86_64CPU[{}]: Serial output: 0x{:x} ('{}')", m_cpuId, value,
                  (value >= 32 && value <= 126) ? static_cast<char>(value)
                                                : '?');
    break;
  case 0xCF8: // PCI Configuration Address Register
    spdlog::debug("X86_64CPU[{}]: PCI Config Address written: 0x{:x}", m_cpuId,
                  value);
    // In a real emulator, this would select a PCI device/register
    break;
  case 0xCFC: // PCI Configuration Data Register
    spdlog::debug("X86_64CPU[{}]: PCI Config Data written: 0x{:x}", m_cpuId,
                  value);
    break;
  default:
    spdlog::warn(
        "X86_64CPU[{}]: Write to unhandled I/O port 0x{:x} with value 0x{:x}",
        m_cpuId, port, value);
    break;
  }
}

void X86_64CPU::SetIOPermissionBit(uint16_t port, bool allow) {
  if (ioPermissionBitmap.empty()) {
    // Initialize bitmap if not present (8KB for all 65536 ports)
    ioPermissionBitmap.resize(8192, 0xFF); // Default deny all
  }

  uint32_t byteIndex = port / 8;
  uint8_t bitIndex = port % 8;

  if (byteIndex >= ioPermissionBitmap.size()) {
    spdlog::warn("X86_64CPU[{}]: Port 0x{:x} beyond I/O permission bitmap",
                 m_cpuId, port);
    return;
  }

  if (allow) {
    // Clear bit to allow access (0 = allow)
    ioPermissionBitmap[byteIndex] &= ~(1 << bitIndex);
  } else {
    // Set bit to deny access (1 = deny)
    ioPermissionBitmap[byteIndex] |= (1 << bitIndex);
  }

  spdlog::debug("X86_64CPU[{}]: Set I/O permission for port 0x{:x}: {}",
                m_cpuId, port, allow ? "ALLOW" : "DENY");
}

void X86_64CPU::SetIOPermissionRange(uint16_t startPort, uint16_t endPort,
                                     bool allow) {
  for (uint16_t port = startPort; port <= endPort; port++) {
    SetIOPermissionBit(port, allow);
  }

  spdlog::info("X86_64CPU[{}]: Set I/O permission for range 0x{:x}-0x{:x}: {}",
               m_cpuId, startPort, endPort, allow ? "ALLOW" : "DENY");
}

void X86_64CPU::ClearIOPermissionBitmap() {
  ioPermissionBitmap.clear();
  ioPermissionBitmapOffset = 0;
  spdlog::debug("X86_64CPU[{}]: Cleared I/O permission bitmap", m_cpuId);
}

void X86_64CPU::UpdateIOPermissionBitmapOffset(uint16_t offset) {
  ioPermissionBitmapOffset = offset;
  // Force reload of bitmap on next access
  ioPermissionBitmap.clear();
  spdlog::debug("X86_64CPU[{}]: Updated I/O permission bitmap offset to 0x{:x}",
                m_cpuId, offset);
}

void X86_64CPU::UpdateStringIndexes(uint8_t size, bool forward) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  int64_t delta = forward ? size : -size;

  // Update RSI and RDI based on direction flag
  bool directionFlag = GetFlag(FLAG_DF);
  if (directionFlag) {
    delta = -delta;
  }

  uint64_t rsi = _getRegister(Register::RSI);
  uint64_t rdi = _getRegister(Register::RDI);
  _setRegister(Register::RSI, rsi + delta);
  _setRegister(Register::RDI, rdi + delta);

  spdlog::trace("X86_64CPU[{}]: Updated string indexes: RSI=0x{:x}, "
                "RDI=0x{:x}, delta={}",
                m_cpuId, rsi + delta, rdi + delta, delta);
}

void X86_64CPU::ExecuteStringOperation(const DecodedInstruction &instr,
                                       uint64_t &nextRip) {
  // Handle REP prefixes
  bool hasRepPrefix = instr.repPrefix || instr.repePrefix || instr.repnePrefix;
  uint64_t count = hasRepPrefix ? GetRegister(Register::RCX) : 1;

  if (hasRepPrefix && count == 0) {
    spdlog::trace(
        "X86_64CPU[{}]: String operation with REP but RCX=0, skipping",
        m_cpuId);
    return;
  }

  // Determine operation size
  uint8_t size = 1;
  switch (instr.instType) {
  case InstructionType::Movsb:
  case InstructionType::Stosb:
  case InstructionType::Lodsb:
  case InstructionType::Scasb:
  case InstructionType::Cmpsb:
    size = 1;
    break;
  case InstructionType::Movsw:
  case InstructionType::Stosw:
  case InstructionType::Lodsw:
  case InstructionType::Scasw:
  case InstructionType::Cmpsw:
    size = 2;
    break;
  case InstructionType::Movsd_str:
  case InstructionType::Stosd:
  case InstructionType::Lodsd:
  case InstructionType::Scasd:
  case InstructionType::Cmpsd:
    size = 4;
    break;
  case InstructionType::Movsq:
  case InstructionType::Stosq:
  case InstructionType::Lodsq:
  case InstructionType::Scasq:
  case InstructionType::Cmpsq:
    size = 8;
    break;
  default:
    spdlog::error("X86_64CPU[{}]: Invalid string operation type: {}", m_cpuId,
                  static_cast<int>(instr.instType));
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  bool continueLoop = true;
  uint64_t iterations = 0;

  while (continueLoop && count > 0) {
    switch (instr.instType) {
    case InstructionType::Movsb:
    case InstructionType::Movsw:
    case InstructionType::Movsd_str:
    case InstructionType::Movsq: {
      uint64_t rsi = GetRegister(Register::RSI);
      uint64_t rdi = GetRegister(Register::RDI);

      // Read from source
      std::vector<uint8_t> buffer(size);
      try {
        if (!mmu.ReadVirtual(rsi, buffer.data(), size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rsi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: MOVSx source read failed at 0x{:x}: {}",
                      m_cpuId, rsi, e.what());
        TriggerInterrupt(EXC_PF, rsi, false);
        return;
      }

      // Write to destination
      try {
        if (!mmu.WriteVirtual(rdi, buffer.data(), size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rdi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error(
            "X86_64CPU[{}]: MOVSx destination write failed at 0x{:x}: {}",
            m_cpuId, rdi, e.what());
        TriggerInterrupt(EXC_PF, rdi, false);
        return;
      }

      UpdateStringIndexes(size);
      spdlog::trace(
          "X86_64CPU[{}]: MOVS{}: copied {} bytes from 0x{:x} to 0x{:x}",
          m_cpuId,
          size == 1   ? "B"
          : size == 2 ? "W"
          : size == 4 ? "D"
                      : "Q",
          size, rsi, rdi);
      break;
    }

    case InstructionType::Stosb:
    case InstructionType::Stosw:
    case InstructionType::Stosd:
    case InstructionType::Stosq: {
      uint64_t rdi = GetRegister(Register::RDI);
      uint64_t value = GetRegister(Register::RAX) & ((1ULL << (size * 8)) - 1);

      try {
        if (!mmu.WriteVirtual(rdi, &value, size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rdi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: STOSx write failed at 0x{:x}: {}",
                      m_cpuId, rdi, e.what());
        TriggerInterrupt(EXC_PF, rdi, false);
        return;
      }

      UpdateStringIndexes(size);
      spdlog::trace("X86_64CPU[{}]: STOS{}: stored 0x{:x} to 0x{:x}", m_cpuId,
                    size == 1   ? "B"
                    : size == 2 ? "W"
                    : size == 4 ? "D"
                                : "Q",
                    value, rdi);
      break;
    }

    case InstructionType::Lodsb:
    case InstructionType::Lodsw:
    case InstructionType::Lodsd:
    case InstructionType::Lodsq: {
      uint64_t rsi = GetRegister(Register::RSI);
      uint64_t value = 0;

      try {
        if (!mmu.ReadVirtual(rsi, &value, size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rsi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: LODSx read failed at 0x{:x}: {}", m_cpuId,
                      rsi, e.what());
        TriggerInterrupt(EXC_PF, rsi, false);
        return;
      }

      // Store in AL/AX/EAX/RAX based on size
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      uint64_t rax = _getRegister(Register::RAX);
      switch (size) {
      case 1:
        rax = (rax & 0xFFFFFFFFFFFFFF00ULL) | (value & 0xFF);
        break;
      case 2:
        rax = (rax & 0xFFFFFFFFFFFF0000ULL) | (value & 0xFFFF);
        break;
      case 4:
        rax = (rax & 0xFFFFFFFF00000000ULL) | (value & 0xFFFFFFFF);
        break;
      case 8:
        rax = value;
        break;
      }
      _setRegister(Register::RAX, rax);
      lock.unlock();

      UpdateStringIndexes(size);
      spdlog::trace("X86_64CPU[{}]: LODS{}: loaded 0x{:x} from 0x{:x} into RAX",
                    m_cpuId,
                    size == 1   ? "B"
                    : size == 2 ? "W"
                    : size == 4 ? "D"
                                : "Q",
                    value, rsi);
      break;
    }

    case InstructionType::Scasb:
    case InstructionType::Scasw:
    case InstructionType::Scasd:
    case InstructionType::Scasq: {
      uint64_t rdi = GetRegister(Register::RDI);
      uint64_t raxValue =
          GetRegister(Register::RAX) & ((1ULL << (size * 8)) - 1);
      uint64_t memValue = 0;

      try {
        if (!mmu.ReadVirtual(rdi, &memValue, size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rdi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: SCASx read failed at 0x{:x}: {}", m_cpuId,
                      rdi, e.what());
        TriggerInterrupt(EXC_PF, rdi, false);
        return;
      }

      memValue &= ((1ULL << (size * 8)) - 1);
      uint64_t result = raxValue - memValue;
      UpdateArithmeticFlags(raxValue, memValue, result, size * 8, true);

      UpdateStringIndexes(size);

      // Check termination condition for REPE/REPNE
      if (instr.repePrefix) {
        continueLoop = GetFlag(FLAG_ZF); // Continue while equal
      } else if (instr.repnePrefix) {
        continueLoop = !GetFlag(FLAG_ZF); // Continue while not equal
      }

      spdlog::trace("X86_64CPU[{}]: SCAS{}: compared RAX=0x{:x} with "
                    "[0x{:x}]=0x{:x}, ZF={}",
                    m_cpuId,
                    size == 1   ? "B"
                    : size == 2 ? "W"
                    : size == 4 ? "D"
                                : "Q",
                    raxValue, rdi, memValue, GetFlag(FLAG_ZF) ? 1 : 0);
      break;
    }

    case InstructionType::Cmpsb:
    case InstructionType::Cmpsw:
    case InstructionType::Cmpsd:
    case InstructionType::Cmpsq: {
      uint64_t rsi = GetRegister(Register::RSI);
      uint64_t rdi = GetRegister(Register::RDI);
      uint64_t srcValue = 0, dstValue = 0;

      try {
        if (!mmu.ReadVirtual(rsi, &srcValue, size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rsi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error("X86_64CPU[{}]: CMPSx source read failed at 0x{:x}: {}",
                      m_cpuId, rsi, e.what());
        TriggerInterrupt(EXC_PF, rsi, false);
        return;
      }

      try {
        if (!mmu.ReadVirtual(rdi, &dstValue, size, GetProcessId())) {
          TriggerInterrupt(EXC_PF, rdi, false);
          return;
        }
      } catch (const std::exception &e) {
        spdlog::error(
            "X86_64CPU[{}]: CMPSx destination read failed at 0x{:x}: {}",
            m_cpuId, rdi, e.what());
        TriggerInterrupt(EXC_PF, rdi, false);
        return;
      }

      srcValue &= ((1ULL << (size * 8)) - 1);
      dstValue &= ((1ULL << (size * 8)) - 1);
      uint64_t result = srcValue - dstValue;
      UpdateArithmeticFlags(srcValue, dstValue, result, size * 8, true);

      UpdateStringIndexes(size);

      // Check termination condition for REPE/REPNE
      if (instr.repePrefix) {
        continueLoop = GetFlag(FLAG_ZF); // Continue while equal
      } else if (instr.repnePrefix) {
        continueLoop = !GetFlag(FLAG_ZF); // Continue while not equal
      }

      spdlog::trace("X86_64CPU[{}]: CMPS{}: compared [0x{:x}]=0x{:x} with "
                    "[0x{:x}]=0x{:x}, ZF={}",
                    m_cpuId,
                    size == 1   ? "B"
                    : size == 2 ? "W"
                    : size == 4 ? "D"
                                : "Q",
                    rsi, srcValue, rdi, dstValue, GetFlag(FLAG_ZF) ? 1 : 0);
      break;
    }

    default:
      spdlog::error("X86_64CPU[{}]: Unhandled string operation: {}", m_cpuId,
                    static_cast<int>(instr.instType));
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    if (hasRepPrefix) {
      count--;
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      _setRegister(Register::RCX, count);
      lock.unlock();

      // Prevent infinite loops in emulation
      iterations++;
      if (iterations >
          MAX_REP_ITERATIONS) { // Define MAX_REP_ITERATIONS in header
        spdlog::warn("X86_64CPU[{}]: String operation interrupted after {} "
                     "iterations to prevent hang",
                     m_cpuId, iterations);
        break;
      }
    } else {
      continueLoop = false;
    }
  }

  spdlog::trace("X86_64CPU[{}]: String operation completed after {} "
                "iterations, RCX=0x{:x}",
                m_cpuId, iterations, GetRegister(Register::RCX));
}

// ========================
// Floating-Point Unit (FPU)
// ========================

void X86_64CPU::ExecuteFloatingPointInstruction(const DecodedInstruction &instr,
                                                uint64_t &nextRip) {
  // Check for stack overflow/underflow before executing
  if (fpuState.IsStackEmpty() && (instr.instType == InstructionType::Fadd ||
                                  instr.instType == InstructionType::Fsub ||
                                  instr.instType == InstructionType::Fmul ||
                                  instr.instType == InstructionType::Fdiv ||
                                  instr.instType == InstructionType::Fcom ||
                                  instr.instType == InstructionType::Fsin ||
                                  instr.instType == InstructionType::Fcos)) {
    fpuState.SetStackUnderflow();
    TriggerInterrupt(EXC_MF, 0, false); // Math Fault
    return;
  }

  switch (instr.instType) {
  case InstructionType::Fld: {
    // FLD - Load floating point value onto stack
    if (fpuState.IsStackFull()) {
      fpuState.SetStackOverflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    double value = 0.0;
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::MEMORY) {
      uint64_t addr = CalculateMemoryAddress(instr.operands[0]);
      if (instr.operands[0].size == 32) {
        float fval;
        mmu.ReadVirtual(addr, &fval, 4, GetProcessId());
        value = static_cast<double>(fval);
      } else if (instr.operands[0].size == 64) {
        mmu.ReadVirtual(addr, &value, 8, GetProcessId());
      } else if (instr.operands[0].size == 80) {
        // 80-bit extended precision (simplified)
        mmu.ReadVirtual(addr, &value, 8, GetProcessId());
      }
    } else if (instr.operands[0].type ==
               DecodedInstruction::Operand::Type::FPU) {
      uint8_t st_reg = static_cast<uint8_t>(instr.operands[0].reg) -
                       static_cast<uint8_t>(Register::ST0);
      uint8_t stack_idx = fpuState.GetStackIndex(st_reg);
      value = fpuState.st[stack_idx];
    }

    fpuState.PushStack(value);
    break;
  }

  case InstructionType::Fst:
  case InstructionType::Fstp: {
    // FST/FSTP - Store floating point value from stack
    if (fpuState.IsStackEmpty()) {
      fpuState.SetStackUnderflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    double value = fpuState.st[fpuState.top];

    if (instr.operands[0].type == DecodedInstruction::Operand::Type::MEMORY) {
      uint64_t addr = CalculateMemoryAddress(instr.operands[0]);
      if (instr.operands[0].size == 32) {
        float fval = static_cast<float>(value);
        mmu.WriteVirtual(addr, &fval, 4, GetProcessId());
      } else if (instr.operands[0].size == 64) {
        mmu.WriteVirtual(addr, &value, 8, GetProcessId());
      }
    } else if (instr.operands[0].type ==
               DecodedInstruction::Operand::Type::FPU) {
      uint8_t st_reg = static_cast<uint8_t>(instr.operands[0].reg) -
                       static_cast<uint8_t>(Register::ST0);
      uint8_t stack_idx = fpuState.GetStackIndex(st_reg);
      fpuState.st[stack_idx] = value;
    }

    // FSTP pops the stack, FST does not
    if (instr.instType == InstructionType::Fstp) {
      fpuState.PopStack();
    }
    break;
  }

  case InstructionType::Fadd: {
    // FADD/FADDP/FIADD
    if (instr.operandCount == 0) {
      // FADD ST(0), ST(1)
      uint8_t st0_idx = fpuState.GetStackIndex(0);
      uint8_t st1_idx = fpuState.GetStackIndex(1);
      fpuState.st[st0_idx] = fpuState.st[st0_idx] + fpuState.st[st1_idx];
    } else if (instr.operands[0].type ==
               DecodedInstruction::Operand::Type::FPU) {
      // FADD ST(0), ST(i)
      uint8_t st_reg = static_cast<uint8_t>(instr.operands[0].reg) -
                       static_cast<uint8_t>(Register::ST0);
      uint8_t st0_idx = fpuState.GetStackIndex(0);
      uint8_t sti_idx = fpuState.GetStackIndex(st_reg);
      fpuState.st[st0_idx] = fpuState.st[st0_idx] + fpuState.st[sti_idx];
    } else if (instr.operands[0].type ==
               DecodedInstruction::Operand::Type::MEMORY) {
      // FADD m32fp/m64fp
      uint64_t addr = CalculateMemoryAddress(instr.operands[0]);
      double value;
      if (instr.operands[0].size == 32) {
        float fval;
        mmu.ReadVirtual(addr, &fval, 4, GetProcessId());
        value = static_cast<double>(fval);
      } else {
        mmu.ReadVirtual(addr, &value, 8, GetProcessId());
      }
      uint8_t st0_idx = fpuState.GetStackIndex(0);
      fpuState.st[st0_idx] = fpuState.st[st0_idx] + value;
    }
    break;
  }
  case InstructionType::Fmul: {
    // Similar implementation to FADD
    break;
  }
  case InstructionType::Fcom: {
    // Floating-point compare
    double st0 = fpuState.st[0];
    double operand = 0.0;

    if (instr.operandCount == 0) {
      operand = fpuState.st[1];
    } else {
      uint8_t st_index = static_cast<uint8_t>(instr.operands[0].reg) -
                         static_cast<uint8_t>(Register::ST0);
      operand = fpuState.st[st_index];
    }

    // Clear condition codes
    fpuState.statusWord &= ~0x4500;

    if (std::isnan(st0) || std::isnan(operand)) {
      fpuState.statusWord |= 0x4500; // Invalid operation
    } else if (st0 == operand) {
      fpuState.statusWord |= 0x4000; // C3 (equal)
    } else if (st0 < operand) {
      fpuState.statusWord |= 0x0100; // C0 (less than)
    } else {
      // C2 and C3 cleared for greater than
    }
    break;
  }
  case InstructionType::Fsin: {
    if (std::fabs(fpuState.st[0]) > 2.0 * M_PI) {
      fpuState.statusWord |= 0x02; // Stack error
    } else {
      fpuState.st[0] = std::sin(fpuState.st[0]);
    }
    break;
  }
  case InstructionType::Fcos: {
    if (std::fabs(fpuState.st[0]) > 2.0 * M_PI) {
      fpuState.statusWord |= 0x02; // Stack error
    } else {
      fpuState.st[0] = std::cos(fpuState.st[0]);
    }
    break;
  }
  case InstructionType::Fsincos: {
    if (fpuState.IsStackFull()) {
      fpuState.SetStackOverflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    uint8_t st0_idx = fpuState.GetStackIndex(0);
    double angle = fpuState.st[st0_idx];

    if (std::fabs(angle) > (1ULL << 63)) { // Large angle check
      fpuState.statusWord |= 0x0400;       // C2 set for incomplete reduction
    } else {
      double sin_val = std::sin(angle);
      double cos_val = std::cos(angle);

      // Replace ST(0) with sine
      fpuState.st[st0_idx] = sin_val;

      // Push cosine onto stack
      fpuState.PushStack(cos_val);

      // Clear C2 to indicate complete operation
      fpuState.statusWord &= ~0x0400;
    }
    break;
  }
  case InstructionType::Fprem: {
    // Partial remainder (IEEE 754 compatible)
    if (fpuState.IsStackEmpty()) {
      fpuState.SetStackUnderflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    uint8_t st0_idx = fpuState.GetStackIndex(0);
    uint8_t st1_idx = fpuState.GetStackIndex(1);
    double dividend = fpuState.st[st0_idx];
    double divisor = fpuState.st[st1_idx];

    if (divisor == 0.0) {
      // Division by zero
      fpuState.statusWord |= 0x0004; // Set D flag (Divide by Zero)
      fpuState.st[st0_idx] = std::copysign(INFINITY, dividend); // Result is ±∞
    } else if (std::isnan(dividend) || std::isnan(divisor)) {
      // NaN operand
      fpuState.statusWord |= 0x0001; // Set IE flag (Invalid Operation)
      fpuState.st[st0_idx] = std::numeric_limits<double>::quiet_NaN();
    } else if (std::isinf(dividend)) {
      // Infinity / finite
      fpuState.statusWord |= 0x0001; // Set IE flag (Invalid Operation)
      fpuState.st[st0_idx] = std::numeric_limits<double>::quiet_NaN();
    } else {
      // Normal case: compute partial remainder
      double temp = dividend / divisor;
      int n = static_cast<int>(temp);

      // Calculate remainder
      double remainder = dividend - (n * divisor);
      fpuState.st[st0_idx] = remainder;

      // Set condition code bits C0, C1, C3 based on quotient bits
      fpuState.statusWord &= ~0x4500;         // Clear C0, C1, C3
      fpuState.statusWord |= ((n & 1) << 8);  // C0 = Q0
      fpuState.statusWord |= ((n & 2) << 13); // C3 = Q1
      fpuState.statusWord |= ((n & 4) << 12); // C1 = Q2

      // Clear C2 to indicate complete operation
      fpuState.statusWord &= ~0x0400;
    }
    break;
  }
  case InstructionType::Fscale: {
    // Scale by power of two
    double exponent = std::floor(fpuState.st[1]);
    if (exponent > 1024 || exponent < -1024) {
      fpuState.statusWord |= 0x01; // Invalid operation
    } else {
      fpuState.st[0] = std::scalbn(fpuState.st[0], static_cast<int>(exponent));
    }
    break;
  }
  case InstructionType::F2xm1: {
    // 2^x - 1
    if (fpuState.st[0] < -1.0 || fpuState.st[0] > 1.0) {
      fpuState.statusWord |= 0x01; // Invalid operation
    } else {
      fpuState.st[0] = std::exp2(fpuState.st[0]) - 1.0;
    }
    break;
  }
  case InstructionType::Fyl2x: {
    // y * log2(x)
    double st0 = fpuState.st[0];
    double st1 = fpuState.st[1];

    if (st0 <= 0.0) {
      fpuState.statusWord |= 0x01; // Invalid operation
    } else {
      fpuState.st[1] = st1 * std::log2(st0);
      // Pop stack
      fpuState.st[0] = fpuState.st[1];
      fpuState.statusWord =
          (fpuState.statusWord & ~0x3800) | 0x2000; // C1=0, C2=0, C3=0, TOP=1
    }
    break;
  }
  case InstructionType::Fptan: {
    // Tangent of ST(0) and push 1.0
    if (fpuState.IsStackFull()) {
      fpuState.SetStackOverflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    uint8_t st0_idx = fpuState.GetStackIndex(0);
    double angle = fpuState.st[st0_idx];

    if (std::fabs(angle) > (1ULL << 63)) { // Large angle check
      fpuState.statusWord |= 0x0400;       // C2 set for incomplete reduction
    } else {
      double tan_val = std::tan(angle);

      // Replace ST(0) with tangent
      fpuState.st[st0_idx] = tan_val;

      // Push 1.0 onto stack (required by FPTAN)
      fpuState.PushStack(1.0);

      // Clear C2 to indicate complete operation
      fpuState.statusWord &= ~0x0400;
    }
    break;
  }
  case InstructionType::Fpatan: {
    // Arctangent of ST(1)/ST(0)
    double y = fpuState.st[1];
    double x = fpuState.st[0];

    if (x == 0.0 && y == 0.0) {
      fpuState.statusWord |= 0x01; // Invalid operation
    } else {
      fpuState.st[1] = std::atan2(y, x);
      // Pop stack
      fpuState.st[0] = fpuState.st[1];
    }
    break;
  }
  case InstructionType::Fxtract: {
    // Extract exponent and significand
    if (fpuState.IsStackFull()) {
      fpuState.SetStackOverflow();
      TriggerInterrupt(EXC_MF, 0, false);
      return;
    }

    uint8_t st0_idx = fpuState.GetStackIndex(0);
    double value = fpuState.st[st0_idx];

    if (value == 0.0) {
      // Special case for zero
      fpuState.st[st0_idx] = -INFINITY; // Exponent = -∞ for zero
      fpuState.PushStack(0.0);          // Significand = 0
      fpuState.statusWord |= 0x0001;    // Set IE (Invalid Operation)
    } else {
      int bin_exp;
      double significand = std::frexp(value, &bin_exp);

      // Adjust significand to be in [1.0, 2.0) range
      significand *= 2.0;
      bin_exp -= 1;

      // Replace ST(0) with exponent
      fpuState.st[st0_idx] = static_cast<double>(bin_exp);

      // Push significand onto stack
      fpuState.PushStack(significand);
    }
    break;
  }
  default:
    spdlog::warn("X86_64CPU[{}]: Unhandled FPU instruction: {}", m_cpuId,
                 static_cast<int>(instr.instType));
    TriggerInterrupt(EXC_UD, 0, false);
    break;
  }
}

// ========================
// Advanced SIMD (AVX/AVX2)
// ========================

void X86_64CPU::ExecuteAVXInstruction(const DecodedInstruction &instr,
                                      uint64_t &nextRip) {
  // ... [previous AVX code] ...

  // Add new AVX instructions
  switch (instr.instType) {
  case InstructionType::Vaddps: {
    __m256i op1_int = ReadXmmOperandValue(instr.operands[1]);
    __m256i op2_int = ReadXmmOperandValue(instr.operands[2]);
    __m256 op1 = _mm256_castsi256_ps(op1_int);
    __m256 op2 = _mm256_castsi256_ps(op2_int);
    __m256 result = _mm256_add_ps(op1, op2);
    WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result));
    break;
  }
  case InstructionType::Vaddpd: {
    __m256i op1_int = ReadXmmOperandValue(instr.operands[1]);
    __m256i op2_int = ReadXmmOperandValue(instr.operands[2]);
    __m256d op1 = _mm256_castsi256_pd(op1_int);
    __m256d op2 = _mm256_castsi256_pd(op2_int);
    __m256d result = _mm256_add_pd(op1, op2);
    WriteXmmOperandValue(instr.operands[0], _mm256_castpd_si256(result));
    break;
  }
  case InstructionType::Vfmadd132ps: {
    // Fused multiply-add: a*b + c
    __m256i a_int = ReadXmmOperandValue(instr.operands[0]);
    __m256i b_int = ReadXmmOperandValue(instr.operands[1]);
    __m256i c_int = ReadXmmOperandValue(instr.operands[2]);
    __m256 a = _mm256_castsi256_ps(a_int);
    __m256 b = _mm256_castsi256_ps(b_int);
    __m256 c = _mm256_castsi256_ps(c_int);
    __m256 result = _mm256_fmadd_ps(a, b, c);
    WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result));
    break;
  }
  case InstructionType::Vgatherdps: {
    // Gather packed single-precision values
    __m256i vindex_256 = ReadXmmOperandValue(instr.operands[2]);
    __m256i mask_256 = ReadXmmOperandValue(instr.operands[3]);
    __m256i base_256 = ReadXmmOperandValue(instr.operands[1]);

    // Extract lower 128 bits for gather operation
    __m128i vindex = _mm256_extracti128_si256(vindex_256, 0);
    __m128i mask = _mm256_extracti128_si256(mask_256, 0);
    __m128 base = _mm_castsi128_ps(_mm256_extracti128_si256(base_256, 0));

    // Use aligned arrays to access individual elements
    alignas(16) int32_t vindex_arr[4];
    alignas(16) int32_t mask_arr[4];
    alignas(16) float base_arr[4];
    alignas(16) float result_arr[4] = {0};

    _mm_store_si128((__m128i *)vindex_arr, vindex);
    _mm_store_si128((__m128i *)mask_arr, mask);
    _mm_store_ps(base_arr, base);

    for (int i = 0; i < 4; i++) {
      if (mask_arr[i] & 0x80000000) {
        uint64_t addr = static_cast<uint64_t>(base_arr[i]) + vindex_arr[i];
        float value;
        mmu.ReadVirtual(addr, &value, 4, GetProcessId());
        result_arr[i] = value;
      }
    }

    __m128 result = _mm_load_ps(result_arr);
    WriteXmmOperandValue(instr.operands[0],
                         _mm256_zextsi128_si256(_mm_castps_si128(result)));
    break;
  }
  case InstructionType::Vperm2f128: {
    // Permute floating-point values
    __m256i a_int = ReadXmmOperandValue(instr.operands[1]);
    __m256i b_int = ReadXmmOperandValue(instr.operands[2]);
    __m256 a = _mm256_castsi256_ps(a_int);
    __m256 b = _mm256_castsi256_ps(b_int);
    int imm8 = static_cast<int>(instr.operands[3].immediate);

    __m256 result;
    switch (imm8 & 0x0F) {
    case 0x00:
      result = _mm256_permute2f128_ps(a, b, 0x00);
      break;
    case 0x01:
      result = _mm256_permute2f128_ps(a, b, 0x01);
      break;
    case 0x02:
      result = _mm256_permute2f128_ps(a, b, 0x02);
      break;
    case 0x03:
      result = _mm256_permute2f128_ps(a, b, 0x03);
      break;
    default:
      spdlog::warn("X86_64CPU[{}]: Invalid permute mask: 0x{:x}", m_cpuId,
                   imm8);
      result = _mm256_setzero_ps();
    }
    WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result));
    break;
  }
  case InstructionType::Vbroadcastss: {
    // Broadcast single-precision value
    float value;
    if (instr.operands[1].type == DecodedInstruction::Operand::Type::XMM) {
      __m256i xmm_int = ReadXmmOperandValue(instr.operands[1]);
      __m128 xmm_float = _mm_castsi128_ps(_mm256_extracti128_si256(xmm_int, 0));
      alignas(16) float temp[4];
      _mm_store_ps(temp, xmm_float);
      value = temp[0];
    } else {
      uint64_t addr = CalculateMemoryAddress(instr.operands[1]);
      mmu.ReadVirtual(addr, &value, 4, GetProcessId());
    }

    __m256 result = _mm256_set1_ps(value);
    WriteXmmOperandValue(instr.operands[0], _mm256_castps_si256(result));
    break;
  }
  case InstructionType::Vroundps: {
    // Round packed single-precision values
    __m256i input_int = ReadXmmOperandValue(instr.operands[1]);
    __m128 input = _mm_castsi128_ps(_mm256_extracti128_si256(input_int, 0));
    int rounding = static_cast<int>(instr.operands[2].immediate) & 0x7;

    __m128 result;
    switch (rounding) {
    case 0:
      result = _mm_round_ps(input, _MM_FROUND_TO_NEAREST_INT);
      break;
    case 1:
      result = _mm_round_ps(input, _MM_FROUND_TO_NEG_INF);
      break;
    case 2:
      result = _mm_round_ps(input, _MM_FROUND_TO_POS_INF);
      break;
    case 3:
      result = _mm_round_ps(input, _MM_FROUND_TO_ZERO);
      break;
    default:
      spdlog::warn("X86_64CPU[{}]: Invalid rounding mode: {}", m_cpuId,
                   rounding);
      result = input;
    }
    WriteXmmOperandValue(instr.operands[0],
                         _mm256_zextsi128_si256(_mm_castps_si128(result)));
    break;
  }
  case InstructionType::Vtestps: {
    // Logical AND and set flags
    __m256i a_int = ReadXmmOperandValue(instr.operands[0]);
    __m256i b_int = ReadXmmOperandValue(instr.operands[1]);
    __m128 a = _mm_castsi128_ps(_mm256_extracti128_si256(a_int, 0));
    __m128 b = _mm_castsi128_ps(_mm256_extracti128_si256(b_int, 0));

    __m128 and_result = _mm_and_ps(a, b);
    int mask = _mm_movemask_ps(and_result);

    // Set ZF if all bits are 0, CF if all bits are 1
    SetFlag(FLAG_ZF, mask == 0);
    SetFlag(FLAG_CF, mask == 0x0F);
    break;
  }
  case InstructionType::Vzeroupper: {
    // Zero upper bits of YMM registers
    for (auto &reg : xmmRegisters) {
      reg = _mm256_zextsi128_si256(_mm256_castsi256_si128(reg));
    }
    break;
  }
  default:
    spdlog::warn("X86_64CPU[{}]: Unhandled AVX instruction: {}", m_cpuId,
                 static_cast<int>(instr.instType));
    TriggerInterrupt(EXC_UD, 0, false);
    break;
  }
}

// ========================
// System Management Mode (SMM)
// ========================

void X86_64CPU::EnterSMM(SMMInterruptType type) {
  if (inSMM) {
    spdlog::warn("X86_64CPU[{}]: Already in SMM, ignoring SMM entry", m_cpuId);
    return;
  }

  // Check if SMM is enabled (simplified check)
  // In real implementation, this would check SMRR and other SMM configuration
  if (smbase == 0) {
    spdlog::error("X86_64CPU[{}]: SMM entry attempted with invalid SMBASE",
                  m_cpuId);
    return;
  }

  spdlog::info("X86_64CPU[{}]: Entering SMM, type=0x{:x}, SMBASE=0x{:x}",
               m_cpuId, static_cast<uint8_t>(type), smbase);

  // Record SMM entry time and statistics
  smmEntryTime = std::chrono::high_resolution_clock::now();
  smmEntryCount++;
  smmInterruptType = static_cast<uint8_t>(type);

  // Check for special SMI conditions
  switch (type) {
  case SMMInterruptType::SMI:
    spdlog::debug("X86_64CPU[{}]: Processing standard SMI", m_cpuId);
    break;
  case SMMInterruptType::NMI:
    spdlog::debug("X86_64CPU[{}]: Processing NMI in SMM context", m_cpuId);
    break;
  case SMMInterruptType::MACHINE_CHECK:
    spdlog::debug("X86_64CPU[{}]: Processing Machine Check in SMM", m_cpuId);
    break;
  case SMMInterruptType::THERMAL:
    spdlog::debug("X86_64CPU[{}]: Processing Thermal interrupt in SMM",
                  m_cpuId);
    break;
  case SMMInterruptType::PERFORMANCE:
    spdlog::debug("X86_64CPU[{}]: Processing Performance interrupt in SMM",
                  m_cpuId);
    break;
  default:
    spdlog::warn("X86_64CPU[{}]: Unknown SMI type 0x{:x}", m_cpuId,
                 static_cast<uint8_t>(type));
    break;
  }

  // Save complete CPU state to SMM saved state structure
  SaveSMMState();

  // Write SMM state to SMRAM
  WriteSMMStateToMemory();

  // Set up SMM execution environment according to x86_64 specification

  // 1. Clear interrupt flag and other critical flags
  uint64_t currentRflags = GetRflags();
  uint64_t newRflags = currentRflags & ~(FLAG_IF | FLAG_DF | FLAG_RF | FLAG_TF |
                                         FLAG_AC | FLAG_NT);
  SetRflags(newRflags);

  // 2. Set up SMM segment registers (16-bit real mode like environment)
  // CS base = SMBASE, other segments = 0
  uint16_t cs_selector = static_cast<uint16_t>(smbase >> 4);
  SetCS(cs_selector);
  SetDS(0x0000);
  SetES(0x0000);
  SetFS(0x0000);
  SetGS(0x0000);
  SetSS(0x0000);

  // 3. Set up SMM control registers
  // CR0: Clear PE, PG, and other mode bits to enter real mode-like state
  uint64_t currentCR0 = GetCR0();
  uint64_t smmCR0 =
      currentCR0 & ~(0x1 | 0x80000000 | 0x40000000); // Clear PE, PG, CD
  smmCR0 |= 0x20000000; // Set NW (Not Write-through)
  SetCR0(smmCR0);

  // 4. Set SMM entry point and stack
  SetRegister(Register::RIP, 0x8000); // SMM handler offset within SMBASE
  SetRegister(Register::RSP,
              smbase + 0x8000 - 0x200); // Stack grows down from near top

  // 5. Initialize SMM-specific state
  smmIoRestart = false;
  smmAutoHaltRestart = false;

  // 6. Set SMM flag to indicate we're now in SMM
  inSMM = true;

  // 7. Handle I/O instruction restart if this SMI was triggered during I/O
  if (type == SMMInterruptType::SMI) {
    // Check if the interrupted instruction was an I/O instruction
    // This is a simplified check - real implementation would be more
    // sophisticated
    uint64_t currentRIP = smmSavedState.rip;
    try {
      uint8_t opcode;
      mmu.ReadVirtual(currentRIP, &opcode, 1, GetProcessId());
      if (opcode == 0xEC || opcode == 0xED || opcode == 0xEE ||
          opcode == 0xEF || // IN/OUT
          opcode == 0x6C || opcode == 0x6D || opcode == 0x6E ||
          opcode == 0x6F) { // INS/OUTS
        smmIoRestart = true;
        spdlog::debug("X86_64CPU[{}]: I/O instruction detected at RIP 0x{:x}, "
                      "enabling restart",
                      m_cpuId, currentRIP);
      }
    } catch (const std::exception &e) {
      spdlog::debug(
          "X86_64CPU[{}]: Could not check interrupted instruction: {}", m_cpuId,
          e.what());
    }
  }

  // 8. Handle auto-halt restart if CPU was in HLT state
  if (halted) {
    smmAutoHaltRestart = true;
    halted = false; // Clear halt state
    spdlog::debug("X86_64CPU[{}]: CPU was halted, enabling auto-halt restart",
                  m_cpuId);
  }

  spdlog::info(
      "X86_64CPU[{}]: Successfully entered SMM, handler at 0x{:x}:0x8000",
      m_cpuId, smbase);
}

void X86_64CPU::ExitSMM() {
  if (!inSMM) {
    spdlog::warn("X86_64CPU[{}]: Not in SMM, ignoring SMM exit", m_cpuId);
    return;
  }

  spdlog::info("X86_64CPU[{}]: Exiting SMM", m_cpuId);

  // Record SMM exit time and update statistics
  smmExitTime = std::chrono::high_resolution_clock::now();
  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                      smmExitTime - smmEntryTime)
                      .count();
  smmTotalTime += duration;

  // Update SMM saved state with exit time and statistics
  smmSavedState.last_smm_exit_time =
      std::chrono::duration_cast<std::chrono::nanoseconds>(
          smmExitTime.time_since_epoch())
          .count();
  smmSavedState.smm_total_time = smmTotalTime;

  // Read potentially updated state from SMRAM
  // SMM handler may have modified the saved state
  try {
    ReadSMMStateFromMemory();
    spdlog::debug("X86_64CPU[{}]: Read updated SMM state from SMRAM", m_cpuId);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Failed to read SMM state from SMRAM: {}",
                  m_cpuId, e.what());
    // Continue with exit using current state
  }

  // Check if SMBASE was updated during SMM
  uint64_t newSMBASE = smmSavedState.smbase;
  if (newSMBASE != smbase && newSMBASE != 0) {
    // Validate new SMBASE (must be page-aligned and in valid range)
    if ((newSMBASE & 0xFFF) == 0 && newSMBASE >= 0x30000 &&
        newSMBASE < 0x100000000ULL) {
      spdlog::info("X86_64CPU[{}]: SMBASE updated from 0x{:x} to 0x{:x}",
                   m_cpuId, smbase, newSMBASE);
      smbase = newSMBASE;
    } else {
      spdlog::warn("X86_64CPU[{}]: Invalid SMBASE 0x{:x} ignored", m_cpuId,
                   newSMBASE);
      smmSavedState.smbase = smbase; // Restore valid SMBASE
    }
  }

  // Check for SMM handler modifications to restart flags
  bool autoHaltRestart = smmSavedState.auto_halt_restart != 0;
  bool ioRestart = smmSavedState.io_restart_flag != 0;

  if (autoHaltRestart != smmAutoHaltRestart) {
    spdlog::debug("X86_64CPU[{}]: Auto-halt restart flag changed to {}",
                  m_cpuId, autoHaltRestart);
    smmAutoHaltRestart = autoHaltRestart;
  }

  if (ioRestart != smmIoRestart) {
    spdlog::debug("X86_64CPU[{}]: I/O restart flag changed to {}", m_cpuId,
                  ioRestart);
    smmIoRestart = ioRestart;
  }

  // Restore CPU state from SMM saved state
  RestoreSMMState();

  // Handle special restart conditions

  // 1. Handle auto-halt restart if enabled
  if (smmAutoHaltRestart) {
    // If CPU was in HALT before SMM, re-enter HALT
    // Adjust RIP to point back to the HLT instruction
    uint64_t currentRIP = GetRegister(Register::RIP);
    SetRegister(Register::RIP, currentRIP - 1); // Point to HLT instruction
    halted = true;                              // Set halt state
    spdlog::debug(
        "X86_64CPU[{}]: Auto-halt restart enabled, returning to HALT at 0x{:x}",
        m_cpuId, currentRIP - 1);
  }

  // 2. Handle I/O instruction restart if enabled
  if (smmIoRestart) {
    // Restore registers and RIP to restart the I/O instruction
    if (smmSavedState.io_restart_rip64 != 0) {
      SetRegister(Register::RIP, smmSavedState.io_restart_rip64);
      SetRegister(Register::RCX, smmSavedState.io_restart_rcx64);
      SetRegister(Register::RSI, smmSavedState.io_restart_rsi64);
      SetRegister(Register::RDI, smmSavedState.io_restart_rdi64);
      spdlog::debug("X86_64CPU[{}]: I/O instruction restart enabled, returning "
                    "to I/O at 0x{:x}",
                    m_cpuId, smmSavedState.io_restart_rip64);
    } else {
      spdlog::warn(
          "X86_64CPU[{}]: I/O restart requested but no restart RIP available",
          m_cpuId);
    }
  }

  // 3. Clear SMM state and flags
  inSMM = false;
  smmIoRestart = false;
  smmAutoHaltRestart = false;
  smmInterruptType = 0;

  // 4. Re-enable interrupts if they were enabled before SMM
  // This is handled by RestoreSMMState() which restores RFLAGS

  // 5. Flush any SMM-specific TLB entries or caches if needed
  // In a real implementation, this might involve cache/TLB management

  spdlog::info("X86_64CPU[{}]: Successfully exited SMM, duration={} μs, total "
               "SMM time={} μs",
               m_cpuId, duration, smmTotalTime);
}

// ========================
// Virtualization (VMX)
// ========================

void X86_64CPU::VMXON(uint64_t region) {
  // Check if already in VMX operation
  if (vmxEnabled) {
    spdlog::warn("X86_64CPU[{}]: VMXON executed while already in VMX operation",
                 m_cpuId);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check privilege level (must be CPL 0)
  if (GetCPL() != 0) {
    spdlog::warn("X86_64CPU[{}]: VMXON executed at CPL {}, requires CPL 0",
                 m_cpuId, GetCPL());
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check if in SMM (VMXON not allowed in SMM)
  if (inSMM) {
    spdlog::warn("X86_64CPU[{}]: VMXON executed in SMM, not allowed", m_cpuId);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check CR4.VMXE (VMX Enable bit)
  if (!(GetCR4() & (1ULL << 13))) {
    spdlog::warn("X86_64CPU[{}]: VMXON executed with CR4.VMXE=0", m_cpuId);
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Check CR0 fixed bits for VMX operation
  uint64_t cr0 = GetCR0();
  // CR0.PE and CR0.PG must be 1, CR0.CD and CR0.NW should be 0 for VMX
  if (!(cr0 & 0x1) || !(cr0 & 0x80000000)) {
    spdlog::warn(
        "X86_64CPU[{}]: VMXON executed with invalid CR0 (PE={}, PG={})",
        m_cpuId, (cr0 & 0x1) ? 1 : 0, (cr0 & 0x80000000) ? 1 : 0);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check that VMXON region is page-aligned
  if (region & 0xFFF) {
    spdlog::warn("X86_64CPU[{}]: VMXON region 0x{:x} is not page-aligned",
                 m_cpuId, region);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check that VMXON region is in valid memory range
  if (region == 0 || region >= 0x100000000ULL) { // Simplified check
    spdlog::warn("X86_64CPU[{}]: VMXON region 0x{:x} is invalid", m_cpuId,
                 region);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Read and validate VMXON region revision ID
  uint32_t revision;
  try {
    mmu.ReadVirtual(region, &revision, 4, GetProcessId());

    // Check revision ID (bits 30:0), bit 31 should be 0 for VMXON region
    if ((revision & 0x7FFFFFFF) != VMX_REVISION_ID || (revision & 0x80000000)) {
      spdlog::warn("X86_64CPU[{}]: VMXON region has invalid revision ID "
                   "0x{:x}, expected 0x{:x}",
                   m_cpuId, revision, VMX_REVISION_ID);
      TriggerInterrupt(EXC_GP, 0, false);
      return;
    }
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: VMXON failed to read region revision: {}",
                  m_cpuId, e.what());
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Initialize VMX operation
  vmxonRegion = region;
  vmxEnabled = true;
  inVMXNonRootOperation = false;
  currentVMCS = 0;

  // Clear VMCS state
  vmcs = {};
  vmcsState = {};

  spdlog::info("X86_64CPU[{}]: VMXON executed successfully, region=0x{:x}, "
               "revision=0x{:x}",
               m_cpuId, region, revision);
}

void X86_64CPU::VMCLEAR(uint64_t region) {
  if (!vmxEnabled) {
    spdlog::warn("X86_64CPU[{}]: VMCLEAR executed while not in VMX operation",
                 m_cpuId);
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Check privilege level (must be CPL 0)
  if (GetCPL() != 0) {
    spdlog::warn("X86_64CPU[{}]: VMCLEAR executed at CPL {}, requires CPL 0",
                 m_cpuId, GetCPL());
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check if region is page-aligned
  if (region & 0xFFF) {
    spdlog::warn("X86_64CPU[{}]: VMCLEAR region 0x{:x} is not page-aligned",
                 m_cpuId, region);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check that VMCLEAR region is not the VMXON region
  if (region == vmxonRegion) {
    spdlog::warn("X86_64CPU[{}]: VMCLEAR attempted on VMXON region 0x{:x}",
                 m_cpuId, region);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check that region is in valid memory range
  if (region == 0 || region >= 0x100000000ULL) { // Simplified check
    spdlog::warn("X86_64CPU[{}]: VMCLEAR region 0x{:x} is invalid", m_cpuId,
                 region);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Validate VMCS revision ID
  uint32_t revision;
  try {
    mmu.ReadVirtual(region, &revision, 4, GetProcessId());

    // Check revision ID (bits 30:0), bit 31 can be 0 or 1 for VMCS
    if ((revision & 0x7FFFFFFF) != VMX_REVISION_ID) {
      spdlog::warn("X86_64CPU[{}]: VMCLEAR region has invalid revision ID "
                   "0x{:x}, expected 0x{:x}",
                   m_cpuId, revision, VMX_REVISION_ID);
      TriggerInterrupt(EXC_GP, 0, false);
      return;
    }
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: VMCLEAR failed to read region revision: {}",
                  m_cpuId, e.what());
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // If this is the current VMCS, save state and clear current pointer
  if (region == currentVMCS) {
    try {
      // Save current VMCS state to memory
      mmu.WriteVirtual(region, &vmcs, sizeof(VMCS), GetProcessId());

      // Clear the current VMCS pointer
      currentVMCS = 0;

      spdlog::debug("X86_64CPU[{}]: Saved and cleared current VMCS at 0x{:x}",
                    m_cpuId, region);
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: VMCLEAR failed to save VMCS: {}", m_cpuId,
                    e.what());
      TriggerInterrupt(EXC_GP, 0, false);
      return;
    }
  }

  // Clear the VMCS launch state by setting abort indicator to 0
  try {
    uint32_t clear_indicator = 0;
    mmu.WriteVirtual(region + 4, &clear_indicator, 4,
                     GetProcessId()); // Write to abort indicator field

    spdlog::info("X86_64CPU[{}]: VMCLEAR executed successfully, region=0x{:x}, "
                 "revision=0x{:x}",
                 m_cpuId, region, revision);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: VMCLEAR failed to clear launch state: {}",
                  m_cpuId, e.what());
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Clear any cached VMCS state for this region
  if (region == currentVMCS) {
    vmcs = {};
    vmcsState = {};
  }
}

void X86_64CPU::VMPTRLD(uint64_t region) {
  if (!vmxEnabled) {
    spdlog::warn("X86_64CPU[{}]: VMPTRLD executed while not in VMX operation",
                 m_cpuId);
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Check privilege level (must be CPL 0)
  if (GetCPL() != 0) {
    spdlog::warn("X86_64CPU[{}]: VMPTRLD executed at CPL {}, requires CPL 0",
                 m_cpuId, GetCPL());
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check if region is page-aligned
  if (region & 0xFFF) {
    spdlog::warn("X86_64CPU[{}]: VMPTRLD region 0x{:x} is not page-aligned",
                 m_cpuId, region);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check that VMPTRLD region is not the VMXON region
  if (region == vmxonRegion) {
    spdlog::warn("X86_64CPU[{}]: VMPTRLD attempted on VMXON region 0x{:x}",
                 m_cpuId, region);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check that region is in valid memory range
  if (region == 0 || region >= 0x100000000ULL) { // Simplified check
    spdlog::warn("X86_64CPU[{}]: VMPTRLD region 0x{:x} is invalid", m_cpuId,
                 region);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Save current VMCS if there is one
  if (currentVMCS != 0 && currentVMCS != region) {
    try {
      mmu.WriteVirtual(currentVMCS, &vmcs, sizeof(VMCS), GetProcessId());
      spdlog::debug("X86_64CPU[{}]: Saved previous VMCS at 0x{:x}", m_cpuId,
                    currentVMCS);
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: VMPTRLD failed to save previous VMCS: {}",
                    m_cpuId, e.what());
      TriggerInterrupt(EXC_GP, 0, false);
      return;
    }
  }

  // Validate and load new VMCS
  uint32_t revision;
  try {
    mmu.ReadVirtual(region, &revision, 4, GetProcessId());

    // Check revision ID (bits 30:0), bit 31 can be 0 or 1 for VMCS
    if ((revision & 0x7FFFFFFF) != VMX_REVISION_ID) {
      spdlog::warn("X86_64CPU[{}]: VMPTRLD region has invalid revision ID "
                   "0x{:x}, expected 0x{:x}",
                   m_cpuId, revision, VMX_REVISION_ID);
      TriggerInterrupt(EXC_GP, 0, false);
      return;
    }

    // Load VMCS data from memory
    mmu.ReadVirtual(region, &vmcs, sizeof(VMCS), GetProcessId());

    // Set as current VMCS
    currentVMCS = region;

    spdlog::info("X86_64CPU[{}]: VMPTRLD executed successfully, region=0x{:x}, "
                 "revision=0x{:x}",
                 m_cpuId, region, revision);

  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: VMPTRLD failed to load VMCS: {}", m_cpuId,
                  e.what());
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }
}

void X86_64CPU::VMLAUNCH() {
  if (!vmxEnabled || currentVMCS == 0) {
    spdlog::warn(
        "X86_64CPU[{}]: VMLAUNCH executed without valid VMX/VMCS state",
        m_cpuId);
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Check privilege level (must be CPL 0)
  if (GetCPL() != 0) {
    spdlog::warn("X86_64CPU[{}]: VMLAUNCH executed at CPL {}, requires CPL 0",
                 m_cpuId, GetCPL());
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check if VMCS is in the correct state for VMLAUNCH (not launched before)
  if (vmcs.abort_indicator != 0) {
    spdlog::warn("X86_64CPU[{}]: VMLAUNCH attempted on already launched VMCS",
                 m_cpuId);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  spdlog::info("X86_64CPU[{}]: Executing VMLAUNCH", m_cpuId);

  // Perform comprehensive guest state validation
  if (!ValidateGuestState()) {
    spdlog::error("X86_64CPU[{}]: VMLAUNCH failed guest state validation",
                  m_cpuId);
    VMExit(
        static_cast<uint32_t>(VMExitReason::ENTRY_FAILURE_INVALID_GUEST_STATE),
        0);
    return;
  }

  // Save complete host state
  SaveHostState();

  // Process VM entry controls
  if (!ProcessVMEntryControls()) {
    spdlog::error("X86_64CPU[{}]: VMLAUNCH failed VM entry controls processing",
                  m_cpuId);
    VMExit(static_cast<uint32_t>(VMExitReason::ENTRY_FAILURE_MSR_LOADING), 0);
    return;
  }

  // Load guest state
  LoadGuestState();

  // Mark VMCS as launched
  vmcs.abort_indicator = 1;

  // Enter VMX non-root operation
  inVMXNonRootOperation = true;

  spdlog::info("X86_64CPU[{}]: VMLAUNCH successful, entered VMX non-root "
               "operation at RIP=0x{:x}",
               m_cpuId, vmcs.guest_rip);
}

void X86_64CPU::VMRESUME() {
  if (!vmxEnabled || currentVMCS == 0) {
    spdlog::warn(
        "X86_64CPU[{}]: VMRESUME executed without valid VMX/VMCS state",
        m_cpuId);
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Check privilege level (must be CPL 0)
  if (GetCPL() != 0) {
    spdlog::warn("X86_64CPU[{}]: VMRESUME executed at CPL {}, requires CPL 0",
                 m_cpuId, GetCPL());
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Check if VMCS is in the correct state for VMRESUME (must be launched
  // before)
  if (vmcs.abort_indicator == 0) {
    spdlog::warn("X86_64CPU[{}]: VMRESUME attempted on non-launched VMCS",
                 m_cpuId);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  spdlog::info("X86_64CPU[{}]: Executing VMRESUME", m_cpuId);

  // Perform guest state validation (similar to VMLAUNCH but may be less strict)
  if (!ValidateGuestState()) {
    spdlog::error("X86_64CPU[{}]: VMRESUME failed guest state validation",
                  m_cpuId);
    VMExit(
        static_cast<uint32_t>(VMExitReason::ENTRY_FAILURE_INVALID_GUEST_STATE),
        0);
    return;
  }

  // Process VM entry controls
  if (!ProcessVMEntryControls()) {
    spdlog::error("X86_64CPU[{}]: VMRESUME failed VM entry controls processing",
                  m_cpuId);
    VMExit(static_cast<uint32_t>(VMExitReason::ENTRY_FAILURE_MSR_LOADING), 0);
    return;
  }

  // Load guest state (host state is already saved from previous VM entry)
  LoadGuestState();

  // Enter VMX non-root operation
  inVMXNonRootOperation = true;

  spdlog::info("X86_64CPU[{}]: VMRESUME successful, resumed VMX non-root "
               "operation at RIP=0x{:x}",
               m_cpuId, vmcs.guest_rip);
}

void X86_64CPU::VMREAD(uint64_t field, uint64_t &value) {
  if (!vmxEnabled || currentVMCS == 0) {
    spdlog::warn("X86_64CPU[{}]: VMREAD executed without valid VMCS", m_cpuId);
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Check privilege level (must be CPL 0)
  if (GetCPL() != 0) {
    spdlog::warn("X86_64CPU[{}]: VMREAD executed at CPL {}, requires CPL 0",
                 m_cpuId, GetCPL());
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Get field from VMCS
  VMCSField vmcsField = static_cast<VMCSField>(field);

  switch (vmcsField) {
  // Guest state fields
  case VMCSField::GUEST_RIP:
    value = vmcs.guest_rip;
    break;
  case VMCSField::GUEST_RSP:
    value = vmcs.guest_rsp;
    break;
  case VMCSField::GUEST_RFLAGS:
    value = vmcs.guest_rflags;
    break;
  case VMCSField::GUEST_CR0:
    value = vmcs.guest_cr0;
    break;
  case VMCSField::GUEST_CR3:
    value = vmcs.guest_cr3;
    break;
  case VMCSField::GUEST_CR4:
    value = vmcs.guest_cr4;
    break;
  case VMCSField::GUEST_DR7:
    value = vmcs.guest_dr7;
    break;
  case VMCSField::GUEST_ES_SELECTOR:
    value = vmcs.guest_es_selector;
    break;
  case VMCSField::GUEST_CS_SELECTOR:
    value = vmcs.guest_cs_selector;
    break;
  case VMCSField::GUEST_SS_SELECTOR:
    value = vmcs.guest_ss_selector;
    break;
  case VMCSField::GUEST_DS_SELECTOR:
    value = vmcs.guest_ds_selector;
    break;
  case VMCSField::GUEST_FS_SELECTOR:
    value = vmcs.guest_fs_selector;
    break;
  case VMCSField::GUEST_GS_SELECTOR:
    value = vmcs.guest_gs_selector;
    break;
  case VMCSField::GUEST_LDTR_SELECTOR:
    value = vmcs.guest_ldtr_selector;
    break;
  case VMCSField::GUEST_TR_SELECTOR:
    value = vmcs.guest_tr_selector;
    break;
  case VMCSField::GUEST_ES_BASE:
    value = vmcs.guest_es_base;
    break;
  case VMCSField::GUEST_CS_BASE:
    value = vmcs.guest_cs_base;
    break;
  case VMCSField::GUEST_SS_BASE:
    value = vmcs.guest_ss_base;
    break;
  case VMCSField::GUEST_DS_BASE:
    value = vmcs.guest_ds_base;
    break;
  case VMCSField::GUEST_FS_BASE:
    value = vmcs.guest_fs_base;
    break;
  case VMCSField::GUEST_GS_BASE:
    value = vmcs.guest_gs_base;
    break;
  case VMCSField::GUEST_LDTR_BASE:
    value = vmcs.guest_ldtr_base;
    break;
  case VMCSField::GUEST_TR_BASE:
    value = vmcs.guest_tr_base;
    break;
  case VMCSField::GUEST_GDTR_BASE:
    value = vmcs.guest_gdtr_base;
    break;
  case VMCSField::GUEST_IDTR_BASE:
    value = vmcs.guest_idtr_base;
    break;
  case VMCSField::GUEST_ES_LIMIT:
    value = vmcs.guest_es_limit;
    break;
  case VMCSField::GUEST_CS_LIMIT:
    value = vmcs.guest_cs_limit;
    break;
  case VMCSField::GUEST_SS_LIMIT:
    value = vmcs.guest_ss_limit;
    break;
  case VMCSField::GUEST_DS_LIMIT:
    value = vmcs.guest_ds_limit;
    break;
  case VMCSField::GUEST_FS_LIMIT:
    value = vmcs.guest_fs_limit;
    break;
  case VMCSField::GUEST_GS_LIMIT:
    value = vmcs.guest_gs_limit;
    break;
  case VMCSField::GUEST_LDTR_LIMIT:
    value = vmcs.guest_ldtr_limit;
    break;
  case VMCSField::GUEST_TR_LIMIT:
    value = vmcs.guest_tr_limit;
    break;
  case VMCSField::GUEST_GDTR_LIMIT:
    value = vmcs.guest_gdtr_limit;
    break;
  case VMCSField::GUEST_IDTR_LIMIT:
    value = vmcs.guest_idtr_limit;
    break;
  case VMCSField::GUEST_ES_AR_BYTES:
    value = vmcs.guest_es_access_rights;
    break;
  case VMCSField::GUEST_CS_AR_BYTES:
    value = vmcs.guest_cs_access_rights;
    break;
  case VMCSField::GUEST_SS_AR_BYTES:
    value = vmcs.guest_ss_access_rights;
    break;
  case VMCSField::GUEST_DS_AR_BYTES:
    value = vmcs.guest_ds_access_rights;
    break;
  case VMCSField::GUEST_FS_AR_BYTES:
    value = vmcs.guest_fs_access_rights;
    break;
  case VMCSField::GUEST_GS_AR_BYTES:
    value = vmcs.guest_gs_access_rights;
    break;
  case VMCSField::GUEST_LDTR_AR_BYTES:
    value = vmcs.guest_ldtr_access_rights;
    break;
  case VMCSField::GUEST_TR_AR_BYTES:
    value = vmcs.guest_tr_access_rights;
    break;

  // Host state fields
  case VMCSField::HOST_CR0:
    value = vmcs.host_cr0;
    break;
  case VMCSField::HOST_CR3:
    value = vmcs.host_cr3;
    break;
  case VMCSField::HOST_CR4:
    value = vmcs.host_cr4;
    break;
  case VMCSField::HOST_RIP:
    value = vmcs.host_rip;
    break;
  case VMCSField::HOST_RSP:
    value = vmcs.host_rsp;
    break;
  case VMCSField::HOST_ES_SELECTOR:
    value = vmcs.host_es_selector;
    break;
  case VMCSField::HOST_CS_SELECTOR:
    value = vmcs.host_cs_selector;
    break;
  case VMCSField::HOST_SS_SELECTOR:
    value = vmcs.host_ss_selector;
    break;
  case VMCSField::HOST_DS_SELECTOR:
    value = vmcs.host_ds_selector;
    break;
  case VMCSField::HOST_FS_SELECTOR:
    value = vmcs.host_fs_selector;
    break;
  case VMCSField::HOST_GS_SELECTOR:
    value = vmcs.host_gs_selector;
    break;
  case VMCSField::HOST_TR_SELECTOR:
    value = vmcs.host_tr_selector;
    break;
  case VMCSField::HOST_FS_BASE:
    value = vmcs.host_fs_base;
    break;
  case VMCSField::HOST_GS_BASE:
    value = vmcs.host_gs_base;
    break;
  case VMCSField::HOST_TR_BASE:
    value = vmcs.host_tr_base;
    break;
  case VMCSField::HOST_GDTR_BASE:
    value = vmcs.host_gdtr_base;
    break;
  case VMCSField::HOST_IDTR_BASE:
    value = vmcs.host_idtr_base;
    break;

  // Control fields
  case VMCSField::PIN_BASED_VM_EXEC_CONTROL:
    value = vmcs.pin_based_vm_exec_control;
    break;
  case VMCSField::CPU_BASED_VM_EXEC_CONTROL:
    value = vmcs.cpu_based_vm_exec_control;
    break;
  case VMCSField::SECONDARY_VM_EXEC_CONTROL:
    value = vmcs.secondary_vm_exec_control;
    break;
  case VMCSField::EXCEPTION_BITMAP:
    value = vmcs.exception_bitmap;
    break;
  case VMCSField::VM_EXIT_CONTROLS:
    value = vmcs.vm_exit_controls;
    break;
  case VMCSField::VM_ENTRY_CONTROLS:
    value = vmcs.vm_entry_controls;
    break;
  case VMCSField::CR3_TARGET_COUNT:
    value = vmcs.cr3_target_count;
    break;
  case VMCSField::CR3_TARGET_VALUE0:
    value = vmcs.cr3_target_value0;
    break;
  case VMCSField::CR3_TARGET_VALUE1:
    value = vmcs.cr3_target_value1;
    break;
  case VMCSField::CR3_TARGET_VALUE2:
    value = vmcs.cr3_target_value2;
    break;
  case VMCSField::CR3_TARGET_VALUE3:
    value = vmcs.cr3_target_value3;
    break;

  // VM exit information fields (read-only)
  case VMCSField::VM_EXIT_REASON:
    value = vmcs.vm_exit_reason;
    break;
  case VMCSField::EXIT_QUALIFICATION:
    value = vmcs.vm_exit_qualification;
    break;
  case VMCSField::GUEST_LINEAR_ADDRESS:
    value = vmcs.guest_linear_address;
    break;
  case VMCSField::VM_EXIT_INTERRUPTION_INFO:
    value = vmcs.vm_exit_intr_info;
    break;
  case VMCSField::VM_EXIT_INTERRUPTION_ERROR_CODE:
    value = vmcs.vm_exit_intr_error_code;
    break;
  case VMCSField::VM_EXIT_INSTRUCTION_LEN:
    value = vmcs.vm_exit_instruction_len;
    break;
  case VMCSField::VMX_INSTRUCTION_INFO:
    value = vmcs.vm_exit_instruction_info;
    break;

  default:
    spdlog::warn("X86_64CPU[{}]: VMREAD with unsupported field 0x{:x}", m_cpuId,
                 static_cast<uint64_t>(vmcsField));
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  spdlog::trace("X86_64CPU[{}]: VMREAD field=0x{:x}, value=0x{:x}", m_cpuId,
                static_cast<uint64_t>(vmcsField), value);
}

void X86_64CPU::VMWRITE(uint64_t field, uint64_t value) {
  if (!vmxEnabled || currentVMCS == 0) {
    spdlog::warn("X86_64CPU[{}]: VMWRITE executed without valid VMCS", m_cpuId);
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Check privilege level (must be CPL 0)
  if (GetCPL() != 0) {
    spdlog::warn("X86_64CPU[{}]: VMWRITE executed at CPL {}, requires CPL 0",
                 m_cpuId, GetCPL());
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Write field to VMCS
  VMCSField vmcsField = static_cast<VMCSField>(field);

  switch (vmcsField) {
  // Guest state fields
  case VMCSField::GUEST_RIP:
    vmcs.guest_rip = value;
    break;
  case VMCSField::GUEST_RSP:
    vmcs.guest_rsp = value;
    break;
  case VMCSField::GUEST_RFLAGS:
    vmcs.guest_rflags = value;
    break;
  case VMCSField::GUEST_CR0:
    vmcs.guest_cr0 = value;
    break;
  case VMCSField::GUEST_CR3:
    vmcs.guest_cr3 = value;
    break;
  case VMCSField::GUEST_CR4:
    vmcs.guest_cr4 = value;
    break;
  case VMCSField::GUEST_DR7:
    vmcs.guest_dr7 = value;
    break;
  case VMCSField::GUEST_ES_SELECTOR:
    vmcs.guest_es_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::GUEST_CS_SELECTOR:
    vmcs.guest_cs_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::GUEST_SS_SELECTOR:
    vmcs.guest_ss_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::GUEST_DS_SELECTOR:
    vmcs.guest_ds_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::GUEST_FS_SELECTOR:
    vmcs.guest_fs_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::GUEST_GS_SELECTOR:
    vmcs.guest_gs_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::GUEST_LDTR_SELECTOR:
    vmcs.guest_ldtr_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::GUEST_TR_SELECTOR:
    vmcs.guest_tr_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::GUEST_ES_BASE:
    vmcs.guest_es_base = value;
    break;
  case VMCSField::GUEST_CS_BASE:
    vmcs.guest_cs_base = value;
    break;
  case VMCSField::GUEST_SS_BASE:
    vmcs.guest_ss_base = value;
    break;
  case VMCSField::GUEST_DS_BASE:
    vmcs.guest_ds_base = value;
    break;
  case VMCSField::GUEST_FS_BASE:
    vmcs.guest_fs_base = value;
    break;
  case VMCSField::GUEST_GS_BASE:
    vmcs.guest_gs_base = value;
    break;
  case VMCSField::GUEST_LDTR_BASE:
    vmcs.guest_ldtr_base = value;
    break;
  case VMCSField::GUEST_TR_BASE:
    vmcs.guest_tr_base = value;
    break;
  case VMCSField::GUEST_GDTR_BASE:
    vmcs.guest_gdtr_base = value;
    break;
  case VMCSField::GUEST_IDTR_BASE:
    vmcs.guest_idtr_base = value;
    break;
  case VMCSField::GUEST_ES_LIMIT:
    vmcs.guest_es_limit = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_CS_LIMIT:
    vmcs.guest_cs_limit = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_SS_LIMIT:
    vmcs.guest_ss_limit = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_DS_LIMIT:
    vmcs.guest_ds_limit = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_FS_LIMIT:
    vmcs.guest_fs_limit = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_GS_LIMIT:
    vmcs.guest_gs_limit = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_LDTR_LIMIT:
    vmcs.guest_ldtr_limit = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_TR_LIMIT:
    vmcs.guest_tr_limit = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_GDTR_LIMIT:
    vmcs.guest_gdtr_limit = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_IDTR_LIMIT:
    vmcs.guest_idtr_limit = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_ES_AR_BYTES:
    vmcs.guest_es_access_rights = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_CS_AR_BYTES:
    vmcs.guest_cs_access_rights = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_SS_AR_BYTES:
    vmcs.guest_ss_access_rights = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_DS_AR_BYTES:
    vmcs.guest_ds_access_rights = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_FS_AR_BYTES:
    vmcs.guest_fs_access_rights = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_GS_AR_BYTES:
    vmcs.guest_gs_access_rights = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_LDTR_AR_BYTES:
    vmcs.guest_ldtr_access_rights = static_cast<uint32_t>(value);
    break;
  case VMCSField::GUEST_TR_AR_BYTES:
    vmcs.guest_tr_access_rights = static_cast<uint32_t>(value);
    break;

  // Host state fields
  case VMCSField::HOST_CR0:
    vmcs.host_cr0 = value;
    break;
  case VMCSField::HOST_CR3:
    vmcs.host_cr3 = value;
    break;
  case VMCSField::HOST_CR4:
    vmcs.host_cr4 = value;
    break;
  case VMCSField::HOST_RIP:
    vmcs.host_rip = value;
    break;
  case VMCSField::HOST_RSP:
    vmcs.host_rsp = value;
    break;
  case VMCSField::HOST_ES_SELECTOR:
    vmcs.host_es_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::HOST_CS_SELECTOR:
    vmcs.host_cs_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::HOST_SS_SELECTOR:
    vmcs.host_ss_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::HOST_DS_SELECTOR:
    vmcs.host_ds_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::HOST_FS_SELECTOR:
    vmcs.host_fs_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::HOST_GS_SELECTOR:
    vmcs.host_gs_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::HOST_TR_SELECTOR:
    vmcs.host_tr_selector = static_cast<uint16_t>(value);
    break;
  case VMCSField::HOST_FS_BASE:
    vmcs.host_fs_base = value;
    break;
  case VMCSField::HOST_GS_BASE:
    vmcs.host_gs_base = value;
    break;
  case VMCSField::HOST_TR_BASE:
    vmcs.host_tr_base = value;
    break;
  case VMCSField::HOST_GDTR_BASE:
    vmcs.host_gdtr_base = value;
    break;
  case VMCSField::HOST_IDTR_BASE:
    vmcs.host_idtr_base = value;
    break;

  // Control fields
  case VMCSField::PIN_BASED_VM_EXEC_CONTROL:
    vmcs.pin_based_vm_exec_control = static_cast<uint32_t>(value);
    break;
  case VMCSField::CPU_BASED_VM_EXEC_CONTROL:
    vmcs.cpu_based_vm_exec_control = static_cast<uint32_t>(value);
    break;
  case VMCSField::SECONDARY_VM_EXEC_CONTROL:
    vmcs.secondary_vm_exec_control = static_cast<uint32_t>(value);
    break;
  case VMCSField::EXCEPTION_BITMAP:
    vmcs.exception_bitmap = static_cast<uint32_t>(value);
    break;
  case VMCSField::VM_EXIT_CONTROLS:
    vmcs.vm_exit_controls = static_cast<uint32_t>(value);
    break;
  case VMCSField::VM_ENTRY_CONTROLS:
    vmcs.vm_entry_controls = static_cast<uint32_t>(value);
    break;
  case VMCSField::CR3_TARGET_COUNT:
    vmcs.cr3_target_count = static_cast<uint32_t>(value);
    break;
  case VMCSField::CR3_TARGET_VALUE0:
    vmcs.cr3_target_value0 = value;
    break;
  case VMCSField::CR3_TARGET_VALUE1:
    vmcs.cr3_target_value1 = value;
    break;
  case VMCSField::CR3_TARGET_VALUE2:
    vmcs.cr3_target_value2 = value;
    break;
  case VMCSField::CR3_TARGET_VALUE3:
    vmcs.cr3_target_value3 = value;
    break;

  // Read-only fields - should not be written to
  case VMCSField::VM_EXIT_REASON:
  case VMCSField::EXIT_QUALIFICATION:
  case VMCSField::GUEST_LINEAR_ADDRESS:
  case VMCSField::VM_EXIT_INTERRUPTION_INFO:
  case VMCSField::VM_EXIT_INTERRUPTION_ERROR_CODE:
  case VMCSField::VM_EXIT_INSTRUCTION_LEN:
  case VMCSField::VMX_INSTRUCTION_INFO:
    spdlog::warn("X86_64CPU[{}]: VMWRITE attempted on read-only field 0x{:x}",
                 m_cpuId, static_cast<uint64_t>(vmcsField));
    TriggerInterrupt(EXC_GP, 0, false);
    return;

  default:
    spdlog::warn("X86_64CPU[{}]: VMWRITE with unsupported field 0x{:x}",
                 m_cpuId, static_cast<uint64_t>(vmcsField));
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  spdlog::trace("X86_64CPU[{}]: VMWRITE field=0x{:x}, value=0x{:x}", m_cpuId,
                static_cast<uint64_t>(vmcsField), value);
}

void X86_64CPU::VMXOFF() {
  if (!vmxEnabled) {
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  vmxEnabled = false;
  inVMXNonRootOperation = false;
  currentVMCS = 0;
  vmxonRegion = 0;
  spdlog::info("X86_64CPU[{}]: VMXOFF executed", m_cpuId);
}

void X86_64CPU::VMPTRST(uint64_t addr) {
  if (!vmxEnabled) {
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Check privilege level (must be CPL 0)
  if (GetCPL() != 0) {
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Store current VMCS pointer to memory
  try {
    mmu.WriteVirtual(addr, &currentVMCS, 8, GetProcessId());
    spdlog::info(
        "X86_64CPU[{}]: VMPTRST executed, stored VMCS pointer 0x{:x} to 0x{:x}",
        m_cpuId, currentVMCS, addr);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: VMPTRST failed to write to memory: {}",
                  m_cpuId, e.what());
    TriggerInterrupt(EXC_GP, 0, false);
  }
}

void X86_64CPU::INVEPT(uint64_t type, uint64_t descriptor) {
  if (!vmxEnabled) {
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Check privilege level (must be CPL 0)
  if (GetCPL() != 0) {
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // INVEPT types according to Intel specification
  switch (type) {
  case 1: // Single-context invalidation
  {
    // Read EPT pointer from descriptor
    uint64_t ept_pointer;
    try {
      mmu.ReadVirtual(descriptor, &ept_pointer, 8, GetProcessId());
      // Invalidate EPT entries for specific EPT pointer
      // In a real implementation, this would invalidate TLB entries
      // associated with the specific EPT structure
      spdlog::info("X86_64CPU[{}]: INVEPT single-context, EPT pointer=0x{:x}",
                   m_cpuId, ept_pointer);
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: INVEPT failed to read descriptor: {}",
                    m_cpuId, e.what());
      TriggerInterrupt(EXC_GP, 0, false);
      return;
    }
  } break;
  case 2: // All-context invalidation
    // Invalidate all EPT-derived translations
    spdlog::info("X86_64CPU[{}]: INVEPT all-context invalidation", m_cpuId);
    break;
  default:
    // Invalid INVEPT type
    spdlog::warn("X86_64CPU[{}]: INVEPT with invalid type {}", m_cpuId, type);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Clear relevant TLB entries (simplified implementation)
  // In a real implementation, this would be more sophisticated
  // and would interact with the MMU's TLB management
}

void X86_64CPU::INVVPID(uint64_t type, uint64_t descriptor) {
  if (!vmxEnabled) {
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Check privilege level (must be CPL 0)
  if (GetCPL() != 0) {
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // INVVPID types according to Intel specification
  switch (type) {
  case 0: // Individual-address invalidation
  {
    struct {
      uint16_t vpid;
      uint16_t reserved1;
      uint32_t reserved2;
      uint64_t linear_address;
    } invvpid_desc;

    try {
      mmu.ReadVirtual(descriptor, &invvpid_desc, sizeof(invvpid_desc),
                      GetProcessId());
      spdlog::info(
          "X86_64CPU[{}]: INVVPID individual-address, VPID={}, addr=0x{:x}",
          m_cpuId, invvpid_desc.vpid, invvpid_desc.linear_address);
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: INVVPID failed to read descriptor: {}",
                    m_cpuId, e.what());
      TriggerInterrupt(EXC_GP, 0, false);
      return;
    }
  } break;
  case 1: // Single-context invalidation
  {
    uint16_t vpid;
    try {
      mmu.ReadVirtual(descriptor, &vpid, 2, GetProcessId());
      spdlog::info("X86_64CPU[{}]: INVVPID single-context, VPID={}", m_cpuId,
                   vpid);
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: INVVPID failed to read VPID: {}", m_cpuId,
                    e.what());
      TriggerInterrupt(EXC_GP, 0, false);
      return;
    }
  } break;
  case 2: // All-context invalidation
    spdlog::info("X86_64CPU[{}]: INVVPID all-context invalidation", m_cpuId);
    break;
  case 3: // Single-context-retaining-globals invalidation
  {
    uint16_t vpid;
    try {
      mmu.ReadVirtual(descriptor, &vpid, 2, GetProcessId());
      spdlog::info(
          "X86_64CPU[{}]: INVVPID single-context-retaining-globals, VPID={}",
          m_cpuId, vpid);
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: INVVPID failed to read VPID: {}", m_cpuId,
                    e.what());
      TriggerInterrupt(EXC_GP, 0, false);
      return;
    }
  } break;
  default:
    spdlog::warn("X86_64CPU[{}]: INVVPID with invalid type {}", m_cpuId, type);
    TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  // Clear relevant TLB entries (simplified implementation)
  // In a real implementation, this would interact with VPID-tagged TLB entries
}

// VM Exit handling
void X86_64CPU::VMExit(uint32_t reason, uint64_t qualification) {
  if (!inVMXNonRootOperation) {
    spdlog::error(
        "X86_64CPU[{}]: VMExit called but not in VMX non-root operation",
        m_cpuId);
    return;
  }

  spdlog::info(
      "X86_64CPU[{}]: VM Exit triggered, reason=0x{:x}, qualification=0x{:x}",
      m_cpuId, reason, qualification);

  // Save complete guest state
  SaveGuestState();

  // Set VM exit information fields with comprehensive details
  vmcs.vm_exit_reason = reason;
  vmcs.vm_exit_qualification = qualification;

  // Set additional exit information based on exit reason
  ProcessVMExitReason(reason, qualification);

  // Process VM exit controls
  ProcessVMExitControls();

  // Load host state and return to VMX root operation
  LoadHostState();

  // Clear VMX non-root operation flag
  inVMXNonRootOperation = false;

  spdlog::info("X86_64CPU[{}]: VM Exit completed, returned to VMX root "
               "operation at RIP=0x{:x}",
               m_cpuId, vmcs.host_rip);
}

void X86_64CPU::SaveGuestState() {
  // Save general purpose registers
  vmcs.guest_rax = GetRegister(Register::RAX);
  vmcs.guest_rbx = GetRegister(Register::RBX);
  vmcs.guest_rcx = GetRegister(Register::RCX);
  vmcs.guest_rdx = GetRegister(Register::RDX);
  vmcs.guest_rsi = GetRegister(Register::RSI);
  vmcs.guest_rdi = GetRegister(Register::RDI);
  vmcs.guest_rbp = GetRegister(Register::RBP);
  vmcs.guest_rsp = GetRegister(Register::RSP);
  vmcs.guest_r8 = GetRegister(Register::R8);
  vmcs.guest_r9 = GetRegister(Register::R9);
  vmcs.guest_r10 = GetRegister(Register::R10);
  vmcs.guest_r11 = GetRegister(Register::R11);
  vmcs.guest_r12 = GetRegister(Register::R12);
  vmcs.guest_r13 = GetRegister(Register::R13);
  vmcs.guest_r14 = GetRegister(Register::R14);
  vmcs.guest_r15 = GetRegister(Register::R15);
  vmcs.guest_rip = GetRegister(Register::RIP);
  vmcs.guest_rflags = GetRflags();

  // Save control registers
  vmcs.guest_cr0 = GetCR0();
  vmcs.guest_cr3 = GetCR3();
  vmcs.guest_cr4 = GetCR4();
  vmcs.guest_dr7 = GetDebugRegister(Register::DR7);

  // Save segment registers (simplified)
  vmcs.guest_cs_selector = GetCS();
  vmcs.guest_ds_selector = GetDS();
  vmcs.guest_es_selector = GetES();
  vmcs.guest_fs_selector = GetFS();
  vmcs.guest_gs_selector = GetGS();
  vmcs.guest_ss_selector = GetSS();
}

void X86_64CPU::LoadHostState() {
  // Load control registers
  SetCR0(vmcs.host_cr0);
  SetCR3(vmcs.host_cr3);
  SetCR4(vmcs.host_cr4);

  // Load segment registers (simplified)
  SetCS(vmcs.host_cs_selector);
  SetDS(vmcs.host_ds_selector);
  SetES(vmcs.host_es_selector);
  SetFS(vmcs.host_fs_selector);
  SetGS(vmcs.host_gs_selector);
  SetSS(vmcs.host_ss_selector);

  // Load RIP and RSP
  SetRegister(Register::RIP, vmcs.host_rip);
  SetRegister(Register::RSP, vmcs.host_rsp);
}

bool X86_64CPU::CheckVMExitConditions(const DecodedInstruction &instr) {
  if (!inVMXNonRootOperation) {
    return false;
  }

  // Check if instruction causes VM exit based on VMCS configuration
  switch (instr.instType) {
  case InstructionType::Cpuid:
    // CPUID always causes VM exit in VMX non-root operation
    VMExit(static_cast<uint32_t>(VMExitReason::CPUID), 0);
    return true;

  case InstructionType::Invd:
    // INVD always causes VM exit
    VMExit(static_cast<uint32_t>(VMExitReason::INVD), 0);
    return true;

  case InstructionType::Vmcall:
    // VMCALL always causes VM exit
    VMExit(static_cast<uint32_t>(VMExitReason::VMCALL), 0);
    return true;

  case InstructionType::Hlt:
    // Check if HLT exiting is enabled
    if (vmcs.cpu_based_vm_exec_control & (1 << 7)) {
      VMExit(static_cast<uint32_t>(VMExitReason::HLT), 0);
      return true;
    }
    break;

  case InstructionType::In:
  case InstructionType::Out:
  case InstructionType::Ins:
  case InstructionType::Outs:
    // Check if I/O instruction exiting is enabled
    if (vmcs.cpu_based_vm_exec_control & (1 << 24)) {
      uint64_t qualification = 0;
      // Set qualification based on instruction details
      VMExit(static_cast<uint32_t>(VMExitReason::IO_INSTRUCTION),
             qualification);
      return true;
    }
    break;

  case InstructionType::Rdmsr:
  case InstructionType::Wrmsr:
    // Check if MSR access causes VM exit
    if (vmcs.cpu_based_vm_exec_control & (1 << 28)) {
      uint32_t reason = (instr.instType == InstructionType::Rdmsr)
                            ? static_cast<uint32_t>(VMExitReason::RDMSR)
                            : static_cast<uint32_t>(VMExitReason::WRMSR);
      VMExit(reason, 0);
      return true;
    }
    break;

  default:
    break;
  }

  return false;
}

bool X86_64CPU::ValidateGuestState() {
  // Validate guest control registers
  uint64_t guest_cr0 = vmcs.guest_cr0;
  uint64_t guest_cr4 = vmcs.guest_cr4;

  // CR0 validation
  // PE and PG bits must be consistent
  bool pe = (guest_cr0 & 0x1) != 0;
  bool pg = (guest_cr0 & 0x80000000) != 0;

  if (pg && !pe) {
    spdlog::error("X86_64CPU[{}]: Invalid guest CR0: PG=1 but PE=0", m_cpuId);
    return false;
  }

  // CR4 validation
  // Check reserved bits (simplified)
  if (guest_cr4 & 0xFFFFF800) {
    spdlog::error("X86_64CPU[{}]: Invalid guest CR4: reserved bits set",
                  m_cpuId);
    return false;
  }

  // Validate guest segment registers
  if (!ValidateGuestSegmentRegisters()) {
    return false;
  }

  // Validate guest RIP
  if (vmcs.guest_rip == 0) {
    spdlog::error("X86_64CPU[{}]: Invalid guest RIP: cannot be zero", m_cpuId);
    return false;
  }

  // Validate guest RFLAGS
  uint64_t guest_rflags = vmcs.guest_rflags;
  // Bit 1 must always be 1, bits 3,5,15,22-31 must be 0
  if (!(guest_rflags & 0x2) || (guest_rflags & 0xFFC08028)) {
    spdlog::error("X86_64CPU[{}]: Invalid guest RFLAGS: 0x{:x}", m_cpuId,
                  guest_rflags);
    return false;
  }

  spdlog::debug("X86_64CPU[{}]: Guest state validation passed", m_cpuId);
  return true;
}

bool X86_64CPU::ValidateGuestSegmentRegisters() {
  // Validate CS register
  if (vmcs.guest_cs_selector == 0 && (vmcs.guest_cr0 & 0x1)) {
    spdlog::error(
        "X86_64CPU[{}]: Invalid guest CS: null selector in protected mode",
        m_cpuId);
    return false;
  }

  // Check segment limits and access rights (simplified validation)
  if (vmcs.guest_cs_limit == 0 && (vmcs.guest_cr0 & 0x1)) {
    spdlog::error(
        "X86_64CPU[{}]: Invalid guest CS: zero limit in protected mode",
        m_cpuId);
    return false;
  }

  // Validate other segment registers (simplified)
  // In real implementation, this would be much more comprehensive

  return true;
}

void X86_64CPU::SaveHostState() {
  // Save control registers
  vmcs.host_cr0 = GetCR0();
  vmcs.host_cr3 = GetCR3();
  vmcs.host_cr4 = GetCR4();

  // Save segment selectors
  vmcs.host_cs_selector = GetCS();
  vmcs.host_ds_selector = GetDS();
  vmcs.host_es_selector = GetES();
  vmcs.host_fs_selector = GetFS();
  vmcs.host_gs_selector = GetGS();
  vmcs.host_ss_selector = GetSS();
  vmcs.host_tr_selector = GetTR();

  // Save segment bases
  vmcs.host_fs_base = GetFSBase();
  vmcs.host_gs_base = GetGSBase();
  vmcs.host_tr_base = GetTRBase();
  vmcs.host_gdtr_base = GetGDTRBase();
  vmcs.host_idtr_base = GetIDTRBase();

  // Save RIP and RSP
  vmcs.host_rip = GetRegister(Register::RIP);
  vmcs.host_rsp = GetRegister(Register::RSP);

  spdlog::debug("X86_64CPU[{}]: Host state saved, RIP=0x{:x}, RSP=0x{:x}",
                m_cpuId, vmcs.host_rip, vmcs.host_rsp);
}

void X86_64CPU::LoadGuestState() {
  // Load control registers
  SetCR0(vmcs.guest_cr0);
  SetCR3(vmcs.guest_cr3);
  SetCR4(vmcs.guest_cr4);

  // Load general purpose registers
  SetRegister(Register::RAX, vmcs.guest_rax);
  SetRegister(Register::RBX, vmcs.guest_rbx);
  SetRegister(Register::RCX, vmcs.guest_rcx);
  SetRegister(Register::RDX, vmcs.guest_rdx);
  SetRegister(Register::RSI, vmcs.guest_rsi);
  SetRegister(Register::RDI, vmcs.guest_rdi);
  SetRegister(Register::RBP, vmcs.guest_rbp);
  SetRegister(Register::RSP, vmcs.guest_rsp);
  SetRegister(Register::R8, vmcs.guest_r8);
  SetRegister(Register::R9, vmcs.guest_r9);
  SetRegister(Register::R10, vmcs.guest_r10);
  SetRegister(Register::R11, vmcs.guest_r11);
  SetRegister(Register::R12, vmcs.guest_r12);
  SetRegister(Register::R13, vmcs.guest_r13);
  SetRegister(Register::R14, vmcs.guest_r14);
  SetRegister(Register::R15, vmcs.guest_r15);

  // Load segment registers
  SetCS(vmcs.guest_cs_selector);
  SetDS(vmcs.guest_ds_selector);
  SetES(vmcs.guest_es_selector);
  SetFS(vmcs.guest_fs_selector);
  SetGS(vmcs.guest_gs_selector);
  SetSS(vmcs.guest_ss_selector);

  // Load RIP and RFLAGS
  SetRegister(Register::RIP, vmcs.guest_rip);
  SetRflags(vmcs.guest_rflags);

  spdlog::debug("X86_64CPU[{}]: Guest state loaded, RIP=0x{:x}, RSP=0x{:x}",
                m_cpuId, vmcs.guest_rip, vmcs.guest_rsp);
}

bool X86_64CPU::ProcessVMEntryControls() {
  uint32_t entry_controls = vmcs.vm_entry_controls;

  // Process entry controls according to Intel specification

  // Check if we need to load debug controls
  if (entry_controls & (1 << 2)) {
    // Load debug controls from VMCS
    // In real implementation, this would load DR7 and IA32_DEBUGCTL
    spdlog::debug("X86_64CPU[{}]: Loading debug controls on VM entry", m_cpuId);
  }

  // Check if we need to load IA32_PERF_GLOBAL_CTRL
  if (entry_controls & (1 << 13)) {
    // Load performance global control
    spdlog::debug("X86_64CPU[{}]: Loading IA32_PERF_GLOBAL_CTRL on VM entry",
                  m_cpuId);
  }

  // Check if we need to load PAT
  if (entry_controls & (1 << 14)) {
    // Load Page Attribute Table
    spdlog::debug("X86_64CPU[{}]: Loading PAT on VM entry", m_cpuId);
  }

  // Check if we need to load EFER
  if (entry_controls & (1 << 15)) {
    // Load Extended Feature Enable Register
    spdlog::debug("X86_64CPU[{}]: Loading EFER on VM entry", m_cpuId);
  }

  // Process VM entry MSR load list if present
  // This is simplified - real implementation would load MSRs from memory

  return true;
}

void X86_64CPU::ProcessVMExitReason(uint32_t reason, uint64_t qualification) {
  // Set additional VMCS fields based on specific exit reasons
  VMExitReason exitReason = static_cast<VMExitReason>(reason);

  switch (exitReason) {
  case VMExitReason::EXCEPTION_OR_NMI: {
    // For exception/NMI exits, set interruption information
    uint32_t vector = static_cast<uint32_t>(qualification & 0xFF);
    uint32_t type = static_cast<uint32_t>((qualification >> 8) & 0x7);
    uint32_t error_code_valid =
        static_cast<uint32_t>((qualification >> 11) & 0x1);

    vmcs.vm_exit_intr_info =
        vector | (type << 8) | (error_code_valid << 11) | (1U << 31);
    if (error_code_valid) {
      vmcs.vm_exit_intr_error_code =
          static_cast<uint32_t>((qualification >> 32) & 0xFFFFFFFF);
    }

    spdlog::debug("X86_64CPU[{}]: Exception/NMI exit, vector={}, type={}",
                  m_cpuId, vector, type);
  } break;

  case VMExitReason::EXTERNAL_INTERRUPT: {
    uint32_t vector = static_cast<uint32_t>(qualification & 0xFF);
    vmcs.vm_exit_intr_info =
        vector | (0 << 8) | (1U << 31); // External interrupt type
    spdlog::debug("X86_64CPU[{}]: External interrupt exit, vector={}", m_cpuId,
                  vector);
  } break;

  case VMExitReason::TRIPLE_FAULT:
    spdlog::warn("X86_64CPU[{}]: Triple fault VM exit", m_cpuId);
    break;

  case VMExitReason::INIT_SIGNAL:
    spdlog::info("X86_64CPU[{}]: INIT signal VM exit", m_cpuId);
    break;

  case VMExitReason::SIPI: {
    uint32_t vector = static_cast<uint32_t>(qualification & 0xFF);
    spdlog::info("X86_64CPU[{}]: SIPI VM exit, vector={}", m_cpuId, vector);
  } break;

  case VMExitReason::IO_SMI:
  case VMExitReason::OTHER_SMI:
    spdlog::info("X86_64CPU[{}]: SMI VM exit", m_cpuId);
    break;

  case VMExitReason::INTERRUPT_WINDOW:
    spdlog::debug("X86_64CPU[{}]: Interrupt window VM exit", m_cpuId);
    break;

  case VMExitReason::NMI_WINDOW:
    spdlog::debug("X86_64CPU[{}]: NMI window VM exit", m_cpuId);
    break;

  case VMExitReason::TASK_SWITCH:
    spdlog::debug("X86_64CPU[{}]: Task switch VM exit", m_cpuId);
    break;

  case VMExitReason::CPUID:
    spdlog::debug("X86_64CPU[{}]: CPUID VM exit", m_cpuId);
    break;

  case VMExitReason::GETSEC:
    spdlog::debug("X86_64CPU[{}]: GETSEC VM exit", m_cpuId);
    break;

  case VMExitReason::HLT:
    spdlog::debug("X86_64CPU[{}]: HLT VM exit", m_cpuId);
    break;

  case VMExitReason::INVD:
    spdlog::debug("X86_64CPU[{}]: INVD VM exit", m_cpuId);
    break;

  case VMExitReason::INVLPG:
    vmcs.guest_linear_address = qualification;
    spdlog::debug("X86_64CPU[{}]: INVLPG VM exit, linear_addr=0x{:x}", m_cpuId,
                  qualification);
    break;

  case VMExitReason::RDPMC:
    spdlog::debug("X86_64CPU[{}]: RDPMC VM exit", m_cpuId);
    break;

  case VMExitReason::RDTSC:
    spdlog::debug("X86_64CPU[{}]: RDTSC VM exit", m_cpuId);
    break;

  case VMExitReason::RSM:
    spdlog::debug("X86_64CPU[{}]: RSM VM exit", m_cpuId);
    break;

  case VMExitReason::VMCALL:
    spdlog::debug("X86_64CPU[{}]: VMCALL VM exit", m_cpuId);
    break;

  case VMExitReason::VMCLEAR:
  case VMExitReason::VMLAUNCH:
  case VMExitReason::VMPTRLD:
  case VMExitReason::VMPTRST:
  case VMExitReason::VMREAD:
  case VMExitReason::VMRESUME:
  case VMExitReason::VMWRITE:
  case VMExitReason::VMXOFF:
  case VMExitReason::VMXON:
    spdlog::debug("X86_64CPU[{}]: VMX instruction VM exit, reason=0x{:x}",
                  m_cpuId, reason);
    break;

  case VMExitReason::CR_ACCESS: {
    uint32_t cr_num = static_cast<uint32_t>(qualification & 0xF);
    uint32_t access_type = static_cast<uint32_t>((qualification >> 4) & 0x3);
    spdlog::debug("X86_64CPU[{}]: CR{} access VM exit, type={}", m_cpuId,
                  cr_num, access_type);
  } break;

  case VMExitReason::DR_ACCESS: {
    uint32_t dr_num = static_cast<uint32_t>(qualification & 0x7);
    spdlog::debug("X86_64CPU[{}]: DR{} access VM exit", m_cpuId, dr_num);
  } break;

  case VMExitReason::IO_INSTRUCTION: {
    uint32_t port = static_cast<uint32_t>((qualification >> 16) & 0xFFFF);
    uint32_t size = static_cast<uint32_t>((qualification >> 0) & 0x7) + 1;
    uint32_t direction = static_cast<uint32_t>((qualification >> 3) & 0x1);
    spdlog::debug(
        "X86_64CPU[{}]: I/O instruction VM exit, port=0x{:x}, size={}, dir={}",
        m_cpuId, port, size, direction ? "IN" : "OUT");
  } break;

  case VMExitReason::RDMSR:
  case VMExitReason::WRMSR: {
    uint32_t msr =
        static_cast<uint32_t>(GetRegister(Register::RCX) & 0xFFFFFFFF);
    spdlog::debug("X86_64CPU[{}]: MSR access VM exit, MSR=0x{:x}, type={}",
                  m_cpuId, msr,
                  (exitReason == VMExitReason::RDMSR) ? "READ" : "write");
  } break;

  case VMExitReason::ENTRY_FAILURE_INVALID_GUEST_STATE:
    spdlog::error("X86_64CPU[{}]: VM entry failure - invalid guest state",
                  m_cpuId);
    break;

  case VMExitReason::ENTRY_FAILURE_MSR_LOADING:
    spdlog::error("X86_64CPU[{}]: VM entry failure - MSR loading", m_cpuId);
    break;

  case VMExitReason::MWAIT:
    spdlog::debug("X86_64CPU[{}]: MWAIT VM exit", m_cpuId);
    break;

  case VMExitReason::MONITOR_TRAP_FLAG:
    spdlog::debug("X86_64CPU[{}]: Monitor trap flag VM exit", m_cpuId);
    break;

  case VMExitReason::MONITOR:
    spdlog::debug("X86_64CPU[{}]: MONITOR VM exit", m_cpuId);
    break;

  case VMExitReason::PAUSE:
    spdlog::debug("X86_64CPU[{}]: PAUSE VM exit", m_cpuId);
    break;

  case VMExitReason::ENTRY_FAILURE_MACHINE_CHECK:
    spdlog::error("X86_64CPU[{}]: VM entry failure - machine check", m_cpuId);
    break;

  case VMExitReason::TPR_BELOW_THRESHOLD:
    spdlog::debug("X86_64CPU[{}]: TPR below threshold VM exit", m_cpuId);
    break;

  default:
    spdlog::warn("X86_64CPU[{}]: Unknown VM exit reason 0x{:x}", m_cpuId,
                 reason);
    break;
  }

  // Set instruction length for instruction-specific exits
  if (IsInstructionExit(exitReason)) {
    vmcs.vm_exit_instruction_len = GetLastInstructionLength();
  }
}

bool X86_64CPU::IsInstructionExit(VMExitReason reason) {
  switch (reason) {
  case VMExitReason::CPUID:
  case VMExitReason::GETSEC:
  case VMExitReason::HLT:
  case VMExitReason::INVD:
  case VMExitReason::INVLPG:
  case VMExitReason::RDPMC:
  case VMExitReason::RDTSC:
  case VMExitReason::RSM:
  case VMExitReason::VMCALL:
  case VMExitReason::VMCLEAR:
  case VMExitReason::VMLAUNCH:
  case VMExitReason::VMPTRLD:
  case VMExitReason::VMPTRST:
  case VMExitReason::VMREAD:
  case VMExitReason::VMRESUME:
  case VMExitReason::VMWRITE:
  case VMExitReason::VMXOFF:
  case VMExitReason::VMXON:
  case VMExitReason::CR_ACCESS:
  case VMExitReason::DR_ACCESS:
  case VMExitReason::IO_INSTRUCTION:
  case VMExitReason::RDMSR:
  case VMExitReason::WRMSR:
  case VMExitReason::MWAIT:
  case VMExitReason::MONITOR:
  case VMExitReason::PAUSE:
    return true;
  default:
    return false;
  }
}

void X86_64CPU::ProcessVMExitControls() {
  uint32_t exit_controls = vmcs.vm_exit_controls;

  // Process VM exit controls according to Intel specification

  // Check if we need to save debug controls
  if (exit_controls & (1 << 2)) {
    // Save debug controls to VMCS
    spdlog::debug("X86_64CPU[{}]: Saving debug controls on VM exit", m_cpuId);
  }

  // Check if we need to host address-space size (switch to 64-bit mode)
  if (exit_controls & (1 << 9)) {
    // Ensure host is in 64-bit mode
    spdlog::debug("X86_64CPU[{}]: Switching to 64-bit host mode on VM exit",
                  m_cpuId);
  }

  // Check if we need to load IA32_PERF_GLOBAL_CTRL
  if (exit_controls & (1 << 12)) {
    // Load performance global control
    spdlog::debug("X86_64CPU[{}]: Loading IA32_PERF_GLOBAL_CTRL on VM exit",
                  m_cpuId);
  }

  // Check if we need to acknowledge interrupt on exit
  if (exit_controls & (1 << 15)) {
    // Acknowledge external interrupt
    spdlog::debug("X86_64CPU[{}]: Acknowledging interrupt on VM exit", m_cpuId);
  }

  // Check if we need to save IA32_PAT
  if (exit_controls & (1 << 18)) {
    // Save Page Attribute Table
    spdlog::debug("X86_64CPU[{}]: Saving PAT on VM exit", m_cpuId);
  }

  // Check if we need to load IA32_PAT
  if (exit_controls & (1 << 19)) {
    // Load Page Attribute Table
    spdlog::debug("X86_64CPU[{}]: Loading PAT on VM exit", m_cpuId);
  }

  // Check if we need to save IA32_EFER
  if (exit_controls & (1 << 20)) {
    // Save Extended Feature Enable Register
    spdlog::debug("X86_64CPU[{}]: Saving EFER on VM exit", m_cpuId);
  }

  // Check if we need to load IA32_EFER
  if (exit_controls & (1 << 21)) {
    // Load Extended Feature Enable Register
    spdlog::debug("X86_64CPU[{}]: Loading EFER on VM exit", m_cpuId);
  }

  // Process VM exit MSR store/load lists if present
  // This is simplified - real implementation would store/load MSRs from memory
}

uint32_t X86_64CPU::GetLastInstructionLength() {
  // This is a simplified implementation
  // In a real implementation, this would track the actual instruction length
  return 1; // Default to 1 byte for now
}

// ========================
// Advanced Power Management
// ========================

void X86_64CPU::CheckMWAITWakeup() {
  if (!mwaitState)
    return;

  // Check if monitored address has changed
  uint64_t currentValue;
  mmu.ReadVirtual(mwaitMonitorAddr, &currentValue, 8, GetProcessId());

  if (currentValue != mwaitSavedValue) {
    mwaitState = false;
    halted = false;
    spdlog::info("X86_64CPU[{}]: MWAIT wakeup triggered", m_cpuId);
  } else if (mwaitWakeupCounter++ > MWAIT_MAX_CYCLES) {
    mwaitState = false;
    halted = false;
    spdlog::warn("X86_64CPU[{}]: MWAIT timeout", m_cpuId);
  }
}

// ========================
// Cache Control
// ========================

void X86_64CPU::CLFLUSH(uint64_t addr) {
  // In a real implementation, this would flush a specific cache line.
  // Here we can invalidate the JIT cache for that region as a substitute.
  jit->InvalidateRange(addr, 64); // Invalidate a 64-byte cache line
  spdlog::trace("X86_64CPU[{}]: CLFLUSH for address 0x{:x}", m_cpuId, addr);
}

void X86_64CPU::PREFETCH(DecodedInstruction::Operand::Type hint,
                         uint64_t addr) {
  // This is a hint to the CPU and may be a NOP in emulation.
  // In a sophisticated cache model, this would move data closer to the CPU.
  spdlog::trace("X86_64CPU[{}]: PREFETCH hint received for address 0x{:x}",
                m_cpuId, addr);
}

// XMM Register Access Methods
__m256i X86_64CPU::GetXMMRegister(uint8_t reg) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  if (reg >= XMM_REGISTER_COUNT) {
    spdlog::error("X86_64CPU[{}]: Invalid XMM register index: {}", m_cpuId,
                  reg);
    return _mm256_setzero_si256();
  }
  return xmmRegisters[reg];
}

void X86_64CPU::SetXMMRegister(uint8_t reg, const __m256i &value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  if (reg >= XMM_REGISTER_COUNT) {
    spdlog::error("X86_64CPU[{}]: Invalid XMM register index: {}", m_cpuId,
                  reg);
    return;
  }
  xmmRegisters[reg] = value;
  spdlog::trace("X86_64CPU[{}]: Set XMM{} register", m_cpuId, reg);
}

uint16_t X86_64CPU::GetMaskRegister(uint8_t reg) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  if (reg >= K_REGISTER_COUNT) {
    spdlog::error("X86_64CPU[{}]: Invalid mask register index: {}", m_cpuId,
                  reg);
    return 0;
  }
  return kRegisters[reg];
}

void X86_64CPU::SetMaskRegister(uint8_t reg, uint16_t value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  if (reg >= K_REGISTER_COUNT) {
    spdlog::error("X86_64CPU[{}]: Invalid mask register index: {}", m_cpuId,
                  reg);
    return;
  }
  // K0 is always 0xFFFF (all bits set)
  if (reg == 0) {
    kRegisters[0] = 0xFFFF;
    spdlog::trace("X86_64CPU[{}]: K0 register is read-only (always 0xFFFF)",
                  m_cpuId);
  } else {
    kRegisters[reg] = value;
    spdlog::trace("X86_64CPU[{}]: Set K{} register to 0x{:x}", m_cpuId, reg,
                  value);
  }
}

// AVX-512 Instruction Execution
void X86_64CPU::ExecuteAVX512Instruction(const DecodedInstruction &instr,
                                         uint64_t &nextRip) {
  if (!g_cpuFeatures.avx512f) {
    spdlog::warn("X86_64CPU[{}]: AVX-512 instruction attempted but not "
                 "supported by host CPU: type={}",
                 m_cpuId, static_cast<int>(instr.instType));
    TriggerInterrupt(EXC_UD, 0, false); // Undefined Opcode
    return;
  }

  // Ensure we have valid operands
  if (instr.operandCount < 1) {
    spdlog::error(
        "X86_64CPU[{}]: AVX-512 instruction with invalid operand count: {}",
        m_cpuId, instr.operandCount);
    TriggerInterrupt(EXC_UD, 0, false);
    return;
  }

  // Get mask register if used (k1-k7)
  uint8_t maskReg = 0;
  bool useMask = false;
  bool zeroMasking = false;

  if (instr.isEvex && instr.evexInfo.valid) {
    maskReg = instr.evexInfo.aaa();
    useMask = maskReg > 0;
    zeroMasking = instr.evexInfo.z();
  }

  // Handle different AVX-512 instruction types
  switch (instr.instType) {
  // Vector arithmetic operations
  case InstructionType::Vaddpd:
  case InstructionType::Vaddps:
  case InstructionType::Vaddsd:
  case InstructionType::Vaddss: {
    if (instr.operandCount < 3) {
      spdlog::error("X86_64CPU[{}]: AVX-512 VADD with invalid operand count",
                    m_cpuId);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    __m256i src1_256 = ReadXmmOperandValue(instr.operands[1]);
    __m256i src2_256 = ReadXmmOperandValue(instr.operands[2]);
    __m512 src1 = _mm512_castsi512_ps(_mm512_castsi256_si512(src1_256));
    __m512 src2 = _mm512_castsi512_ps(_mm512_castsi256_si512(src2_256));
    __m512 result;

    if (instr.instType == InstructionType::Vaddps) {
      result = _mm512_add_ps(src1, src2);
    } else if (instr.instType == InstructionType::Vaddpd) {
      result = _mm512_castpd_ps(
          _mm512_add_pd(_mm512_castps_pd(src1), _mm512_castps_pd(src2)));
    } else if (instr.instType == InstructionType::Vaddsd) {
      // Scalar double operation
      __m128d scalar_src1 = _mm_castps_pd(_mm512_castps512_ps128(src1));
      __m128d scalar_src2 = _mm_castps_pd(_mm512_castps512_ps128(src2));
      __m128d scalar_result = _mm_add_sd(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(_mm_castpd_ps(scalar_result));
    } else { // Vaddss
      // Scalar single operation
      __m128 scalar_src1 = _mm512_castps512_ps128(src1);
      __m128 scalar_src2 = _mm512_castps512_ps128(src2);
      __m128 scalar_result = _mm_add_ss(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(scalar_result);
    }

    // Apply masking if needed
    if (useMask) {
      uint16_t mask = GetMaskRegister(maskReg);
      __mmask16 k = static_cast<__mmask16>(mask);
      __m256i dest_256 = ReadXmmOperandValue(instr.operands[0]);
      __m512 dest = _mm512_castsi512_ps(_mm512_castsi256_si512(dest_256));

      if (zeroMasking) {
        result = _mm512_maskz_mov_ps(k, result);
      } else {
        result = _mm512_mask_mov_ps(dest, k, result);
      }
    }

    // Convert 512-bit result back to 256-bit for storage
    __m256 result256_ps = _mm512_castps512_ps256(result);
    __m256i result256 = _mm256_castps_si256(result256_ps);
    WriteXmmOperandValue(instr.operands[0], result256);
    break;
  }

  // Vector subtraction operations
  case InstructionType::Vsubpd:
  case InstructionType::Vsubps:
  case InstructionType::Vsubsd:
  case InstructionType::Vsubss: {
    if (instr.operandCount < 3) {
      spdlog::error("X86_64CPU[{}]: AVX-512 VSUB with invalid operand count",
                    m_cpuId);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    __m256i src1_256 = ReadXmmOperandValue(instr.operands[1]);
    __m256i src2_256 = ReadXmmOperandValue(instr.operands[2]);
    __m512 src1 = _mm512_castsi512_ps(_mm512_castsi256_si512(src1_256));
    __m512 src2 = _mm512_castsi512_ps(_mm512_castsi256_si512(src2_256));
    __m512 result;

    if (instr.instType == InstructionType::Vsubps) {
      result = _mm512_sub_ps(src1, src2);
    } else if (instr.instType == InstructionType::Vsubpd) {
      result = _mm512_castpd_ps(
          _mm512_sub_pd(_mm512_castps_pd(src1), _mm512_castps_pd(src2)));
    } else if (instr.instType == InstructionType::Vsubsd) {
      // Scalar double operation
      __m128d scalar_src1 = _mm_castps_pd(_mm512_castps512_ps128(src1));
      __m128d scalar_src2 = _mm_castps_pd(_mm512_castps512_ps128(src2));
      __m128d scalar_result = _mm_sub_sd(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(_mm_castpd_ps(scalar_result));
    } else { // Vsubss
      // Scalar single operation
      __m128 scalar_src1 = _mm512_castps512_ps128(src1);
      __m128 scalar_src2 = _mm512_castps512_ps128(src2);
      __m128 scalar_result = _mm_sub_ss(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(scalar_result);
    }

    // Apply masking if needed
    if (useMask) {
      uint16_t mask = GetMaskRegister(maskReg);
      __mmask16 k = static_cast<__mmask16>(mask);
      __m256i dest_256 = ReadXmmOperandValue(instr.operands[0]);
      __m512 dest = _mm512_castsi512_ps(_mm512_castsi256_si512(dest_256));

      if (zeroMasking) {
        result = _mm512_maskz_mov_ps(k, result);
      } else {
        result = _mm512_mask_mov_ps(dest, k, result);
      }
    }

    // Convert 512-bit result back to 256-bit for storage
    __m256 result256_ps = _mm512_castps512_ps256(result);
    __m256i result256 = _mm256_castps_si256(result256_ps);
    WriteXmmOperandValue(instr.operands[0], result256);
    break;
  }

  // Vector multiplication operations
  case InstructionType::Vmulpd:
  case InstructionType::Vmulps:
  case InstructionType::Vmulsd:
  case InstructionType::Vmulss: {
    if (instr.operandCount < 3) {
      spdlog::error("X86_64CPU[{}]: AVX-512 VMUL with invalid operand count",
                    m_cpuId);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    __m256i src1_256 = ReadXmmOperandValue(instr.operands[1]);
    __m256i src2_256 = ReadXmmOperandValue(instr.operands[2]);
    __m512 src1 = _mm512_castsi512_ps(_mm512_castsi256_si512(src1_256));
    __m512 src2 = _mm512_castsi512_ps(_mm512_castsi256_si512(src2_256));
    __m512 result;

    if (instr.instType == InstructionType::Vmulps) {
      result = _mm512_mul_ps(src1, src2);
    } else if (instr.instType == InstructionType::Vmulpd) {
      result = _mm512_castpd_ps(
          _mm512_mul_pd(_mm512_castps_pd(src1), _mm512_castps_pd(src2)));
    } else if (instr.instType == InstructionType::Vmulsd) {
      // Scalar double operation
      __m128d scalar_src1 = _mm_castps_pd(_mm512_castps512_ps128(src1));
      __m128d scalar_src2 = _mm_castps_pd(_mm512_castps512_ps128(src2));
      __m128d scalar_result = _mm_mul_sd(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(_mm_castpd_ps(scalar_result));
    } else { // Vmulss
      // Scalar single operation
      __m128 scalar_src1 = _mm512_castps512_ps128(src1);
      __m128 scalar_src2 = _mm512_castps512_ps128(src2);
      __m128 scalar_result = _mm_mul_ss(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(scalar_result);
    }

    // Apply masking if needed
    if (useMask) {
      uint16_t mask = GetMaskRegister(maskReg);
      __mmask16 k = static_cast<__mmask16>(mask);
      __m256i dest_256 = ReadXmmOperandValue(instr.operands[0]);
      __m512 dest = _mm512_castsi512_ps(_mm512_castsi256_si512(dest_256));

      if (zeroMasking) {
        result = _mm512_maskz_mov_ps(k, result);
      } else {
        result = _mm512_mask_mov_ps(dest, k, result);
      }
    }

    // Convert 512-bit result back to 256-bit for storage
    __m256 result256_ps = _mm512_castps512_ps256(result);
    __m256i result256 = _mm256_castps_si256(result256_ps);
    WriteXmmOperandValue(instr.operands[0], result256);
    break;
  }

  // Vector division operations
  case InstructionType::Vdivpd:
  case InstructionType::Vdivps:
  case InstructionType::Vdivsd:
  case InstructionType::Vdivss: {
    if (instr.operandCount < 3) {
      spdlog::error("X86_64CPU[{}]: AVX-512 VDIV with invalid operand count",
                    m_cpuId);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    __m256i src1_256 = ReadXmmOperandValue(instr.operands[1]);
    __m256i src2_256 = ReadXmmOperandValue(instr.operands[2]);
    __m512 src1 = _mm512_castsi512_ps(_mm512_castsi256_si512(src1_256));
    __m512 src2 = _mm512_castsi512_ps(_mm512_castsi256_si512(src2_256));
    __m512 result;

    if (instr.instType == InstructionType::Vdivps) {
      result = _mm512_div_ps(src1, src2);
    } else if (instr.instType == InstructionType::Vdivpd) {
      result = _mm512_castpd_ps(
          _mm512_div_pd(_mm512_castps_pd(src1), _mm512_castps_pd(src2)));
    } else if (instr.instType == InstructionType::Vdivsd) {
      // Scalar double operation
      __m128d scalar_src1 = _mm_castps_pd(_mm512_castps512_ps128(src1));
      __m128d scalar_src2 = _mm_castps_pd(_mm512_castps512_ps128(src2));
      __m128d scalar_result = _mm_div_sd(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(_mm_castpd_ps(scalar_result));
    } else { // Vdivss
      // Scalar single operation
      __m128 scalar_src1 = _mm512_castps512_ps128(src1);
      __m128 scalar_src2 = _mm512_castps512_ps128(src2);
      __m128 scalar_result = _mm_div_ss(scalar_src1, scalar_src2);
      result = _mm512_castps128_ps512(scalar_result);
    }

    // Apply masking if needed
    if (useMask) {
      uint16_t mask = GetMaskRegister(maskReg);
      __mmask16 k = static_cast<__mmask16>(mask);
      __m256i dest_256 = ReadXmmOperandValue(instr.operands[0]);
      __m512 dest = _mm512_castsi512_ps(_mm512_castsi256_si512(dest_256));

      if (zeroMasking) {
        result = _mm512_maskz_mov_ps(k, result);
      } else {
        result = _mm512_mask_mov_ps(dest, k, result);
      }
    }

    // Convert 512-bit result back to 256-bit for storage
    __m256 result256_ps = _mm512_castps512_ps256(result);
    __m256i result256 = _mm256_castps_si256(result256_ps);
    WriteXmmOperandValue(instr.operands[0], result256);
    break;
  }

  // Vector square root operations
  case InstructionType::Vsqrtpd:
  case InstructionType::Vsqrtps:
  case InstructionType::Vsqrtsd:
  case InstructionType::Vsqrtss: {
    if (instr.operandCount < 2) {
      spdlog::error("X86_64CPU[{}]: AVX-512 VSQRT with invalid operand count",
                    m_cpuId);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    __m256i src_256 = ReadXmmOperandValue(instr.operands[1]);
    __m512 src = _mm512_castsi512_ps(_mm512_castsi256_si512(src_256));
    __m512 result;

    if (instr.instType == InstructionType::Vsqrtps) {
      result = _mm512_sqrt_ps(src);
    } else if (instr.instType == InstructionType::Vsqrtpd) {
      result = _mm512_castpd_ps(_mm512_sqrt_pd(_mm512_castps_pd(src)));
    } else if (instr.instType == InstructionType::Vsqrtsd) {
      // Scalar double operation
      __m128d scalar_src = _mm_castps_pd(_mm512_castps512_ps128(src));
      __m128d scalar_result = _mm_sqrt_sd(scalar_src, scalar_src);
      result = _mm512_castps128_ps512(_mm_castpd_ps(scalar_result));
    } else { // Vsqrtss
      // Scalar single operation
      __m128 scalar_src = _mm512_castps512_ps128(src);
      __m128 scalar_result = _mm_sqrt_ss(scalar_src);
      result = _mm512_castps128_ps512(scalar_result);
    }

    // Apply masking if needed
    if (useMask) {
      uint16_t mask = GetMaskRegister(maskReg);
      __mmask16 k = static_cast<__mmask16>(mask);
      __m256i dest_256 = ReadXmmOperandValue(instr.operands[0]);
      __m512 dest = _mm512_castsi512_ps(_mm512_castsi256_si512(dest_256));

      if (zeroMasking) {
        result = _mm512_maskz_mov_ps(k, result);
      } else {
        result = _mm512_mask_mov_ps(dest, k, result);
      }
    }

    // Convert 512-bit result back to 256-bit for storage
    __m256 result256_ps = _mm512_castps512_ps256(result);
    __m256i result256 = _mm256_castps_si256(result256_ps);
    WriteXmmOperandValue(instr.operands[0], result256);
    break;
  }

  default:
    spdlog::warn("X86_64CPU[{}]: Unhandled AVX-512 instruction: {}", m_cpuId,
                 static_cast<int>(instr.instType));
    TriggerInterrupt(EXC_UD, 0, false);
    break;
  }

  // Update RIP
  nextRip = GetRegister(Register::RIP) + instr.length;
}

void X86_64CPU::SaveSMMState() {
  // Initialize SMM state save area
  smmSavedState = {};
  smmSavedState.smm_revision_id = SMM_REVISION_ID;
  smmSavedState.smbase = smbase;
  smmSavedState.smm_state_save_completion_flag = 0;

  // Save general purpose registers
  smmSavedState.rax = GetRegister(Register::RAX);
  smmSavedState.rcx = GetRegister(Register::RCX);
  smmSavedState.rdx = GetRegister(Register::RDX);
  smmSavedState.rbx = GetRegister(Register::RBX);
  smmSavedState.rsp = GetRegister(Register::RSP);
  smmSavedState.rbp = GetRegister(Register::RBP);
  smmSavedState.rsi = GetRegister(Register::RSI);
  smmSavedState.rdi = GetRegister(Register::RDI);
  smmSavedState.r8 = GetRegister(Register::R8);
  smmSavedState.r9 = GetRegister(Register::R9);
  smmSavedState.r10 = GetRegister(Register::R10);
  smmSavedState.r11 = GetRegister(Register::R11);
  smmSavedState.r12 = GetRegister(Register::R12);
  smmSavedState.r13 = GetRegister(Register::R13);
  smmSavedState.r14 = GetRegister(Register::R14);
  smmSavedState.r15 = GetRegister(Register::R15);

  // Save RIP and RFLAGS
  smmSavedState.rip = GetRegister(Register::RIP);
  smmSavedState.rflags = GetRflags();

  // Save control registers
  smmSavedState.cr0 = GetCR0();
  smmSavedState.cr3 = GetCR3();
  smmSavedState.cr4 = GetCR4();
  smmSavedState.dr6 = GetDebugRegister(Register::DR6);
  smmSavedState.dr7 = GetDebugRegister(Register::DR7);

  // Save segment registers (simplified - in real implementation would save
  // hidden parts too)
  smmSavedState.cs_selector = GetCS();
  smmSavedState.ds_selector = GetDS();
  smmSavedState.es_selector = GetES();
  smmSavedState.fs_selector = GetFS();
  smmSavedState.gs_selector = GetGS();
  smmSavedState.ss_selector = GetSS();

  // Save FPU state (simplified)
  memcpy(smmSavedState.fpu_state, &fpuState, sizeof(fpuState));

  // Save SMM statistics
  smmSavedState.smm_entry_count = smmEntryCount;
  smmSavedState.last_smm_entry_time =
      std::chrono::duration_cast<std::chrono::nanoseconds>(
          smmEntryTime.time_since_epoch())
          .count();

  // Set completion flag
  smmSavedState.smm_state_save_completion_flag = 1;

  spdlog::debug("X86_64CPU[{}]: Saved CPU state to SMM saved state area",
                m_cpuId);
}

void X86_64CPU::RestoreSMMState() {
  // Restore general purpose registers
  SetRegister(Register::RAX, smmSavedState.rax);
  SetRegister(Register::RCX, smmSavedState.rcx);
  SetRegister(Register::RDX, smmSavedState.rdx);
  SetRegister(Register::RBX, smmSavedState.rbx);
  SetRegister(Register::RSP, smmSavedState.rsp);
  SetRegister(Register::RBP, smmSavedState.rbp);
  SetRegister(Register::RSI, smmSavedState.rsi);
  SetRegister(Register::RDI, smmSavedState.rdi);
  SetRegister(Register::R8, smmSavedState.r8);
  SetRegister(Register::R9, smmSavedState.r9);
  SetRegister(Register::R10, smmSavedState.r10);
  SetRegister(Register::R11, smmSavedState.r11);
  SetRegister(Register::R12, smmSavedState.r12);
  SetRegister(Register::R13, smmSavedState.r13);
  SetRegister(Register::R14, smmSavedState.r14);
  SetRegister(Register::R15, smmSavedState.r15);

  // Restore RIP and RFLAGS
  SetRegister(Register::RIP, smmSavedState.rip);
  SetRflags(smmSavedState.rflags);

  // Restore control registers
  SetCR0(smmSavedState.cr0);
  SetCR3(smmSavedState.cr3);
  SetCR4(smmSavedState.cr4);
  SetDebugRegister(Register::DR6, smmSavedState.dr6);
  SetDebugRegister(Register::DR7, smmSavedState.dr7);

  // Restore segment registers
  SetCS(smmSavedState.cs_selector);
  SetDS(smmSavedState.ds_selector);
  SetES(smmSavedState.es_selector);
  SetFS(smmSavedState.fs_selector);
  SetGS(smmSavedState.gs_selector);
  SetSS(smmSavedState.ss_selector);

  // Restore FPU state (simplified)
  memcpy(&fpuState, smmSavedState.fpu_state, sizeof(fpuState));

  spdlog::debug("X86_64CPU[{}]: Restored CPU state from SMM saved state area",
                m_cpuId);
}

void X86_64CPU::WriteSMMStateToMemory() {
  // Calculate SMRAM state save area address
  uint64_t stateSaveArea = smbase + SMM_STATE_SAVE_OFFSET;

  // Validate SMRAM access
  if (!ValidateSMRAMAccess(stateSaveArea, sizeof(SMMSavedState), true)) {
    spdlog::error("X86_64CPU[{}]: Invalid SMRAM write access to 0x{:x}",
                  m_cpuId, stateSaveArea);
    return;
  }

  try {
    // Write SMM state to SMRAM with proper access controls
    WriteSMRAMData(stateSaveArea, &smmSavedState, sizeof(SMMSavedState));

    spdlog::debug(
        "X86_64CPU[{}]: Wrote SMM state to SMRAM at 0x{:x}, size={} bytes",
        m_cpuId, stateSaveArea, sizeof(SMMSavedState));
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Failed to write SMM state to SMRAM: {}",
                  m_cpuId, e.what());
    throw;
  }
}

void X86_64CPU::ReadSMMStateFromMemory() {
  // Calculate SMRAM state save area address
  uint64_t stateSaveArea = smbase + SMM_STATE_SAVE_OFFSET;

  // Validate SMRAM access
  if (!ValidateSMRAMAccess(stateSaveArea, sizeof(SMMSavedState), false)) {
    spdlog::error("X86_64CPU[{}]: Invalid SMRAM read access to 0x{:x}", m_cpuId,
                  stateSaveArea);
    return;
  }

  try {
    // Read SMM state from SMRAM with proper access controls
    ReadSMRAMData(stateSaveArea, &smmSavedState, sizeof(SMMSavedState));

    // Update SMM control flags based on what was read
    smmAutoHaltRestart = smmSavedState.auto_halt_restart != 0;
    smmIoRestart = smmSavedState.io_restart_flag != 0;

    spdlog::debug(
        "X86_64CPU[{}]: Read SMM state from SMRAM at 0x{:x}, size={} bytes",
        m_cpuId, stateSaveArea, sizeof(SMMSavedState));
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Failed to read SMM state from SMRAM: {}",
                  m_cpuId, e.what());
    throw;
  }
}

void X86_64CPU::SetSMBASE(uint64_t newBase) {
  if (inSMM) {
    // SMBASE can only be changed from within SMM
    smbase = newBase;
    smmSavedState.smbase = newBase;
    spdlog::info("X86_64CPU[{}]: SMBASE updated to 0x{:x}", m_cpuId, newBase);
  } else {
    spdlog::warn("X86_64CPU[{}]: Attempted to change SMBASE outside of SMM",
                 m_cpuId);
  }
}

uint64_t X86_64CPU::GetSMBASE() const { return smbase; }

bool X86_64CPU::IsSMMActive() const { return inSMM; }

void X86_64CPU::TriggerSMI(SMMInterruptType type) {
  spdlog::info("X86_64CPU[{}]: SMI triggered, type=0x{:x}", m_cpuId,
               static_cast<uint8_t>(type));
  EnterSMM(type);
}

// RSM (Resume from System Management Mode) instruction
void X86_64CPU::RSM() {
  if (!inSMM) {
    // RSM is only valid in SMM
    spdlog::warn("X86_64CPU[{}]: RSM instruction executed outside of SMM",
                 m_cpuId);
    TriggerInterrupt(EXC_UD, 0, false); // Undefined opcode exception
    return;
  }

  spdlog::info("X86_64CPU[{}]: RSM instruction executed, exiting SMM", m_cpuId);
  ExitSMM();
}

// SMRAM (System Management RAM) helper functions
bool X86_64CPU::ValidateSMRAMAccess(uint64_t address, size_t size, bool write) {
  // Check if address is within SMRAM bounds
  uint64_t smramStart = smbase;
  uint64_t smramEnd = smbase + SMM_MEMORY_SIZE;

  if (address < smramStart || (address + size) > smramEnd) {
    spdlog::warn("X86_64CPU[{}]: SMRAM access outside bounds: addr=0x{:x}, "
                 "size={}, SMRAM=[0x{:x}-0x{:x}]",
                 m_cpuId, address, size, smramStart, smramEnd);
    return false;
  }

  // Check if we're in SMM mode for SMRAM access
  if (!inSMM) {
    spdlog::warn("X86_64CPU[{}]: SMRAM access attempted outside SMM mode",
                 m_cpuId);
    return false;
  }

  // Check alignment requirements (simplified)
  if (address & 0x3) {
    spdlog::warn(
        "X86_64CPU[{}]: SMRAM access not properly aligned: addr=0x{:x}",
        m_cpuId, address);
    return false;
  }

  // Additional write-specific validation
  if (write) {
    // Check if trying to write to read-only areas (simplified)
    uint64_t readOnlyStart = smbase + 0x8000; // SMM handler area
    uint64_t readOnlyEnd = smbase + 0x9000;

    if (address >= readOnlyStart && address < readOnlyEnd) {
      spdlog::warn(
          "X86_64CPU[{}]: Attempted write to read-only SMRAM area: addr=0x{:x}",
          m_cpuId, address);
      return false;
    }
  }

  return true;
}

void X86_64CPU::WriteSMRAMData(uint64_t address, const void *data,
                               size_t size) {
  // Perform the actual SMRAM write with memory management
  try {
    // In a real implementation, this would handle SMRAM-specific memory mapping
    // For now, use the regular MMU with special SMRAM process ID or flags
    mmu.WriteVirtual(address, data, size, GetProcessId());

    spdlog::trace("X86_64CPU[{}]: SMRAM write completed: addr=0x{:x}, size={}",
                  m_cpuId, address, size);
  } catch (const std::exception &e) {
    spdlog::error(
        "X86_64CPU[{}]: SMRAM write failed: addr=0x{:x}, size={}, error={}",
        m_cpuId, address, size, e.what());
    throw;
  }
}

void X86_64CPU::ReadSMRAMData(uint64_t address, void *data, size_t size) {
  // Perform the actual SMRAM read with memory management
  try {
    // In a real implementation, this would handle SMRAM-specific memory mapping
    // For now, use the regular MMU with special SMRAM process ID or flags
    mmu.ReadVirtual(address, data, size, GetProcessId());

    spdlog::trace("X86_64CPU[{}]: SMRAM read completed: addr=0x{:x}, size={}",
                  m_cpuId, address, size);
  } catch (const std::exception &e) {
    spdlog::error(
        "X86_64CPU[{}]: SMRAM read failed: addr=0x{:x}, size={}, error={}",
        m_cpuId, address, size, e.what());
    throw;
  }
}

bool X86_64CPU::IsSMRAMAddress(uint64_t address) {
  uint64_t smramStart = smbase;
  uint64_t smramEnd = smbase + SMM_MEMORY_SIZE;
  return (address >= smramStart && address < smramEnd);
}

void X86_64CPU::InitializeSMRAM() {
  // Initialize SMRAM memory region
  if (smbase == 0) {
    smbase = SMM_DEFAULT_BASE; // Default SMBASE
    spdlog::info("X86_64CPU[{}]: Initialized SMBASE to default 0x{:x}", m_cpuId,
                 smbase);
  }

  // Clear SMRAM memory region (simplified)
  try {
    uint8_t zero_page[4096] = {0};
    for (uint64_t addr = smbase; addr < smbase + SMM_MEMORY_SIZE;
         addr += 4096) {
      size_t write_size =
          std::min(static_cast<size_t>(4096),
                   static_cast<size_t>(smbase + SMM_MEMORY_SIZE - addr));
      mmu.WriteVirtual(addr, zero_page, write_size, GetProcessId());
    }

    spdlog::debug(
        "X86_64CPU[{}]: SMRAM initialized and cleared: [0x{:x}-0x{:x}]",
        m_cpuId, smbase, smbase + SMM_MEMORY_SIZE);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Failed to initialize SMRAM: {}", m_cpuId,
                  e.what());
  }
}

} // namespace x86_64
